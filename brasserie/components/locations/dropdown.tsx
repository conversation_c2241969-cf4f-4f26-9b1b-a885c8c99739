import { useState } from "react";
import { useRouter } from "next/router";
import Select from "react-select";
import { Icon } from "@components/icon";
import { label } from "yet-another-react-lightbox";

interface Props {
    path?: string
    json?: any
}

export default function LocationsDropdown(props: Props) {
    const [value, setValue] = useState(()=>{
        return {
            value: null,
            label: null
        }
    })
    const router = useRouter()
    const json = props?.json || null
    let options = null

    // console.log(props?.json)

    if( json ) {
        options = json.map((item)=>({
            value: item.url,
            label: item.title
        }))
    }

    return(
        options &&
            <>
            <label htmlFor="locations-dropdown-input" className="screen-reader-text">Location</label>
            <Select
                placeholder={'Please select'}
                isSearchable={false}
                options={options}
                onChange={(option)=>{router.push(option.value)}}
                value={value.value ? value : null}
                className="react-select-container"
                classNamePrefix="react-select"
                inputId="locations-dropdown-input"
                components={{
                    DropdownIndicator: () => (
                        <span className="dropdown-indicator">
                            <Icon name={`dropdown-arrow`} className='' />
                        </span>
                    )
                }}
            />
            </>

    )
}

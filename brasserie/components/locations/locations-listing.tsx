
// CSS: /styles/blocks/locations.scss
import { useEffect, useRef, useState } from "react"
import AnimatedSection from "../animated-section"
import { ButtonGroup, Dropdown, Form } from "react-bootstrap"
import { Icon } from "../icon"
import Image from "next/image"
import { useRouter } from "next/router"
import Link from "next/link"

// const Map = dynamic(() => import("../blocks/map"), { ssr:false })

export default function LocationsListing(props) {
    const cssClass = props?.className || '',
    options = props?.options || false,
    layout = props?.options?.layout || 'default',
    regions = props?.options?.regions || false,
    features = props?.options?.features || false,
    pd_features = props?.options?.pd_features || false,
    roomFeatures = props?.options?.room_features || false,
    router = useRouter(),
    [View, setView] = useState('search'), // [map, search]
    [filters, setFilters] = useState(()=>{
        return {
            regions: [],
            features: [],
            pd_features: [],
            roomFeatures: [],
            seated: 0,
            standing: 0
        }
    }),
    [showFilters, setShowFilters] = useState(false),
    [showViewSwitcher, setShowViewSwitcher] = useState(false)

    let [markers, setMarkers] = useState(props?.markers || null)

    if(!markers) return

    // === Filter locations by layout type
    if( layout == 'rooms' && markers ) {
        // - remove locations without rooms enabled
        markers = markers.filter((item)=>{
            return item.rooms_enabled
        })
        // - filter out Regions

    }else if( layout == 'areas' && markers ) {
        markers = markers.filter((item) => {
            return item.bookable_areas
        })
    }

    // console.log(options, markers)

    const hangleRegionChange = (target)=> {
        const {value, checked} = target
        let tempArray = filters.regions || []

        // console.log(value, checked, filters)
        if( checked ) {
            tempArray.push(value)
        }else {
            tempArray.splice(tempArray.indexOf(value), 1)
        }
        setFilters({...filters, regions: tempArray})
        // console.log(tempArray)
        filterMarkers()
    }

    const hangleFeatureChange = (target)=> {
        const {value, checked} = target
        let tempArray = filters.features || []

        // console.log(value, checked, filters)
        if( checked ) {
            tempArray.push(value)
        }else {
            tempArray.splice(tempArray.indexOf(value), 1)
        }
        setFilters({...filters, features: tempArray})
        // console.log(tempArray)
        filterMarkers()
    }

    const handlePdFeatureChange = (target)=> {
        const {value, checked} = target
        let tempArray = filters.pd_features || []

        // console.log(value, checked, filters)
        if( checked ) {
            tempArray.push(value)
        }else {
            tempArray.splice(tempArray.indexOf(value), 1)
        }
        setFilters({...filters, pd_features: tempArray})
        // console.log(tempArray)
        filterMarkers()
    }

    const hangleRoomFeatureChange = (target)=> {
        const {value, checked} = target
        let tempArray = filters.roomFeatures || []

        // console.log(value, checked, filters)
        if( checked ) {
            tempArray.push(value)
        }else {
            tempArray.splice(tempArray.indexOf(value), 1)
        }
        setFilters({...filters, roomFeatures: tempArray})
        // console.log(tempArray)
        filterMarkers()
    }

    const handleSeatedChange = (target) => {
        const {value} = target
        console.log(value)
        setFilters({...filters, seated: value, standing: 0})
        filterMarkers()
    }

    const handleStandingChange = (target) => {
        const {value} = target
        console.log(value)
        setFilters({...filters, standing: value, seated: 0})
        filterMarkers()
    }

    function checkRegion(regions) {
        let found = 0
        let parts = regions.toString().split(",").map(function(item){
            return item.trim()
        })
        if(parts) {
            parts.forEach(function(item){
                if( filters.regions.indexOf(item) != -1 ) found++
            })
        }
        return found ? true : false
    }

    function checkFeatured(arr) {
        if( !filters.features.length ) return true
        if( !arr ) return false
        const result = arr.filter((item)=>{
            if( filters.features.indexOf(item.label) != -1 ) return true
        })
        // console.log(result.length, result)
        return result.length == filters.features.length ? true : false
    }

    function checkPdFeatured(arr) {
        if( !filters.pd_features.length ) return true
        if( !arr ) return false
        const result = arr.filter((item)=>{
            if( filters.pd_features.indexOf(item.label) != -1 ) return true
        })
        // console.log(result.length, result)
        return result.length == filters.pd_features.length ? true : false
    }

    function checkCapacity(seated, standing) {
        if( !(filters.seated || filters.standing) ) return true
        return seated >= filters.seated && standing >= filters.standing
    }

    function checkRoomFeatured(arr) {
        if( !filters.roomFeatures.length ) return true
        if( !arr ) return false
        const result = arr.filter((item)=>{
            if( filters.roomFeatures.indexOf(item.label) != -1 ) return true
        })
        // console.log(result.length, result)
        return result.length == filters.roomFeatures.length ? true : false
    }

    function isFeatureSelected(item) {
        return filters.features.indexOf(item) != -1
    }

    function isPdFeatureSelected(item) {
        return filters.pd_features.indexOf(item) != -1
    }

    function isRoomFeatureSelected(item) {
        return filters.roomFeatures.indexOf(item) != -1
    }

    function filterMarkers() {
        let filtered = props.markers.filter((item)=>{
            // --- filter Bookable Areas (Features, Capacity)
            if(layout == 'areas') {
                let filteredAreas = item?.bookable_areas?.filter((area)=>{
                    const hasAreas = !filters.pd_features.length || checkPdFeatured(area.features)
                    const hasCapacity = checkCapacity(area.seated, area.standing)
                    return hasAreas && hasCapacity
                })
                if( !filteredAreas?.length ) return false
            }
            // --- main filter
            return (!filters.regions.length || checkRegion(item.region)) && checkFeatured(item.feature) && checkRoomFeatured(item.room_feature)
        })
        setMarkers(filtered)
    }

    // ========== Find Restaurant: Browser Geolocation API ==========
    const searchErrorEl = useRef(null),
          searchInputEl = useRef(null),
          searchLocationBtn = useRef(null),
          scrollToEl = useRef(null),
          [postcode, setPostcode] = useState(''),
          [isGeoSearch, setGeoSearch] = useState(false),
          [isSearchResult, setIsSearchResult] = useState(false),
          restApi = process.env.NEXT_PUBLIC_WORDPRESS_REST_API,
          restSecret = process.env.NEXT_PUBLIC_WORDPRESS_PREVIEW_SECRET,
          restApiendpoint = '/locations/v1/search'

    function GEOcheckSupport() {
      if( navigator.geolocation ) {
        // --- Shor button
        // searchLocationBtn.current.closest('p').style.display = "block";
      }else{
        console.log('Browser does not support Geolocation API');
      }
    }

    function GEOgetLocation() {
      if (navigator.geolocation) {
        // --- run geolocation search
        navigator.geolocation.getCurrentPosition(GEOshowPosition, GEOshowError);
      }
    }

    function GEOshowPosition(position) {
        // - Enable the button back again
        searchLocationBtn.current.disabled = false;
        // --- Display location in search input
        setGeoSearch(true)
        console.log(position.coords.latitude + ',' + position.coords.longitude)
        setPostcode( position.coords.latitude + ',' + position.coords.longitude )
    }

    function GEOshowError(error) {
        switch(error.code) {
            case error.PERMISSION_DENIED:
                searchErrorEl.current.innerHTML = 'Please allow geolocation so we can track your location in order to provide you with directions. Your location is not going to be used for any other purposes.'
                console.log('User denied the request for Geolocation.')
                break;
            case error.POSITION_UNAVAILABLE:
                console.log('Location information is unavailable.')
                break;
            case error.TIMEOUT:
                console.log('The request to get user location timed out.')
                break;
            case error.UNKNOWN_ERROR:
                console.log('An unknown error occurred.')
                break;
        }
    }

    const handlePostcodeChange = (event)=>{
        setGeoSearch(false)
        setPostcode(event.target.value)
        console.log('handlePostcodeChange fired: ', event.target.value)
    }

    const handlePostcodeFind = () => {
        console.log('handlePostcodeFind fired')
        formSubmitHandler()
    }

    const resetSearchView = ()=>{
        setView('search')
        // setShowFilters(true)
        // setFilters({
        //     regions: [],
        //     features: [],
        //     pd_features: [],
        //     roomFeatures: []
        // })
        setIsSearchResult(false)
        // setMarkers(props?.markers)
        setPostcode('')
    }

    const resetMapView = ()=>{
        setView('map')
        // setMarkers(props?.markers)
    }

    const  searchBtnHandler = ()=>{
        if( !searchLocationBtn.current.disabled ) {
            // --- Disable button
            searchLocationBtn.current.disabled = true
            // --- Get geolocation data
            GEOgetLocation()
        }
    }

    const formSubmitHandler = ()=>{
        console.log('onSubmit fired', postcode)
        searchErrorEl.current.innerHTML = ''
        var validation = postcode != '' ? true : false
        if ( validation ){
            searchInputEl.current.classList.remove('error')
            console.log('lets find some locations...')
            // === reset filters
            setShowFilters(false)
            setFilters({
                regions: [],
                features: [],
                pd_features: [],
                roomFeatures: [],
                seated: 0,
                standing: 0
            })

            //=== fetch Locations
            searchLocations()
        }else {
            searchInputEl.current.classList.add('error')
        }
    }

    const searchLocations = async function() {
        const data = new URLSearchParams()
        data.append('destination', postcode)
        data.append('api_secret', restSecret) // add secret to auth api call

        let body = document.querySelector('body')
        body.classList.add('loading')
        setMarkers(null)

        let response = await fetch(restApi+restApiendpoint, {
            method: 'POST',
            body: data
        })
        let json = await response.json()
        // console.log(json)

        if( json.code == 200 ){
            console.log('REST api call: success')
            searchErrorEl.current.innerHTML = ''
            setIsSearchResult(true)
            setMarkers(json.markers)
            body.classList.remove('loading')
            setTimeout(function(){
                scrollToEl.current.scrollIntoView({ behavior: 'smooth' })
            }, 500)
        }else{
            console.log('REST api call: error')
            console.log(json.code, json.message);
            searchErrorEl.current.innerHTML = json.message
            setIsSearchResult(false)
            setMarkers(props?.markers)
            body.classList.remove('loading')
        }
    }

    const LocationButtons = (marker,index)=> {
        return(
            <ButtonGroup className="text-center d-flex flex-column align-items-center">
                {marker.comingsoon ? (
                    <p className={`btn bg-green text-warm cursor-default mt-10 mb-0`} >Coming soon</p>
                ) : (
                    layout=='default' && <Link href={marker.bookTable} className={`btn ${index==0 && isSearchResult ? 'btn-secondary' : 'btn-primary'} mt-10`} >Book a Table</Link>
                )}
                {(marker?.rooms_enabled && !marker?.comingsoon) && <a href={marker.bookStay} className="btn btn-primary mt-10" >View available rooms</a>}
                {(marker.comingsoon && marker?.custom_button?.label && marker?.custom_button?.link) ? (
                    <a href={marker.custom_button.link}
                    className={`btn ${index==0 && isSearchResult ? 'btn-primary' : 'btn-primary'} mt-10`}
                    target={marker.custom_button.newtab ? `_blank` : `_self`}>{marker.custom_button.label}</a>
                ) : (
                    <>
                    {/* marker.directions && <a href={marker.directions} className={`btn ${index==0 && isSearchResult ? 'btn-primary' : 'btn-primary'} mt-10`} target="_blank">Directions</a> */}
                    </>
                )}
                <Link href={marker.website}
                    className={`btn ${index==0 && isSearchResult ? 'btn-primary' : 'btn-primary'} mt-10 mb-10`}>Visit location</Link>
            </ButtonGroup>
        )
    }

    useEffect(()=>{
        GEOcheckSupport()
        return ()=>{}
    },[])

    useEffect(()=>{
        if( postcode && isGeoSearch ) {
            console.log('GEO searching started')
            formSubmitHandler()
        }
    },[postcode])

return(
    <div className={cssClass}>
        <div className="row mt-50">
            {!!showViewSwitcher && (
                <>
                <div className="col-12 col-lg-4 offset-lg-1 text-center text-lg-start">&nbsp;</div>
                <div className="col-12 col-lg-5 offset-lg-1 text-center text-lg-start">
                    <p className="view-filters text-center">
                        <button className={`btn btn-primary m-10${View=='search'?' active':''}`} onClick={resetSearchView}>Locations</button>
                        <button className={`btn btn-primary m-10${View=='map'?' active':''}`} onClick={resetMapView}>Map</button>
                    </p>

                    {/*
                    <div className={`find-restaurant-inner px-3 py-0 py-lg-4 mt-2 mt-lg-0${View=='search'?'':' d-none'}`}>
                        <p className="mb-10">Enter an area or postcode</p>
                            <input type="hidden" name="action" value="find-restaurant" />

                            <div className="input-group mb-20">
                                <input ref={searchInputEl} type="text" className="form-control" name="postcode"
                                    value={postcode}
                                    onChange={handlePostcodeChange}
                                    aria-label="Type a postcode or a full address" required />
                                <button className="btn btn-primary btn-narrow" type="button" onClick={handlePostcodeFind}>Find</button>
                                </div>

                            <p style={{display: "none"}}>
                            <strong className="me-20">OR</strong> <button ref={searchLocationBtn} className="btn btn-primary" onClick={searchBtnHandler}>Use location</button>
                            </p>

                            <p ref={searchErrorEl} className="errorsMessage mb-0"></p>
                            <p>Postcode: {postcode}</p>

                    </div>
                    */}

                </div>
                </>
            )}
            {/* === Filters === */}
            {!!showFilters && (
                <div className="col-12 col-lg-10 offset-lg-1 mb-20 ">
                    {regions.length > 0 && (
                        <div>
                            <h4 className="text-uppercase mt-0">Regions</h4>
                            <ul className="list-unstyled">
                            {regions.map((item,i)=>(
                                <li key={`locations-region-index-${i}`} className="list-inline-item">
                                    <input type="checkbox" id={`locations-region-index-${i}`} className="form-check-input" value={item}
                                    onChange={(e)=>hangleRegionChange(e.target)}
                                    checked={checkRegion(item)} />
                                    <label className="text-left px-10" htmlFor={`locations-region-index-${i}`}> {item} </label>
                                </li>
                            ))}
                            </ul>
                        </div>
                    )}
                    {(layout == 'default' && features.length > 0) && (
                        <div>
                            <h4 className="text-uppercase">Features</h4>
                            <ul className="list-unstyled">
                            {features.map((item,i)=>(
                                <li key={`locations-feature-index-${i}`} className="list-inline-item">
                                    <input type="checkbox" id={`locations-feature-index-${i}`} className="form-check-input" value={item}
                                    onChange={(e)=>hangleFeatureChange(e.target)}
                                    checked={isFeatureSelected(item) && filters.features.length ? true : false} />
                                    <label className="text-left px-10" htmlFor={`locations-feature-index-${i}`}> {item} </label>
                                </li>
                            ))}
                            </ul>
                        </div>
                    )}
                    {layout == 'areas' && (
                        <div>
                            <h4 className="text-uppercase">Features</h4>
                            {features.length > 1 &&
                                <ul className="list-unstyled">
                                    <li className="list-inline-item"><strong>Location</strong></li>
                                {features.map((item,i)=>(
                                    <li key={`locations-feature-index-${i}`} className="list-inline-item">
                                        <input type="checkbox" id={`locations-feature-index-${i}`} className="form-check-input" value={item}
                                        onChange={(e)=>hangleFeatureChange(e.target)}
                                        checked={isFeatureSelected(item) && filters.features.length ? true : false} />
                                        <label className="text-left px-10" htmlFor={`locations-feature-index-${i}`}> {item} </label>
                                    </li>
                                ))}
                                </ul>
                            }
                            {pd_features.length  >1 &&
                                <ul className="list-unstyled">
                                    <li className="list-inline-item"><strong>Area</strong></li>
                                {pd_features.map((item,i)=>(
                                    <li key={`locations-pd_feature-index-${i}`} className="list-inline-item">
                                        <input type="checkbox" id={`locations-pd_feature-index-${i}`} className="form-check-input" value={item}
                                        onChange={(e)=>handlePdFeatureChange(e.target)}
                                        checked={isPdFeatureSelected(item) && filters.pd_features.length ? true : false} />
                                        <label className="text-left px-10" htmlFor={`locations-pd_feature-index-${i}`}> {item} </label>
                                    </li>
                                ))}
                                </ul>
                            }
                            <h4 className="text-uppercase">Capacity</h4>
                            <ul className="list-unstyled">
                                <li className="list-inline-item"><strong>Seated</strong></li>
                                <li className="list-inline-item"><input type="number" id="locations-capacity-seated" className="form-control input-capacity me-20"
                                                                    value={filters.seated}
                                                                    min={0}
                                                                    onChange={(e)=>handleSeatedChange(e.target)} /></li>
                                <li className="list-inline-item"><strong>Standing</strong></li>
                                <li className="list-inline-item"><input type="number" id="locations-capacity-seated" className="form-control input-capacity"
                                                                    value={filters.standing}
                                                                    min={0}
                                                                    onChange={(e)=>handleStandingChange(e.target)} /></li>
                            </ul>
                        </div>
                    )}
                    {/* {(layout=='rooms' && roomFeatures.length > 0) && (
                        <div>
                            <h4 className="text-uppercase">Room Features</h4>
                            <ul className="list-unstyled">
                            {roomFeatures.map((item,i)=>(
                                <li key={`locations-roomfeature-index-${i}`} className="list-inline-item">
                                    <input type="checkbox" id={`locations-roomfeature-index-${i}`} className="form-check-input" value={item}
                                    onChange={(e)=>hangleRoomFeatureChange(e.target)}
                                    checked={isRoomFeatureSelected(item) && filters.roomFeatures.length} />
                                    <label className="text-left px-10" htmlFor={`locations-roomfeature-index-${i}`}> {item} </label>
                                </li>
                            ))}
                            </ul>
                        </div>
                    )} */}
                </div>
            )}
        </div>
        <AnimatedSection>
            <div ref={scrollToEl} className={`row pt-0${View == 'search' ? '': ' d-none'} layout-${layout}`}>
                {/* === Locations grid === */}
                { layout !== 'areas' && markers && markers.map((marker, index) =>
                    (
                    <div key={`locations-index-${index}`}
                        className={`text-center pb-50 d-flex flex-column justify-content-start col-md-6 col-lg-4`} >
                            {marker?.image && <Link href={marker.website} className="location-img ratio ratio-1x1 mb-20"><Image src={marker?.image[0] || `/images/img-placeholder.jpeg`} width={350} height={350} className="thumb img-fluid w-100" alt={marker.name} /></Link>}
                            {marker.wordmark ? (
                                <Icon name={`wordmark-${marker.wordmark}`} className='wordmark mx-auto mb-15' />
                            ) : (
                                <h3 className="h4 mt-10 text-center">{marker.name}</h3>
                            )}
                            {marker.dist && <p className="dist">{marker.dist}</p>}
                            <p className="text-center" dangerouslySetInnerHTML={{ __html: marker.address }}></p>
                            <p className="mt-auto text-center">
                                <a href={`tel:${marker.tel}`}>{marker.tel}</a><br />
                                <a href={`mailto:${marker.email}`}>{marker.email}</a>
                            </p>
                            {LocationButtons(marker, index)}
                            <hr className="show mt-50" />
                    </div>
                ) )}
                {/* === Bookable Areas grid === */}
                { layout === 'areas' && markers && markers.map((marker, index) =>
                    (
                    <div key={`locations-index-${index}`} className="col-12">

                        <div className="location-heading mb-30">
                            <hr />

                                <div className="row">
                                    <div className="col-12 col-lg-10 mx-auto">
                                        <div className="d-flex justify-content-center justify-content-lg-between align-items-center flex-column flex-lg-row my-20 my-lg-10 column-gap-md-10 column-gap-xl-20">
                                            {marker.wordmark ? (
                                                <Icon name={`wordmark-${marker.wordmark}`} className='wordmark' />
                                            ) : (
                                                <h3 className="h4 text-center">{marker.name}</h3>
                                            )}
                                            <p className="my-20 my-lg-0" dangerouslySetInnerHTML={{ __html: marker.address_short }}></p>
                                            <Link href={`${marker.website}`} className="btn btn-primary" >Visit location</Link>

                                        </div>
                                    </div>
                                </div>

                            <hr />
                        </div>
                        <div className="location-areas row">
                        {marker.bookable_areas && marker.bookable_areas.map((item, i) => (
                            <div  className={`text-center pb-50 d-flex flex-column justify-content-start col-md-6 col-lg-4 ${checkPdFeatured(item.features) && checkCapacity(item.seated, item.standing) ? '' : 'd-none'}`} >
                                <div className="location-img ratio ratio-1x1 mb-20">
                                    {item?.image && <Image src={item?.image[0] || `/images/img-placeholder.jpeg`} width={350} height={350} className="thumb img-fluid w-100" alt={item.title} />}
                                </div>
                                <h4 className="mt-10">{item.title}</h4>
                                <p className="mt-auto">
                                    <strong>Capacity</strong><br/>
                                    {item.seated && <span>{item.seated} seated<br/></span>}
                                    {item.standing && <span>{item.standing} standing</span>}
                                </p>
                                <Dropdown className={`mt-10`} drop="down-centered">
                                    <Dropdown.Toggle variant={`outline-secondary`} className={`px-10 text-center`}>Explore <Icon name="arrow-down" /></Dropdown.Toggle>
                                    <Dropdown.Menu renderOnMount={true} as="ul" className="guestline-dropdown-menu ps-0">
                                    <li ><a href={`${marker.website}meetings/#${item.slug}`} className="dropdown-item" target="_blank">Meetings</a></li>
                                    <li ><a href={`${marker.website}get-togethers/#${item.slug}`} className="dropdown-item" target="_blank">Get togethers</a></li>
                                    </Dropdown.Menu>
                                </Dropdown>
                                {/* <hr className="show mt-50" /> */}
                            </div>
                        ))}
                        </div>

                    </div>
                ) )}
            </div>
            {/* <div className={`${View == 'map' ? '' : 'visually-hidden'} edge-2-edge`}>
                <Map markers={markers} options={options} />
            </div> */}
        </AnimatedSection>
    </div>
)
}
import { Icon } from "@components/icon";
import OpeningTimes from "@components/opening-times";
import { useState, useEffect } from "react";
import Select from "react-select";
import { motion, AnimatePresence } from "motion/react";

interface Props {
    dropdownClass?: string
}

export default function LocationsDetailsNav(props: Props) {
    const dropdownClass = props?.dropdownClass || ''
    const [options, setOptions] = useState(null)
    const [parent, setParent] = useState(()=>{
        return {
            value: null,
            label: null
        }
    })

    useEffect(() => {
        fetch('/api/wordpress/locations')
            .then(res => res.json())
            .then(data => {
                // get items with node.optGeneral.optGroup == 'london'
                const london = data.filter((item)=> item.node.optGeneral.optGroup == 'london')
                const countrywide = data.filter((item)=> item.node.optGeneral.optGroup != 'london')
                // merge both
                data = london.concat(countrywide)

                setOptions(data.map((item)=>({
                    value: item.node,
                    label: (item.node.optGeneral.optGroup == 'london' ? 'London ' : '') + item.node.title
                })))
                // console.log('details nav loaded...')
            });
    }, []);

    return(
        <>
            {options &&
                <Select
                    placeholder={'Choose location'}
                    isSearchable={false}
                    options={options}
                    onChange={(option)=>{
                        setParent(option)
                    }}
                    value={parent.value ? parent : null}
                    className={`react-select-container ${dropdownClass}`}
                    classNamePrefix="react-select"
                    components={{
                        DropdownIndicator: () => (
                            <span className="dropdown-indicator">
                                <Icon name={`dropdown-arrow`} className='' />
                            </span>
                        )
                    }}
                />}

            <AnimatePresence
                initial={false}
                mode="wait"
                >

                {parent.value && (
                    <motion.div key={parent.value.databaseId}
                    initial={{ opacity: 0, y: 30 }}
                    animate={{
                        opacity: 1,
                        y: 0,
                        transition: {
                            delay: 0.2,
                            type: "spring",
                            visualDuration: 0.3,
                            bounce: 0.4,
                        },
                    }}
                    exit={{ opacity: 0, y: 30 }}
                    >
                        <div className="mt-40">
                            <h2 className="wp-block-heading mt-0 mb-20" id="address">Address</h2>
                            <p className="ms-lg-70 ms-xl-100 address-text" dangerouslySetInnerHTML={{__html: parent.value.optGeneral.optAddressMultiline}} />
                            <p className="ms-lg-70 ms-xl-100 mb-50">
                                <a href={`mailto:{parent.value.optGeneral.optEmail}`}>{parent.value.optGeneral.optEmail}</a><br />
                                <a href={`tel:${parent.value.optGeneral.optPhone}`}>{parent.value.optGeneral.optPhone}</a>
                                </p>
                        </div>
                        <div className="block-times">
                            <OpeningTimes location={parent.value} headingStyle="h2"></OpeningTimes>
                        </div>
                    </motion.div>
                )}
            </AnimatePresence>

        </>
    )
}

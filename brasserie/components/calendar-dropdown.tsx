//@ts-nocheck
import Calendar from "react-calendar"
import { format } from "date-fns"
import { Dropdown } from "react-bootstrap"
import { useId, useState } from "react"
import { Icon } from "./icon";

export default function CalendarDropdown(props) {
    // get max date which is now - 18 years
    const maxDate = new Date()
    maxDate.setFullYear(maxDate.getFullYear() - 18)

    const align = props?.align || 'start'
    const [state, setState] = useState({
        Date: null,
        DateFormatted: null,
        minActiveDate: maxDate,
        showDateDropdown: false
    })

    // console.log(props)

    const onActiveStartDateChangeHandler = ({ activeStartDate, value, view }) => {
        console.log("vv:", activeStartDate, value, view);
        setState({...state, minActiveDate: activeStartDate})
      };

    return(
        <Dropdown
            className={`dropdown calendar-dropdown`}
            onToggle={(e)=>setState({...state, showDateDropdown: !state.showDateDropdown})}
            align={align}
            show={state.showDateDropdown}>
                <Dropdown.Toggle className="form-control" as="div" style={{maxWidth: "100%"}}>
                    { state.DateFormatted || 'Select Date' }
                    <span className="dropdown-indicator">
                       <Icon name={`dropdown-arrow`} className='' />
                    </span>
                </Dropdown.Toggle>
                <Dropdown.Menu renderOnMount={true} as="div" className={`dropdown-menu dropdown-menu-full`}>
                    <div className="dropdown-menu-inner">
                        <Calendar
                        defaultView="decade"
                        instanceId={useId()}
                        className="mx-auto"
                        value={state.Date}
                        // disabled
                        maxDate={maxDate}
                        activeStartDate={state.minActiveDate}
                        onActiveStartDateChange={onActiveStartDateChangeHandler}
                        onChange={(value, event)=>setState({...state,
                            Date: format(new Date(value), "yyyy-MM-dd"),
                            DateFormatted: format(new Date(value), "dd/MM/yyyy"),
                            showDateDropdown: false
                        })}
                        formatLongDate={(locale, date) => format(new Date(date), 'yyyy-MM-dd')}
                        ></Calendar>
                    </div>
                </Dropdown.Menu>
                <input type="hidden" name="full_dob" value={state.Date} />
            </Dropdown>
    )
}
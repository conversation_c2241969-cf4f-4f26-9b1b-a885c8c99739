// @ts-nocheck
import OpeningStatus from "./opening-status"
import { useLocationContext } from "./providers/locationProvider"

export default function OpeningTimes(props) {
    const location = props?.location || useLocationContext()
    if( !location ) return null
    const timeGroups = location.optGeneral?.optTimesNew || null,
        isNewStyle = location.optGeneral?.optTimesNew ? true : false,
        optPubClosed = location.optGeneral?.optPubClosed || false,
        optPubClosedText = location.optGeneral?.optPubClosedText || null,
        optPubClosedDate =location.optGeneral?.optPubClosedDate || null,
        today = new Date().toISOString().slice(0,10).replace(/-/g,""),
        isPubClosed = optPubClosed && optPubClosedDate && optPubClosedDate >= today ? true : false,
        ids = props.ids ? props.ids.split(',').map((id)=>parseInt(id)) : null,
        HeadingTag = props?.headingStyle ? `${props?.headingStyle}` : 'h4'

    // console.log(timeGroups)

    const getFormatedTime = (time)=>{
        let formatedTime = new Date("1970-01-01T" + time)
                            .toLocaleTimeString("en-US", { hour: "numeric", minute: "2-digit" })
                            .replaceAll(":00","")
                            .replaceAll(" ","")
                            .toLowerCase()
        return formatedTime.toString() != "invaliddate" ? formatedTime.toString() : time
    }

    return(
        <div className="opening-times">
            {isPubClosed && optPubClosedText && <div className="pub-closed-message">{optPubClosedText}</div>}
            {!isPubClosed && timeGroups && timeGroups.filter((item,i)=>{
                if( item?.hideSectionEntirely ) {
                    return false
                } else if(ids){
                    return ids.includes(i+1)
                } else {
                    return true
                }
            }).map((item, i)=>(
               <div key={`time-group-${i}`} className="opening-times-group d-flex flex-column">
                 <HeadingTag className="mt-0"><span>{item.name}</span> {isNewStyle && !item?.hideStatus && <OpeningStatus group={item} />}</HeadingTag>
                    <ul className="list-unstyled mt-0 mb-50 d-flex flex-column w-100">
                        {item.entries && item.entries.map((entry,i2)=>(
                            <li key={`time-entry-${i2}`} className="d-flex flex-row flex-wrap justify-content-center justify-content-md-start align-items-baseline">
                                {entry.closed ? (
                                    <>
                                    <span className="day w-100 p-0 text-uppercase" dangerouslySetInnerHTML={{__html: entry.label}} /> <span className="time time-from">Closed</span>
                                    </>
                                ) : (
                                    <>
                                    <span className="day w-100 p-0 text-uppercase" dangerouslySetInnerHTML={{__html: entry.label}} />
                                    <span className="time">
                                        {entry.from && <span className="time-from" suppressHydrationWarning>{getFormatedTime(entry.from)}</span>}
                                        <span className="separator">&nbsp;-&nbsp;</span>
                                        {entry.to && <span className="time-to" suppressHydrationWarning>{getFormatedTime(entry.to)}</span>}
                                    </span>
                                    {entry.note && <small className="note d-inline-block ms-10">{`(${entry.note})`}</small>}
                                    </>
                                )}
                            </li>
                        ))}
                    </ul>
               </div>
            ))}
        </div>
    )
}
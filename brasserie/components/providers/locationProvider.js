import { createContext, useContext } from 'react'

// Create Context object.
const LocationContext = createContext()

// Export Provider.
export function LocationProvider(props) {
	const {value, children} = props

	return (
	   <LocationContext.Provider value={value}>
		{children}
	   </LocationContext.Provider>
	)
}

// Export useContext Hook.
export function useLocationContext() {
	return useContext(LocationContext);
}

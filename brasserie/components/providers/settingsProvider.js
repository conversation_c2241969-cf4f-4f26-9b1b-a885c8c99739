import { createContext, useContext } from 'react'

// Create Context object.
const SettingsContext = createContext()

// Export Provider.
export function SettingsProvider(props) {
	const {value, children} = props

	return (
	   <SettingsContext.Provider value={value}>
		{children}
	   </SettingsContext.Provider>
	)
}

// Export useContext Hook.
export function useSettingsContext() {
	return useContext(SettingsContext);
}

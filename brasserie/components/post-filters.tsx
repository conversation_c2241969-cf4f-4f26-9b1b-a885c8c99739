import Link from "next/link"
import { useRouter } from "next/router"
import { SettingsProvider, useSettingsContext } from "./providers/settingsProvider"

export default function PostFilters(props) {
    const router = useRouter()
    const categoryFilters = props.categoryFilters || false

    // console.log(categoryFilters)

    return (
        <div className="blog-filters">
            <p className="filters text-center">
                <Link href="/news/" className={`btn btn-primary m-10 ${router.asPath.indexOf('/news/') != -1 ?'active':''}`}>All</Link>
                {(!categoryFilters || categoryFilters?.announcements.p.o.t>0) && <Link href="/announcements/" className={`btn btn-primary m-10 ${router.asPath.indexOf('/announcements/') != -1 ?'active':''}`}>Announcements</Link>}
                {(!categoryFilters || categoryFilters?.offers.p.o.t>0) && <Link href="/offers/" className={`btn btn-primary m-10 ${router.asPath.indexOf('/offers/') != -1 ?'active':''}`}>Offers</Link>}
                {(!categoryFilters || categoryFilters?.events.p.o.t>0) && <Link href="/events/" className={`btn btn-primary m-10 ${router.asPath.indexOf('/events/') != -1 ?'active':''}`}>Events</Link>}
                {(!categoryFilters || categoryFilters?.recipes.p.o.t>0) && <Link href="/recipes/" className={`btn btn-primary m-10 ${router.asPath.indexOf('/recipes/') != -1 ?'active':''}`}>Recipes</Link>}
            </p>
        </div>
    )
}
import GalleryScroll2 from "./blocks/gallery-scroll-2";
import PostRelatedPreview from "./post-related-preview";
import { useSettingsContext } from "./providers/settingsProvider";

export default function PostRelated(props) {
  // console.log(posts)
  const {settings} = useSettingsContext(),
  posts = props.posts,
  noTitle = props.noTitle || false,
  siteTitle = settings?.general.title || false,
  title = props?.title || `What's on at  ${siteTitle}`,
  layout = props.layout || 'stacked'

  return(
    <>
    {!noTitle && <h1 className="title text-center m-0">{title}</h1>}
    {layout == 'scroll' ? (
        <GalleryScroll2>
          {posts && Object.keys(posts).map((key) => (
          <PostRelatedPreview key={`post-related-${key}`} node={posts[key][0].node} type={key} layout={layout} />
        ))}
        </GalleryScroll2>
      ) : (
          <>
          {posts && Object.keys(posts).map((key) => (
            <PostRelatedPreview key={`post-related-${key}`} node={posts[key][0].node} type={key} layout={layout} />
          ))}
          </>
      )}
    </>
  )
}
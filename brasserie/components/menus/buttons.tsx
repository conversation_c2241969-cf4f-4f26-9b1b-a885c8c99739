import { useLocationContext } from "@components/providers/locationProvider";
import { useSettingsContext } from "@components/providers/settingsProvider";
import Link from "next/link";


export default function MenusButtons() {
    const location = useLocationContext()
    const parentId = location?.ancestors?.nodes.map((item)=>item.databaseId) || location.databaseId || false
    const {settings} = useSettingsContext()
    const menusJson = settings?.locationsMenusData ? JSON.parse(settings.locationsMenusData) : false

    if( !parentId ) return null
    const parentMenus = menusJson.filter((item)=> item.id == parentId)
    if( !parentMenus.length ) return null
    const options = parentMenus[0].menus.map((item)=>({
        value: item.url,
        label: item.title
    }))

    return(
        <ul className="list-unstyled text-center">
            {options && options.map((item) => (
                <li><Link href={item.value} className="btn btn-primary mb-10 d-block">{item.label}</Link></li>
            ))}
        </ul>
    )
}
"use client"
import Head from "next/head"
import { useCallback, useEffect, useRef, useState } from "react"

declare global {
    interface Window {
        $k10: any;
    }
}

export default function TenKitesEmbed({tenkitesUrl}) {
    const tenkitesMenu = tenkitesUrl || false
    const [showMenu, setShowMenu] = useState(false)
    const [menuLoaded, setMenuLoaded] = useState(false)
    const [filterTrigger, setFilterTrigger] = useState(false)
    const savedCallback = useRef(null)
    const savedFilterCallback = useRef(null)
    const delay = 1000
    const scriptSrc = "https://menus.tenkites.com/Scripts/_k10.min.js?sbk=20160720"

    // console.log(tenkitesUrl)

    const intervalCallback = useCallback(() => {
        const $k10 = window.$k10
        console.log('interval callback fired...')
        if( !menuLoaded ) {
            if( $k10 && typeof $k10 === 'function' ) {
            console.log('$k10 present')
            $k10(function () {
                $k10.ajax({
                    url: tenkitesMenu,
                    type: 'GET',
                    crossDomain: true,
                    success: function (responseData, textStatus, jqXHR) {
                        console.log('POST success')
                        var $k10tkHtml = $k10('<div/>', {
                            html: responseData
                        });

                        // === Find and wrap k10-recipe__desc content
                        $k10tkHtml.find('.k10-recipe__desc').each(function() {
                            const $desc = $k10(this);
                            const innerHtml = $desc.html();
                            $desc.html(`<span>${innerHtml}</span>`);
                        });

                        var headContent = $k10tkHtml.find('.tk-html-header-scripts').html()
                        var bodyContent = $k10tkHtml.find('.tk-html-body').html()
                        var bodyScripts = $k10tkHtml.find('.tk-html-body-scripts').html()

                        if (headContent) {
                            // Remove unwanted tags from headContent
                            var $headContent = $k10('<div/>', { html: headContent })
                            $headContent.find('meta, title, base, link[rel="canonical"], link[rel="shortlink"]').remove()
                            $headContent.find('link:not([rel="stylesheet"]):not([rel="preconnect"]):not([rel="dns-prefetch"])').remove()
                            var safeHeadContent = $headContent.html()
                            if (safeHeadContent) {
                                $k10('head').append(safeHeadContent)
                            }
                        }

                        $k10('.k10-html-container').append(bodyContent)
                        setTimeout(function () {
                            $k10('body').append(bodyScripts)
                            setShowMenu(true)
                        }, 50);
                    },
                    error: function (responseData, textStatus, errorThrown) {
                        console.log('POST failed')
                    }
                });
            });
            console.log('menu loading...')
            setMenuLoaded(true)
            }
        }
    },[tenkitesMenu])

    const filterTriggerCallback = useCallback(() => {
        const filterTrigger = document.querySelector('.k10-labels-filter2__name') || false
        if( filterTrigger ) {
            console.log('filterTrigger found!')
            setFilterTrigger(true)
            const fakeTrigger = document.querySelector('.js-fakeAllergenFilter')
            if( fakeTrigger ) {
                fakeTrigger.addEventListener('click', function(e){
                    e.preventDefault();
                    (filterTrigger as HTMLElement).click()
                })
                fakeTrigger.classList.remove('d-none')
            }
        }
    },[])

    useEffect(()=>{
        if (typeof window === "undefined") return
        if (!document.querySelector(`script[src="${scriptSrc}"]`)) {
            const script = document.createElement("script")
            script.src = scriptSrc
            script.async = true
            document.head.appendChild(script)
        }
    }, [scriptSrc])

    useEffect(()=>{
        if( !showMenu ) return
        console.log('menu loaded!')

        savedFilterCallback.current = filterTriggerCallback
        let filterInterval = setInterval(savedFilterCallback.current, delay)
        if( filterTrigger ) { clearInterval(filterInterval) }
        return ()=>{ clearInterval(filterInterval) }

    })

    useEffect(()=>{
        savedCallback.current = intervalCallback
        let tenkitesInterval = setInterval(savedCallback.current, delay)
        if( menuLoaded ) { clearInterval(tenkitesInterval) }
        return ()=>{ clearInterval(tenkitesInterval) }
    }, [intervalCallback, menuLoaded])

    useEffect(()=>{
        return ()=>{
            console.log('unmounting TenkitesEmbed...')
            // --- find all <link> and <script> in <head> with tnekites.com in src and remove them
            document.querySelectorAll(`
                head link[href*="tenkites.com"],
                script[src*="tenkites.com"],
                head style[id="k10-custom-fonts"],
                head style[id="k10-custom-fonts-by-import"],
                head style[id="k10-css-root-varibles"]
                `).forEach((item)=>{
                item.remove()
            })
        }
    },[tenkitesMenu])

    return(
        <div className="k10-html-container"></div>
    )
}

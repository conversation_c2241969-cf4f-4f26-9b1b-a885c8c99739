import { useState } from "react";
import { useRouter } from "next/router";
import Select from "react-select";
import { useLocationContext } from "@components/providers/locationProvider";
import { Icon } from "@components/icon";
import { useSettingsContext } from "@components/providers/settingsProvider";


export default function MenusDropdown({posts}) {
    const location = useLocationContext()
    if( !location ) return null
    // if location.ancestors exist take last record and use as parent
    const parentId = location?.ancestors?.nodes.map((item)=>item.databaseId) || location.databaseId || false
    const [value, setValue] = useState(()=>{
        return {
            value: null,
            label: null
        }
    })
    const router = useRouter()
    const {settings} = useSettingsContext()
    const menusJson = settings?.locationsMenusData ? JSON.parse(settings.locationsMenusData) : false

    console.log(location, menusJson)

    if( !parentId ) return null
    const parentMenus = menusJson.filter((item)=> item.id == parentId)
    if( !parentMenus.length ) return null
    const options = parentMenus[0].menus.map((item)=>({
        value: item.url,
        label: item.title
    }))

    return(
        options &&
            <Select
                placeholder={'Please select'}
                isSearchable={false}
                options={options}
                onChange={(option)=>{router.push(option.value)}}
                value={value.value ? value : null}
                className="react-select-container menus-dropdown"
                classNamePrefix="react-select"
                components={{
                    DropdownIndicator: () => (
                        <span className="dropdown-indicator">
                            <Icon name={`dropdown-arrow`} className='' />
                        </span>
                    )
                }}
            />

    )
}
import { Icon } from "@components/icon";
import { useLocationContext } from "@components/providers/locationProvider";
import { useRouter } from "next/router";
import { useState, useEffect } from "react";
import Select from "react-select";
import { AnimatePresence, motion } from "motion/react";
import Link from "next/link";

interface Props {
    type?: string
    dropdownClass?: string
}

export default function LocationsMenusNavOLD(props: Props) {
    const type = props?.type || 'buttons'
    const dropdownClass = props?.dropdownClass || ''
    const location = useLocationContext()
    const ancestors = location?.ancestors?.nodes.map((item)=>item.databaseId) || false
    const router = useRouter()
    const [options, setOptions] = useState(null)
    const [menus, setMenus] = useState(null)
    const [parentId, setParentId] = useState(()=>{
        return {
            value: null,
            label: null
        }
    })

    // console.log(ancestors)

    useEffect(() => {
        fetch('/api/wordpress/locations')
            .then(res => res.json())
            .then(data => {
                setOptions(data.map((item)=>({
                    value: item.node.databaseId,
                    label: (item.node.optGeneral.optGroup == 'london' ? 'London ' : '') + item.node.title
                })))
                if( data.length && ancestors ) {
                    // console.log('ancestors: ', ancestors)
                    data.forEach((item)=>{
                        // console.log(item.node.databaseId, ancestors.databaseId)
                        if( ancestors.includes(item.node.databaseId) ) {
                            // console.log('Parent found: ', item.node)
                            const currentOption = {
                                value: item.node.databaseId,
                                label: (item.node.optGeneral.optGroup == 'london' ? 'London ' : '') + item.node.title
                            }
                            setParentId(currentOption)
                        }
                    })
                }
            });
    }, []);

    useEffect(() => {
        fetch('/api/wordpress/menus?parentId='+parentId.value)
            .then(res => res.json())
            .then(data => {
                setMenus(data.map((item)=>({value: item, label: item.node.title})))
                console.log('locations nav loaded...')
            });
    } , [parentId])

    return(
        <>
            {options &&
                <Select
                    placeholder={'Choose location'}
                    isSearchable={false}
                    options={options}
                    onChange={(option)=>{ setParentId(option) }}
                    value={parentId.value ? parentId : null}
                    className={`react-select-container ${dropdownClass}`}
                    classNamePrefix="react-select"
                    components={{
                        DropdownIndicator: () => (
                            <span className="dropdown-indicator">
                                <Icon name={`dropdown-arrow`} className='' />
                            </span>
                        )
                    }}
                />}

            <AnimatePresence
                initial={false}
                mode="wait"
            >
                <motion.div key={parentId.value}
                    initial={{ opacity: 0, y: 30 }}
                    animate={{
                        opacity: 1,
                        y: 0,
                        transition: {
                            delay: 0.2,
                            type: "spring",
                            visualDuration: 0.3,
                            bounce: 0.4,
                        },
                    }}
                    exit={{ opacity: 0, y: 30 }}
                >

                    {menus && type == 'buttons' &&
                        <ul className="list-unstyled text-center mt-20">
                            {menus && menus.map((item, i) => (
                                <li key={`menus-nav-button${i}`}><Link href={item.value.node.uri} className="btn btn-primary mb-10 d-block">{item.label}</Link></li>
                            ))}
                        </ul>}


                    {menus && type == 'links' && (
                        <ul className="menus-nav list-unstyled text-center my-20 text-uppercase">
                            {menus.map((item,i) =>
                                (
                                    !item.value.node.singleMenu.menuHide &&
                                    <li key={`menus-nav-item${i}`} className={`menu-item list-inline-item ${router.asPath == item.value.node.uri ? ' active' : ''}`}>
                                        <Link href={`${item.value.node.uri}`} dangerouslySetInnerHTML={{__html: item.value.node.singleMenu.menuCustomTitle || item.value.node.title}} className="position-relative d-inline-block"></Link>
                                    </li>

                                )
                            )}
                            <li className="menu-item list-inline-item js-fakeAllergenFilter d-none">
                                <a href="#" className="position-relative d-inline-block">Allergies
                                    <span className="dropdown-indicator">
                                        <Icon name={`dropdown-arrow`} className='ms-10' color="inherit" />
                                    </span>
                                </a>
                            </li>
                        </ul>
                    )}

                </motion.div>
            </AnimatePresence>
        </>
    )
}

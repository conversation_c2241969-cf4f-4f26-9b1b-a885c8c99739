import { Icon } from "@components/icon";
import { useLocationContext } from "@components/providers/locationProvider";
import { useRouter } from "next/router";
import { useState, useEffect, useRef } from "react";
import Select from "react-select";
import { AnimatePresence, motion } from "motion/react";
import Link from "next/link";

interface Props {
    type?: string
    dropdownClass?: string
    json?: any
}

export default function LocationsMenusNav(props: Props) {
    const json = props?.json
    const type = props?.type || 'buttons'
    const dropdownClass = props?.dropdownClass || ''
    const location = useLocationContext()
    const ancestors = location?.ancestors?.nodes.map((item)=>item.databaseId) || false
    const router = useRouter()
    const [options, setOptions] = useState(json)
    const [menus, setMenus] = useState(null)
    const [currentMenu, setCurrentMenu] = useState(null)
    const [parentId, setParentId] = useState(()=>{
        return {
            value: null,
            label: null
        }
    })
    const isFirstLoad = useRef(true)

    console.log(json)

    useEffect(() => {
        if( json ) {
            setOptions(json.map((item)=>({
                value: item.id,
                label: (item.opt_group == 'london' ? 'London ' : '') + item.title
            })))
            if( ancestors ) {
                json.map((item)=>{

                    if( ancestors.includes(item.id) ) {
                        // console.log('Parent found: ', item.node)
                        const currentOption = {
                            value: item.id,
                            label: (item.opt_group == 'london' ? 'London ' : '') + item.title
                        }
                        setParentId(currentOption)
                    }
                })
            }
        }
    }, []);

    useEffect(() => {
        if( !parentId.value ) return
        const newMenus = json.filter((item)=> item.id == parentId.value)[0].menus
        console.log(newMenus)
        setMenus( newMenus )

        if( newMenus && isFirstLoad.current ) { // set current menu on first load only
            const matchingMenu = newMenus.find((item)=> item.url == router.asPath)
            if( matchingMenu ) {
                setCurrentMenu({
                    value: matchingMenu.url,
                    label: matchingMenu.title
                })
            }
            isFirstLoad.current = false
        }else {
            setCurrentMenu(null)
        }
        console.log('locations nav loaded...')
    } , [parentId])

    return(
        <div className={dropdownClass}>
            {options &&
                <Select
                    placeholder={'Please select'}
                    isSearchable={false}
                    options={options}
                    onChange={(option)=>{ setParentId(option) }}
                    value={parentId.value ? parentId : null}
                    className={`react-select-container`}
                    classNamePrefix="react-select"
                    components={{
                        DropdownIndicator: () => (
                            <span className="dropdown-indicator">
                                <Icon name={`dropdown-arrow`} className='' />
                            </span>
                        )
                    }}
                />}

            <AnimatePresence
                initial={false}
                mode="wait"
            >
                <motion.div key={parentId.value}
                    initial={{ opacity: 0, y: 30 }}
                    animate={{
                        opacity: 1,
                        y: 0,
                        transition: {
                            delay: 0.2,
                            type: "spring",
                            visualDuration: 0.3,
                            bounce: 0.4,
                        },
                    }}
                    exit={{ opacity: 0, y: 30 }}
                >

                    {menus && type == 'buttons' &&
                        <ul className="list-unstyled text-center mt-20">
                            {menus.map((item, i) => (
                                <li key={`menus-nav-button${i}`}><Link href={item.url} className="btn btn-primary mb-10 d-block">{item.title}</Link></li>
                            ))}
                        </ul>}


                    {menus && type == 'links' && (

                        <Select
                            placeholder={'Please select'}
                            isSearchable={false}
                            options={menus.map((item)=>{ return ({value: item.url, label: item.title}) })}
                            onChange={(option)=>{
                                console.log(option)
                                setCurrentMenu(option)
                                router.push(option.value)
                            }}
                            value={ currentMenu?.value ? currentMenu : null }
                            className={`react-select-container my-20`}
                            classNamePrefix="react-select"
                            components={{
                                DropdownIndicator: () => (
                                    <span className="dropdown-indicator">
                                        <Icon name={`dropdown-arrow`} className='' />
                                    </span>
                                )
                            }}
                        />
                    )}

                </motion.div>
            </AnimatePresence>
        </div>
    )
}

import Script from "next/script"
import { useSettingsContext } from "./providers/settingsProvider"
import { useEffect, useRef } from "react";

export default function CookiebotDeclaration() {
    const {settings} = useSettingsContext(),
    cookiebotID = settings.acf.optGeneral.optCookiebot || false

    const legalContainerRef = useRef(null);

    useEffect(() => {
        const script = document.createElement('script');
        script.id = 'CookieDeclaration';
        script.src = `https://consent.cookiebot.com/${cookiebotID}/cd.js`;
        script.type = 'text/javascript';
        script.async = true;

        legalContainerRef.current.appendChild(script);

        return () => {
            legalContainerRef.current && legalContainerRef.current.removeChild(script);
        };
    }, [legalContainerRef]);

    return(
        <div ref={legalContainerRef} className="cookiebot-declaration-shortcode" suppressHydrationWarning={true}></div>
    )
}
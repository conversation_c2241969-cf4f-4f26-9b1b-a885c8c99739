"use client"
import Link from "next/link";
import { useEffect, useState } from "react";
import { Modal } from "react-bootstrap";
import { useSettingsContext } from "../providers/settingsProvider";
import { isPostExpired } from "../../lib/utils";
import { useLocationContext } from "@components/providers/locationProvider";

export default function ExpiredContentPopup(props) {
    const [show, setShow] = useState(false),
          type = props?.category || 'offer'
    const {settings} = useSettingsContext()
    const optGeneral = settings?.acf.optGeneral || false
    const optFooterBtn = optGeneral?.optFooterBtn || 'Sign up to our newsletter'
    const optFooterBtnLink = optGeneral?.optFooterBtnLink || '/signup/'
    const optFooterBtnNewtab = optGeneral?.optFooterBtnNewtab ? '_blank' : '_self'

    // --- Adjust headline if offer/event is no longer available
    const post = props.ctx || false
    if(!post) return

    const expiryDate = post.singlePost?.expiryDate ? post.singlePost?.expiryDate.replace(':00', '') : false,
          postExpiryTimestamp = expiryDate ? new Date(post.singlePost?.expiryDate.slice(0, expiryDate.indexOf('at'))).getTime() : false,
          isArchivePost = postExpiryTimestamp && isPostExpired(postExpiryTimestamp) ? true : false
    if( !isArchivePost ) return

    function closeModal() {
        setShow(false);
    }

    useEffect(()=>{
        setShow(true)
        return () => {
            closeModal()
        }
    },[])

    return(
        <Modal
            show={show}
            dialogClassName="modal-dialog-scrollable"
            fullscreen={`sm-down`}
            className={`solo-text`}
            centered >
            <Modal.Body className="d-flex justify-content-center align-items-center">
                <div className={`px-md-50 promo-content text-center pt-50`}>
                    <h4 className="h3">This {type} is no longer available.</h4>
                    <ul className="list-unstyled mb-50 w-100">
                        <li>
                            <button type="button" className="btn btn-outline-linen my-10" onClick={()=>closeModal()}>Read anyway</button>
                        </li>
                        <li>
                            <Link href={`/${type}s`} className="btn btn-outline-linen my-10" >View available {type}s</Link>
                        </li>
                        <li>
                            <a href={optFooterBtnLink} target={optFooterBtnNewtab} className='btn btn-outline-linen my-10'>{optFooterBtn}</a>
                        </li>
                    </ul>
                </div>
            </Modal.Body>
            <button type="button" className="btn-close position-absolute bg-plaster" onClick={() => closeModal()} aria-label="Close">
                <svg className='close' fill='currentColor' viewBox="0 0 30.001 24.001">
                    <use href={`/images/icon.svg` + `#close`} />
                </svg>
            </button>
        </Modal>
    )
}
"use client"
import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";
import { Col, Modal, Row } from "react-bootstrap";
import { getCookie, setCookie } from "../../lib/utils";

export default function PromoPopup(props) {
    const [show, setShow] = useState(false),
    content = props?.promoContent,
    image = props?.promoImage || false,
    promoImageLink = props?.promoImageLink || false,
    promoImageTarget = props?.promoImageTarget || false,
    buttons = props?.promoButton,
    timeout = props?.promoPopupTimeout*24,
    cookieName = 'promo_timeout_'+props.postID,
    hasContent = content || buttons ? true : false

    let imgWidth = 0,
        imgHeight = 0

    if( !(image || hasContent) ) return false

    if( image ) {
        imgWidth = image?.mediaDetails?.sizes ? image.mediaDetails.sizes[0].width : image?.mediaDetails.width,
        imgHeight = image?.mediaDetails?.sizes ? image.mediaDetails.sizes[0].height : image?.mediaDetails.height
    }

    // console.log(props)

    function closeModal() {
        if( !getCookie(cookieName) && timeout ) setCookie(cookieName,1,timeout);
        setShow(false);
    }

    const getPromoImage = ()=>{
        return(
            <Image
            src={image.sourceUrl}
            width={imgWidth}
            height={imgHeight}
            className="promo-img img-fluid"
            alt="Promo popup image"
            priority={true} />
        )
    }

    useEffect(()=>{
        if( !getCookie(cookieName) && props?.promoEnable ) {
            setTimeout(()=>{
                console.log('promo popup timeout fired...')
                setShow(true)
              }, 5000)
        }
        return () => {
            closeModal()
        }
    },[])

    return(
        <Modal
            show={show}
            size="lg"
            dialogClassName="modal-dialog-scrollable"
            fullscreen={hasContent?"sm-down":""}
            className={`${image?"solo-img":"solo-text"}`}
            centered
            aria-label="Promo popup">
            <Modal.Body className="p-0">
                <Row className="g-0 h-100">
                    {image && (
                        <Col className={`col-12${hasContent?" col-lg-6":" px-0"}`}>
                        {promoImageLink ? (
                            <a href={promoImageLink} target={promoImageTarget ? `_blank` : `_self`} className="d-block h-100">
                                {image && getPromoImage()}
                            </a>
                        ) : (
                            image && getPromoImage()
                        )}
                        </Col>
                    )}
                    {hasContent && (
                        <Col className={`${image ? 'col-12 col-lg-6 px-lg-30 py-30 text-center text-lg-start d-flex flex-column justify-content-center align-items-center' : 'col-12 pt-100 py-md-30 py-lg-50 px-30 px-lg-50 d-flex flex-column justify-content-center align-items-center text-center'}`}>
                            {content && <div className={`${image ? 'px-50':'px-md-50'} promo-content`} dangerouslySetInnerHTML={{ __html: content }} />}
                            {buttons && (
                                <ul className="promo-btns list-unstyled m-0 w-100 d-flex flex-row justify-content-center">
                                    {buttons.map((btn, index)=>(
                                        <li key={`promo-btn-${index}`}>
                                            {btn.target ? (
                                                <a href={btn.link} className="btn btn-primary" target="_blank">{btn.label}</a>
                                            ) : (
                                                <Link href={btn.link} className="btn btn-primary" onClick={()=>closeModal()}>{btn.label}</Link>
                                            )}
                                        </li>
                                    ))}
                                </ul>
                            )}
                        </Col>
                    )}
                </Row>
            </Modal.Body>
            <button type="button" className="btn-close text-warm position-absolute d-block" onClick={() => closeModal()} aria-label="Close">
                <svg className='close d-block' fill='currentColor' viewBox="0 0 30.001 24.001">
                    <use href={`/images/icon.svg` + `#close`} />
                </svg>
            </button>
        </Modal>
    )
}
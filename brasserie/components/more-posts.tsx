import { useState } from "react";
import GalleryScroll2 from "./blocks/gallery-scroll-2";
import MorePostsPreview from "./moreposts-preview";

export default function MorePosts(props) {
  const [posts, setPosts] = useState(props.posts),
    title = props?.title || `What's on`,
    noTitle = props.noTitle || false,
    layout = props.layout || 'stacked',
    options = props.options || false,
    restApi = process.env.NEXT_PUBLIC_WORDPRESS_REST_API,
    restSecret = process.env.NEXT_PUBLIC_WORDPRESS_PREVIEW_SECRET,
    restApiEndpoint = '/blog/v1/posts',
    [hasNextPage, setHasNextPage] = useState(true)

  // console.log(options)
  // console.log(posts)

  const loadMore = async (e, options) => {
    const target = e.target
    if( options.page ) options.page = options.page + 1

    // disable target button while loading more posts
    target.disabled = true
    // add loading class to target parent
    target.parentElement.classList.add('loading-inline')

    // create query parameters with options
    const data = new URLSearchParams();
    data.append('api_secret', restSecret) // add secret to auth api call
    for (const [key, value] of Object.entries(options)) {
      data.append(key, value as string);
    }

    // fetch more posts
    let response = await fetch(restApi+restApiEndpoint, {
      method: 'POST',
      body: data
    })
    let json = await response.json()
    // console.log(options, json)

    // add json.data.posts to posts array
    setPosts([...posts, ...json.data.posts])
    // enable target button after loading more posts
    target.disabled = false
    // remove loading class from target parent
    target.parentElement.classList.remove('loading-inline')
    // check if there are no more posts to load
    if( !json.data.pagination?.has_next_page ) setHasNextPage(false)
  }

  return(
    <>
    {!noTitle && <h2 className="title h1 text-center m-0">{title}</h2>}
    {layout == 'scroll' ? (
      <GalleryScroll2>
        {posts && posts.map(({node}) => (
          <MorePostsPreview key={`more-offers-${node.slug}`} node={node} layout={layout} />
        ))}
      </GalleryScroll2>
    ) : (
        <>
        {posts && posts.map(({node}) => (
          <MorePostsPreview key={`more-offers-${node.slug}`} node={node} layout={layout} />
        ))}
        {hasNextPage &&
          <p className="text-center my-0 py-30"><span className="d-inline-block">
            <button className="btn btn-red" onClick={(e)=>loadMore(e, options)}>Load more</button>
          </span></p>
        }
        </>
    )}
    </>
  )
}
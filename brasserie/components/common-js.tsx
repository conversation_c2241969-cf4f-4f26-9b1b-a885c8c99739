// @ts-nocheck
"use client"
import { useRouter } from "next/router"
import { useEffect } from "react"
import { deleteCookie, setCookie } from "../lib/utils"
import { UTM_COOKIENAME } from "../lib/constants"
import { useSettingsContext } from "./providers/settingsProvider"

export default function CommonJS(){
    const router = useRouter(),
          {settings} = useSettingsContext()

    useEffect(()=>{
        if (!router.isReady) return

        let queryParams = router.query

        // Fallback for ISR: parse URL params directly from window.location
        if (typeof window !== 'undefined' && Object.keys(queryParams).length <= 1) {
            const urlParams = new URLSearchParams(window.location.search)
            queryParams = {
                ...queryParams,
                im_ref: urlParams.get('im_ref'),
                utm_source: urlParams.get('utm_source'),
                utm_medium: urlParams.get('utm_medium'),
                utm_campaign: urlParams.get('utm_campaign'),
                utm_term: urlParams.get('utm_term'),
                utm_content: urlParams.get('utm_content'),
                promocode: urlParams.get('promocode')
            }
        }
        // console.log('router.query', queryParams)

        // === START: Set UTM campaign cookie if UTM params present
        const setCampaignCookie = function() {
            const {utm_source} = queryParams

            // console.log('checking if UTM params present...')
            if( utm_source ) {
                const {utm_medium, utm_campaign, utm_term, utm_content, promocode} = queryParams
                const utm_cookie = {
                    utm_source: encodeURIComponent(utm_source),
                    utm_medium: encodeURIComponent(utm_medium),
                    utm_campaign: encodeURIComponent(utm_campaign),
                    utm_term: encodeURIComponent(utm_term),
                    utm_content: encodeURIComponent(utm_content),
                    promocode: encodeURIComponent(promocode)
                }
                // console.log(utm_cookie)

                // FIXME: remove cookie for now, we cam bring it back by uncomment line below
                // setCookie(UTM_COOKIENAME, JSON.stringify(utm_cookie), 7*24)
            }

            // FIXME: remove cookie for now, we cam bring it back by removing this line
            deleteCookie(UTM_COOKIENAME)
        }
        setCampaignCookie()
        // === ENd: Set UTM campaign cookie if UTM params present

        // === IMPACT
        // set im_ref cookie from url param
        const {im_ref} = queryParams
        if( im_ref ) {
            setCookie('im_ref', im_ref, 30*24) // 30 days
        }

    },[router.isReady])

    useEffect(()=>{
        const lazyVideos = [].slice.call(document.querySelectorAll("video.lazy"));
        // console.log('lazy loading videos...')
        if ("IntersectionObserver" in window) {
            let lazyVideoObserver = new IntersectionObserver(function(entries, observer) {
            entries.forEach(function(video) {
                if (video.isIntersecting) {
                for (var source in video.target.children) {
                    var videoSource = video.target.children[source];
                    if (typeof videoSource.tagName === "string" && videoSource.tagName === "SOURCE") {
                    videoSource.src = videoSource.dataset.src;
                    }
                }

                video.target.load();
                video.target.classList.remove("lazy");
                lazyVideoObserver.unobserve(video.target);
                console.log('video loaded...')
                }
            });
            });

            lazyVideos.forEach(function(lazyVideo) {
            lazyVideoObserver.observe(lazyVideo);
            });
        }
    },[router])

    return(null)
}
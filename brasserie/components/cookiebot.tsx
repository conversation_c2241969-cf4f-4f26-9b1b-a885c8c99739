import Script from "next/script";
import Head from "next/head";
import { useSettingsContext } from "./providers/settingsProvider";
import { useEffect, useState } from "react";
import { useIsBotDetection } from "../performance/bot-detection";
import DynamicScriptLoader from "../performance/dynamic-script-loader";
// import BotDetectionDebug from "../performance/bot-detection-debug";

declare global {
    interface Window {
        Cookiebot: any;
    }
}

export default function HWCookieBot() {
    const {settings} = useSettingsContext(),
    cookiebotID = settings?.acf.optGeneral.optCookiebot || false,
    gtm = settings?.acf.optGeneral.optGtm || false,
    stellarID = settings?.acf.optGeneral.optStellar || false,
    isBot = useIsBotDetection(),
    shouldLoadScripts = !isBot

    const [scriptsLoaded, setScriptsLoaded] = useState(false);

    // console.log('🤖 HWCookieBot:', { isBot, shouldLoadScripts, scriptsLoaded })

    // Load scripts dynamically after bot detection
    useEffect(() => {
        if (shouldLoadScripts && !scriptsLoaded && !isBot) {
            console.log('✅ Loading third-party scripts for real user');
            setScriptsLoaded(true);
        } else if (isBot) {
            console.log('🚫 Blocking third-party scripts for bot');
        }
    }, [shouldLoadScripts, scriptsLoaded, isBot]);

    return(
        <>
            {/* ===== Load cookiebot handler ===== */}
            {/* {cookiebotID && (
                <Script
                    id='Cookiebot'
                    src='https://consent.cookiebot.com/uc.js'
                    data-cbid={cookiebotID}
                    data-blockingmode='none'
                    type='text/javascript'
                    strategy="beforeInteractive"
                    defer
                    onReady={() => {
                        window.addEventListener("CookiebotOnDialogInit", function() {
                            console.log('CookiebotOnDialogInit fired...')
                            if (window.Cookiebot.getDomainUrlParam("CookiebotScan") === "true") window.Cookiebot.setOutOfRegion();
                        });
                    }}
                >
                </Script>
            )} */}

            {/* <BotDetectionDebug /> */}
            <DynamicScriptLoader>
                {/* ===== Load scripts always (but skip for bots) ===== */}
                {/* Stellar integration */}
                {stellarID && (
                <Head>
                    <script dangerouslySetInnerHTML={{__html: `
                    !function(){var e="body {opacity: 0 !important;}",t=document.createElement("style");t.type="text/css",t.id="page-hide-style",t.styleSheet?t.styleSheet.cssText=e:t.appendChild(document.createTextNode(e)),document.head.appendChild(t),window.rmo=function(){var e=document.getElementById("page-hide-style");e&&(e.parentNode.removeChild(e),document.body.style.opacity="")},setTimeout(window.rmo,3e3)}();
                    `}} />
                    <link rel="preconnect" href="https://d3niuqph2rteir.cloudfront.net" />
                    <link rel="dns-prefetch" href="https://d3niuqph2rteir.cloudfront.net" />
                    <script async src={`https://d3niuqph2rteir.cloudfront.net/client_js/stellar.js?apiKey=${stellarID}`}></script>
                </Head>
            )}

                {/* ===== Load scripts dependent on users consent (but skip for bots) ===== */}
                {/* 1. Google Tag Manager - Statistics consent */}
                {gtm && (
                <Head>
                    {/* Google consent mode v2: defaults */}
                    <script
                    id="google-consent-defaults"
                    dangerouslySetInnerHTML={{
                        __html: `window.dataLayer = window.dataLayer || [];
                        function gtag() { dataLayer.push(arguments); }
                        gtag('consent', 'default', {
                            'ad_user_data': 'denied',
                            'ad_personalization': 'denied',
                            'ad_storage': 'denied',
                            'analytics_storage': 'denied',
                            'wait_for_update': 500,
                        });
                        gtag('js', new Date());
                        gtag('config', '${gtm}');`
                    }}
                    />
                    {/* Google Tag Manager */}
                    <script
                    id="gtag"
                    defer
                    // type="text/plain"
                    // data-cookieconsent="statistics"
                    dangerouslySetInnerHTML={{__html: `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start': new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0], j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src= 'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f); })(window,document,'script','dataLayer','${gtm}');`}}
                    className="d-block"
                    />
                    {/* Google Analytics (Booking widget) */}
                    <script
                    type="text/plain"
                    defer
                    data-cookieconsent="statistics"
                    dangerouslySetInnerHTML={{__html: `
                        (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
                            (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
                            m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
                        })(window,document,'script','//www.google-analytics.com/analytics.js','ga');
                    `}} />
                </Head>
            )}
                {/* 2. Atreemo tracking - Marketing consent */}
                <>
                    <Script
                    type="text/plain"
                    defer
                    data-cookieconsent="marketing"
                    src="https://tracking.atreemo.com/Scripts/TrackingInit.js"
                    />
                    <Script
                    type="text/plain"
                    defer
                    data-cookieconsent="marketing"
                    dangerouslySetInnerHTML={{__html: `AtreemoTrackingLbr.init(["Brasserie Blanc", "https://brasserieblanc.webecast.atreemo.uk"]);`}}
                    />
                    <Script
                    type="text/plain"
                    defer
                    data-cookieconsent="marketing"
                    src="https://tracking.atreemo.com/Scripts/Tracking.js"
                    />
                </>
            </DynamicScriptLoader>
        </>
    )
}
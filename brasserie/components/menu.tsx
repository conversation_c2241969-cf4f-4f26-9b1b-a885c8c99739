// @ts-nocheck
import Link from 'next/link';
import { Dropdown, Nav, NavItem, NavLink } from 'react-bootstrap'
import { debounce, flatListToHierarchical } from '../lib/utils';
import { Icon } from './icon';
import { useRouter } from 'next/router';
import { useSettingsContext } from './providers/settingsProvider';
import { animate } from "motion/react";

export default function Menu({
  children = null, items, menuClass = '', itemClass = '', isFlat = false, allowDropdowns = false, addSocials = false, addBooking = false, wrapLinks = false
}) {
  items = items ? flatListToHierarchical(items.nodes) : null
  // console.log(items)

  const router = useRouter(),
        wp = useSettingsContext(),
        gtm = wp?.settings?.acf.optGeneral.optGtm || false,
        brand = wp?.settings?.acf.optGeneral.optBrand || false,
        pubLocation = wp?.settings?.general.title || false,
        socials = wp.settings?.acf.optGeneral.optSocials || null

  // console.log(brand, gtm, title)

  // add class to menu if isFlat
  if( isFlat ) menuClass += ' flex-column flex-sm-row justify-content-sm-center justify-content-md-between flex-md-nowrap gap-md-20 my-30 nav'

  const clearOffcanvasMenu = (target) => {
    if(target.closest('.dropdown')) return
    const offcanvasSubnav = document.querySelector('.offcanvas-submenu')
    const OffcanvasSubnavInner = document.querySelector('.offcanvas-submenu .inner')
    if( offcanvasSubnav ) {
      OffcanvasSubnavInner.innerHTML = ''
      offcanvasSubnav.classList.remove('show')
    }
  }

  const ToggleMenu = (
    nextShow,
    meta
  ): void => {
    const dropdown = meta.originalEvent.target.closest('.dropdown')
    const offcanvasSubnav = document.querySelector('.offcanvas-submenu')
    const OffcanvasSubnavInner = document.querySelector('.offcanvas-submenu .inner')

    console.log(nextShow, meta.originalEvent.target, dropdown)

    if( dropdown && offcanvasSubnav ) {
      const dropdownMenu = dropdown.querySelector('.dropdown-menu')

      if (nextShow) {
        // Small delay to ensure this runs after any close events
        setTimeout(() => {
          offcanvasSubnav.classList.add('show')
          animate(OffcanvasSubnavInner, { opacity: 0, x: 30 }, { duration: 0.3, ease: "easeOut" })
          setTimeout(() => {
            OffcanvasSubnavInner.innerHTML = '<ul class="list-unstyled">' + dropdownMenu.innerHTML + '</ul>'
            animate(OffcanvasSubnavInner, { opacity: 1, x: 0 }, { duration: 0.3, ease: "easeOut" })
          }, 100)
        }, 100)
      } else {
        // Only close if no other dropdown is about to open
        setTimeout(() => {
          // Check if any dropdown is currently open
          const openDropdown = document.querySelector('.offcanvas-body .dropdown.show')
          if (!openDropdown) {
            animate(OffcanvasSubnavInner, { opacity: 0, x: 30 }, { duration: 0.3, ease: "easeOut" })
            offcanvasSubnav.classList.remove('show')
            OffcanvasSubnavInner.innerHTML = ''
          }
        }, 100)
      }
    }
  }

  const handleMenuclick = (e, NavLink)=>{
    const {href} = e.target
    clearOffcanvasMenu(e.target)
    // === GTM
    if( window ) {
    //  console.log(href, NavLink)
      window.dataLayer = window.dataLayer || []
      window.dataLayer.push({
        event: "navigation",
        pub_location: pubLocation,
        label1: NavLink
      })
   }
  }

  return (
    <Nav as="ul" className={`flex-grow-1 ${menuClass}`}>

    {items && items.map((item, index) => (
      item.uri &&
      <li key={item.key} className={`nav-item my-15 lh-1 ${itemClass} ${item.cssClasses ? item?.cssClasses.join(" "):''}`}>
        {item?.children?.length && allowDropdowns !== false ? (
          <>
          {isFlat ? (
            <>
              <h3 className="h3 lead mt-0 mb-25">{item.title}</h3>
              <ul className='list-unstyled'>
                {item.children.map((child) => (
                  <li key={child.key} className={`nav-item ${child.desc ? 'has-desc ':''}${child.cssClasses ? child?.cssClasses.join(" "):''}`}>
                    {child.desc && <span className='nav-item-desc d-block' dangerouslySetInnerHTML={{ __html: child.desc }}></span>} <Link
                      href={child.uri}
                      onClick={(e)=>handleMenuclick(e, child.title)}
                      className={`nav-link ${router.asPath == child.uri ? ' current-menu-item':''}`}
                      target={child.target}
                      dangerouslySetInnerHTML={{ __html: child.title }}></Link>
                  </li>
                ))}
                {/* Add Socials in front of menu items */}
                {index == items.length - 1 && addSocials && socials && socials.map((item, index)=> (
                      <li key={`socials-${index}`} className="nav-item mb-30 mb-lg-0">
                          <a className="nav-link text-capitalize" href={item.link} target="_blank" title={`Follow on ${item.provider}`}>{item.provider}</a>
                      </li>
                  ))
                }
              </ul>
            </>
          ) : (
            <Dropdown
              onToggle={ToggleMenu}
              onSelect={()=> console.log('menu item selected')}
              as={NavItem}
              className=''>
              <Dropdown.Toggle id={item.key} className='d-flex align-items-center' as={NavLink}
                dangerouslySetInnerHTML={{__html: wrapLinks ? `<span>${item.title}</span>` : item.title}}>
                {/* <Icon name="arrow-down" /> */}
              </Dropdown.Toggle>
              <Dropdown.Menu renderOnMount={true} as="ul" className='text-start'>
              {item.children.map((child) => (
                <li key={child.key} className={`${child.desc ? 'has-desc ':''}${child.cssClasses ? child?.cssClasses.join(" "):''}`}>
                  {child.desc && <span className='nav-item-desc d-block' dangerouslySetInnerHTML={{ __html: child.desc }}></span>} <Link
                    href={child.uri}
                    onClick={(e)=>handleMenuclick(e, child.title)}
                    className={`dropdown-item${router.asPath == child.uri ? ' current-menu-item':''}`}
                    target={child.target}
                    dangerouslySetInnerHTML={{ __html: child.title }}></Link>
                </li>
              ))}
              </Dropdown.Menu>
            </Dropdown>
          )}
          </>
        ) : (
          <Link
            href={item.uri}
            onClick={(e)=>handleMenuclick(e, item.title)}
            className={`nav-link${router.asPath == item.uri ? ' current-menu-item':''}`}
            target={item.target}
            dangerouslySetInnerHTML={{ __html: wrapLinks ? `<span>${item.title}</span>` : item.title }}
          ></Link>
        )}
      </li>
    ))}

    {/* Add Socials in front of menu items */}
    {addSocials && !isFlat && socials && socials.map((item, index)=> (
        // console.log(item)
        <li key={`socials-${item.key}`} className="nav-item mb-30 mb-lg-0">
            <a className="nav-link text-capitalize" href={item.link} target="_blank" title={`Follow on ${item.provider}`}>{item.provider}</a>
        </li>
    ))}

    {children}

    </Nav>
  )
}

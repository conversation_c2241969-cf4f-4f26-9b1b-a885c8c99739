// Wrapper for Next/image
// for easier width/height/alt
import Image from "next/image";

interface Props {
    image: {
        sourceUrl: string
        mediaDetails: {
            sizes: {
                width: number
                height: number
            }[]
            width: number
            height: number
        }
        altText?: string
        title?: string
    }
    alt?: string
    className?: string
    priority?: boolean
    width?: number
    height?: number
}

export default function Img(props: Props) {
    const {image, alt='', className='', priority=false} = props
    const url = image.sourceUrl || false
    if (!url) return

    const width = props.width || (image.mediaDetails?.sizes ? image.mediaDetails?.sizes[0].width : image.mediaDetails.width)
    const height = props.height || (image.mediaDetails?.sizes ? image.mediaDetails?.sizes[0].height : image.mediaDetails.height)
    const altText = alt || image?.altText || image?.title || 'Image alt'

    return (
        <Image
            src={url}
            width={width}
            height={height}
            alt={altText}
            className={className}
            priority={priority}
        />
    )
}
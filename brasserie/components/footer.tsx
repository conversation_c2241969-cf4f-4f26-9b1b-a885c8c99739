import { Col, Container, <PERSON> } from 'react-bootstrap'
import Menu from './menu'
import { useSettingsContext } from './providers/settingsProvider'
import Socials from './socials'
import Image from 'next/image'

export default function Footer(props) {
  const wp = useSettingsContext()
  const menu = props?.menu || wp.menus?.footer
  const title = wp.settings?.general.title || false
  const optGeneral = wp.settings?.acf.optGeneral || false
  const address = optGeneral?.optAddress || false
  const sign =  optGeneral?.optLogoSign?.sourceUrl || false

  return (
    <>
      <footer className="master-footer bg-red text-warm mt-135">
        <Container className='position-relative'>
          {sign && (
              <aside className='footer-brand'>
                {sign && <Image src={sign} width={320} height={150} alt={title} className='swing-sign' />}
              </aside>
          )}
          <h3 className="footer-slogan h1 d-none d-lg-block text-end text-red m-0 me-10">OH LO LO.</h3>
          <Row className='pt-135 pt-lg-30 pb-lg-50'>
            <Col>
              <Socials type="icons" className="d-block d-lg-none text-center text-lg-end" />
            </Col>
          </Row>
          <Row className="content-row text-center text-md-start position-relative mt-lg-30">
            <Col>
              <Menu items={menu} menuClass="footer-nav" itemClass="nav-item" allowDropdowns isFlat addSocials></Menu>
            </Col>
          </Row>
        </Container>

          <div className="bg-green py-20 pb-50 pb-md-0">
            <Container>
               <Row>
                <Col xs={12}>
                  <p className='copyright text-center mb-0'>
                    {address && <span>{address}<br/></span>}
                    <span><a href="//heartwoodcollection.com" target="_blank">Heartwood Collection</a></span>
                    <span className="saint mt-10 d-block">made by <a href="//saintdesign.co.uk" target="_blank">SAINT</a></span>
                  </p>
                </Col>
              </Row>
            </Container>
          </div>

      </footer>

    </>
  )
}

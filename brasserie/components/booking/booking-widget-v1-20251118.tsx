// @ts-nocheck
/**
 * Booking Widget v1 — 2025-11-18
 * - This is the first version of the booking widget, it was used until 2025-11-18.
 */
import { useEffect, useId, useRef, useState } from "react"
import { useSettingsContext } from "../providers/settingsProvider"
import { useRouter } from "next/router"
import {format} from "date-fns"
import { getCookie } from "../../lib/utils"
import { UTM_COOKIENAME } from "../../lib/constants"
import Calendar from 'react-calendar'
import 'react-calendar/dist/Calendar.css';
import { Dropdown } from "react-bootstrap"
import GuestlineBridge from "./guestline-bridge"
import Link from "next/link"
import { scrollTo } from 'seamless-scroll-polyfill';
import BookingWidgetTreats from "./booking-treats"

export default function BookingWidgetV1(props) {
    const {settings} = useSettingsContext(),
        bookingJson = props.settings,
        gtm = settings?.acf.optGeneral.optGtm || false,
        pixel = settings?.acf.optGeneral.optPixel || false,
        brand = settings?.acf.optGeneral.optBrand || false,
        pubLocation = settings?.general.title || false,
        widgetRef = useRef(),
        locationRef = useRef(),
        TimeDropdownRef = useRef(),
        page1 = useRef(),
        page2 = useRef(),
        page3 = useRef(),
        router = useRouter(),
        restApiZonal = bookingJson.restApi+'/zonal/v1',
        restApiAtreemo = bookingJson.restApi+'/atreemo/v1',
        restSecret = process.env.NEXT_PUBLIC_WORDPRESS_PREVIEW_SECRET,
        promocodeDates = [
            '2022-02-03',
            '2022-02-10',
            '2022-02-17',
            '2022-02-24',
            '2022-03-03',
            '2022-03-10',
            '2022-03-17',
            '2022-03-24'
        ],
        excludedOccasions = bookingJson.excludedOccasions

    const [GuestlineApi, setGuestlineApi] = useState(null)

    let utm_cookie
    let {utm_source, utm_medium, utm_campaign, utm_term, utm_content, promocode} = router.query

    const {transaction_id, hotel_id, layout} = router.query

    if( hotel_id ) {
        // --- alter default location from "hotel_id" query
        bookingJson.locationOptions.filter((item)=>{
            return item.hotelid === hotel_id
        }).map((item)=>{
            bookingJson.defaultLocation = item.zonalId
        })
    }
    // console.log(transaction_id, hotel_id, bookingJson)

    const [EnquiryInfo, setEnquiryInfo] = useState(null)
    const [ConfirmationInfo, setConfirmationInfo] = useState(null)

    let customBookingData = {
        date: bookingJson.booking_date ? new Date(bookingJson.booking_date) : false,
        time: bookingJson.booking_time || false,
        areaId: bookingJson.booking_areaId || false,
        menuIds: bookingJson.booking_menuIds || false
    }

    // const customBookingData = {
    //     date: new Date('2024-10-30'),
    //     time: '17:00',
    //     areaId: '979eb332-2080-4bd2-b333-d0ab8dd8cd95',
    //     menuIds: 'feb4bcd2-7378-48b1-b43a-0367fd19ab56'
    // }
    // console.log(customBookingData)

    const getBookingObject = ()=>{
        return {
            bookingReference: null,
            siteId: bookingJson.defaultLocation || 0,
            occasionId: '',
            menuIds: '',
            areaId: '',
            upsellIds: [],
            adults: 0,
            children: 0,
            date: '',
            time: '',
            firstname: '',
            lastname: '',
            emailAddress: '',
            telephoneNumber: '',
            consent: {
                email: false,
                sms: false
            }
        }
    }
    const [BookingData, setBookingData] = useState(getBookingObject)
    const getStateObject = ()=>{
        const today = new Date()
        console.log( today.getFullYear(), today.getMonth(), today.getDate() )
        return{
            step: 1,
            status: 'book',
            loading: true,
            appMessage: null,
            appMessageButtons: false,

            restID: false,
            restUrl: false,
            restName: '',
            restPhone: '',
            restEmail: '',
            restAtreemoId: false,
            restBookingType: bookingJson.restBookingType || 'standard', // [standard, events]
            restOccasionId: false,
            restOccasionName: false,
            restOccasionShow: bookingJson.restBookingType == 'events' ? true : false,
            restMenuName: false,
            restMenuShow: true,
            restAreaId: false,
            restAreaName: false,
            restAreaShow: true,
            restUpsellShow: true,

            restSessions: false,
            restTimes: null,
            restOccasions: null,
            restMenus: null,
            restAreas: null,
            restUpsells: null,

            defaultSizeRange: 12,
            minPartySize: false,
            maxPartySize: false,

            minDate: today,
            minActiveDate: new Date(today.getFullYear(), today.getMonth(), today.getDate()),
            maxDate: null,

            DateMode: 'single',

            Date: '',
            slotDate: null,
            slotDateFormated: null,
            Time: 0,

            slotType: 'enquiry',
            availableTimeslots: 0,
            menuId: null,

            // --- Facebook Pixel
            pixelEventPrefix: bookingJson.restBookingType == 'events' ? 'Enquiry:' : 'Book table:',

            usePromocodes: false,
            promocode: null
        }
    }
    const [state, setState] = useState(getStateObject)

    // ================ Utilities =================

    function scrollBookingTop() {
        // const el = document.getElementById('zonalevents-app')
        let el = widgetRef.current
        //  get closest parent with class .booking-wrapper, if found use this element to scroll to
        const bookingWrapper = el.closest('.booking-wrapper')
        if (bookingWrapper) el = bookingWrapper
        console.log('scrolling to: ' + el.offsetTop)
        if(el) scrollTo(window, { behavior: "smooth", top: el.offsetTop - 180 })
    }

    function clearErrors( currentStep ){
        if(!widgetRef.current) return false
        const   stepCtn = widgetRef.current?.querySelector('#zonalevents_page_' + currentStep),
                inputs = stepCtn?.querySelectorAll('.input-error');
        if( inputs ) inputs.forEach((item)=>{ item.classList.remove('input-error') })
    }

    function valid_step1(){
        return (BookingData.adults && state.Date && state.Time && BookingData.menuIds) ? true : false;
    }

    function validEmail(email){
        var re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        return re.test(email);
    }

    function validateField( field ){
        if( typeof field === "undefined" ) return false;
        // console.log('start validate field');
        let val = field.value;
        if( field.getAttribute('type') == 'radio' ) {
            let name = field.getAttribute('name'),
                is_radio_valid = 0,
                radios = widgetRef.current?.querySelectorAll('[name="'+name+'"]');
                // console.log(radios);
            if( radios ) {
                radios.forEach(function(_this){
                    if( _this.checked ) is_radio_valid++;
                })
                if( is_radio_valid ) {
                    field.closest('.form-group').classList.remove('input-error');
                    return true;
                }else {
                    // console.log('radio group NOT valid');
                    field.closest('.form-group').classList.add('input-error');
                    return false;
                }
            }else {
                return true;
            }
        }else if( !val || val == 0 || val == 'null' ) {
            field.closest('.form-group').classList.add('input-error');
            return false;
        }else {

            if( field.getAttribute('type') == 'email' && !validEmail( val ) ){
                field.closest('.form-group').classList.add('input-error')
                return false;
            }else {
                field.closest('.form-group').classList.remove('input-error');
                return true;
            }

        }
    }

    function getActiveDateFormatted(activeDate) {
        return new Date(activeDate.getFullYear(), activeDate.getMonth(), activeDate.getDate())
    }

    const onActiveStartDateChangeHandler = ({ activeStartDate, value, view }) => {
        console.log("vv:", activeStartDate, value, view);
        setState({...state, minActiveDate: activeStartDate})
        console.log("state:", state.minActiveDate)
      };

    function validateStep( currentStep ){
        if( !widgetRef.current ) return false
        const   stepCtn = widgetRef.current?.querySelector('#zonalevents_page_' + currentStep),
                inputs = stepCtn?.querySelectorAll('[required]');
        let     found_invalid = 0;

        // --- clear errors
        clearErrors( currentStep );

        // --- check inputs
        if( inputs ) {
            inputs.forEach(function(item){
                let is_valid = validateField( item );
                if( !is_valid ) found_invalid++;
            })
        }

        // --- Check time dropdown
        if( currentStep == '1' && !state.Time  ) {
            found_invalid++;
            widgetRef.current.querySelector('#zonalevents_booking_time').closest('.form-group').classList.add('input-error');
        }

        return found_invalid ? false : true;
    }

    function checkPromoConditions(){
        var newDate = new Date(state.slotDate).getTime(),
            foundMatch = 0,
            promoCheckVal;

        var minTimeslot = new Date(),
            bookingTimeslot = new Date(),
            dateArray = state.Time.time.split(":");

        minTimeslot.setHours(18,0);
        bookingTimeslot.setHours(dateArray[0],dateArray[1]);

        for( var i=0; i < promocodeDates.length; i++ ) {
            var dStart = new Date(promocodeDates[i]).getTime() - 20*60*60*1000,
                dEnd = new Date(promocodeDates[i]).getTime() + 20*60*60*1000;
            if( newDate > dStart && newDate < dEnd ) foundMatch++;
        }

        promoCheckVal = state.usePromocodes && state.promocode && state.promocode.toLowerCase() == 'apresski' && newDate && foundMatch && BookingData.adults > 1 && bookingTimeslot >= minTimeslot;

        console.log( bookingTimeslot, minTimeslot, bookingTimeslot >= minTimeslot );
        console.log(newDate, BookingData.adults, state.usePromocodes, state.promocode, foundMatch, promoCheckVal);

        return promoCheckVal;
    }

    function checkPromoMessageConditions(){
        var newDate = new Date(state.slotDate).getTime(),
        foundMatch = 0,
        promoCheckVal;

        for( var i=0; i < promocodeDates.length; i++ ) {
            var dStart = new Date(promocodeDates[i]).getTime() - 20*60*60*1000,
                dEnd = new Date(promocodeDates[i]).getTime() + 20*60*60*1000;
            if( newDate > dStart && newDate < dEnd ) foundMatch++;
        }

        promoCheckVal = state.usePromocodes && newDate && foundMatch && BookingData.adults > 1;

        console.log(newDate, BookingData.adults, state.usePromocodes, foundMatch, promoCheckVal);

        return promoCheckVal;
    }

    const goToStep = (nextStep)=>{
        console.log('going to step: ' + nextStep);
        if( typeof nextStep === "undefined" ) return false;
        setState({...state, step: nextStep})

        // === Check if we have a valid PromoCode and all conditions, disaply Notification
        if( state.promocode && nextStep == 2 && checkPromoConditions() ) {
            setState({...state,
                loading: true,
                appMessage: '<strong style="text-transform:uppercase;">'+ state.promocode +'</strong> promotion has been applied to your booking'
            })
        }
        // === Check if we have no Promocode actions but we still want to display promo message
        // === this is for no promocode campaign route
        if( !state.promocode && nextStep == 2 && checkPromoMessageConditions() ) {
            setState({...state,
                loading: true,
                appMessage: 'We host Après ski evenings from 6pm on Thursdays in our cosy tent. <br>Please call '+ state.restPhone +' to book so we can seat you accordingly.'
            })
        }

        // --- Scroll widget into view
        console.log('scrolling to: ' + nextStep)
        scrollBookingTop()

        // --- FB Pixel
        if( typeof fbq === 'function' && nextStep == 2 ) {
            console.log(state.pixelEventPrefix+' Progress');
            fbq('trackSingleCustom', pixel, state.pixelEventPrefix+' Progress');
        }
    }

    const validateAndGoto = (currentStep, nextStep)=>{
        if( validateStep(currentStep) ) goToStep(nextStep)
    }

    function clearBookingForm(){
        console.log('clearBookingForm method fired');
        setBookingData({...BookingData,
            adults:0
        })
        let localState = {
            Date: new Date(),
            Time: 0,
            slotDate: null,
            slotDateFormated: null,
            slotType: 'enquiry',
            restTimes: null,
            loading: false,
            appMessage: null,
            appMessageButtons: false
        }
        if( customBookingData?.date ) {
            localState.Date = customBookingData.date
            localState.slotDate = format(new Date(customBookingData.date), "yyyy-MM-dd")
            localState.slotDateFormated = format(new Date(customBookingData?.date), "do MMMM Y")
        }

        setState({...state, ...localState})

    }

    function setRestaurantInfo( option ){
        console.log('setRestaurantInfo fired!')
        const minDate = option.dataset.mindate || null
        let localState = {
            restID: option.dataset.id,
            restUrl: option.dataset.url,
            restName: option.dataset.name.replace('&amp;','&'),
            restPhone: option.dataset.phone,
            restEmail: option.dataset.email,
            restAtreemoId: option.dataset.atreemoid,
            restOccasionId: option.dataset.occasionid,
            menuId: option.dataset.menuid,
            restAreaId: option.dataset.areaid,
            restMenuShow: option.dataset.menushow == 1 ? true : false,
            restAreaShow: option.dataset.areashow == 1 ? true : false,
            restUpsellShow: option.dataset.upsellshow == 1 ? true : false,
            usePromocodes: option.dataset.usepromocodes == 1 ? true : false,
            restMenus: false,
            restAreas: false,
            restUpsells: false,
            loading: false,
            restOccasions: null,
            restOccasionName: false,
            restAreaName: false,
            restMenuName: false,
            restTimes: null,
            Time: 0,
            Date: '',
            maxDate: option.dataset.maxdate ? new Date(option.dataset.maxdate) : null
        }

        localState.minDate = minDate ? new Date(minDate) : new Date()
        localState.minActiveDate = minDate ? new Date(minDate) : new Date()
        // localState.Date = minDate ? new Date(minDate) : new Date()
        // localState.slotDate = minDate ? format(new Date(minDate), "yyyy-MM-dd") : format(new Date(), "yyyy-MM-dd")
        // console.log(option.dataset.mindate, localState.minDate, localState.minActiveDate)

        if( customBookingData?.date ) {
            localState.Date = customBookingData.date
            localState.slotDate = format(new Date(customBookingData.date), "yyyy-MM-dd")
            localState.slotDateFormated = format(new Date(customBookingData?.date), "do MMMM Y")
        }

        if( state.status == 'book'  ) {
            localState.step = 1
            clearBookingForm()
        }

        setState({...state,
            ...localState
        })

        if( transaction_id &&
            option.dataset.glapiurl && option.dataset.glapipass && option.dataset.glapioperator && option.dataset.glapiinterface ) {
            console.log('%cRezLynx API: Location details:',"color:green", option.dataset)
            setGuestlineApi({
                glApiUrl: option.dataset.glapiurl,
                glApiPass: option.dataset.glapipass,
                glApiOperator: option.dataset.glapioperator,
                glApiInterface: option.dataset.glapiinterface
            })
        }

        setEnquiryInfo(option.dataset.enquiryinfo)
        setConfirmationInfo(option.dataset.confirmationinfo)

    }

    async function getOccasions(){
        if( !(BookingData.siteId && state.Date && restSecret) ) return false
        console.log('> getOccasions method fired...');
        // --- Turn Loader ON
        setState({...state,
            loading: true,
            appMessage: null
        })

        const postdata = new URLSearchParams()
        postdata.append('siteId', BookingData.siteId)
        postdata.append('date_mode', state.DateMode)
        postdata.append('booking_date', format( new Date(state.Date), 'yyyy-MM-dd'))
        postdata.append('api_secret', restSecret) // add secret to auth api call

        const res = await fetch(restApiZonal+'/occasions', {
            method: 'POST',
            body: postdata
        })
        if (!res.ok) {
            setState({...state, loading: false})
            // This will activate the closest `error.js` Error Boundary
            throw new Error('Failed to fetch data')
        }
        let response = await res.json()
        console.log(response);

        if( response.StatusCode == 200 && response.data.occasions ) {
            if( state.restBookingType == 'events' ) {
                // --- Remove 'Restaurant table' (default occasion) from response
                let temp_restOccasions = response.data.occasions.filter(function(el){
                    return el.id.toString() == state.restOccasionId.toString() ? false : true;
                });

                // === Allow hiding occasions based on ID (2 fields, name + ID)
                if( excludedOccasions ) {
                    temp_restOccasions = temp_restOccasions.filter(function(el){
                        return excludedOccasions.indexOf( el.id.toString() ) != -1 ? false : true;
                    });
                }

                setState({...state, restOccasions: temp_restOccasions})
                // --- reset selected occasion
                setBookingData({...BookingData, occasionId: '', loading: false})
            }else {
                console.log('Standard booking occasion update...')
                // ---TODO: Set default Occasion (when applicable)
                if( state.restOccasionId ) setBookingData({...BookingData, occasionId: state.restOccasionId})
                // --- Update Occasion Name
                let temp_occasionName = ''
                if( response.data.occasions ) {
                    response.data.occasions.forEach(function(item){
                        if( state.restOccasionId == item.id ) {
                            console.log('getOccasions: ' + item.name)
                            temp_occasionName = item.name
                        }
                    })
                }
                setState({...state,
                    restOccasions: response.data.occasions,
                    restOccasionName: temp_occasionName,
                    loading: false})
            }
        }else {
            setState({...state,
                loading: true,
                appMessage: 'No available Occasions found'
            })
        }
    }

    async function getRestrictions(){
        if( !(BookingData.siteId && BookingData.occasionId && restSecret) ) return false
        console.log('> getRestrictions method fired...')
        // --- Turn Loader ON
        setState({...state,
            loading: true,
            appMessage: null
        })

        const postdata = new URLSearchParams()
        postdata.append('siteId', BookingData.siteId)
        postdata.append('occasionId', BookingData.occasionId)
        postdata.append('api_secret', restSecret) // add secret to auth api call

        const res = await fetch(restApiZonal+'/restrictions', {
            method: 'POST',
            body: postdata
        })
        if (!res.ok) {
            setState({...state, loading: false})
            // This will activate the closest `error.js` Error Boundary
            throw new Error('Failed to fetch data')
        }
        let response = await res.json()

        console.log(response);
        if( response.StatusCode == 200 && response.data.minCovers && response.data.maxCovers ) {
            setState({...state,
                minPartySize: response.data.minCovers,
                maxPartySize: response.data.maxCovers,
                loading: false
            })
            // if( response.data.minCovers <= 2 )  setBookingData({...BookingData, adults: 2})
        }else {
            setState({...state,
                loading: true,
                appMessage: 'No available Restrictions found'
            })
        }
    }

    async function getAreas(){
        if( !(BookingData.siteId && BookingData.occasionId && state.slotDate && BookingData.adults && restSecret) ) return false
        console.log('> getAreas method fired...')
        // --- Turn Loader ON
        // setState({...state,
        //     loading: true,
        //     appMessage: null
        // })

        const postdata = new URLSearchParams()
        postdata.append('siteId', BookingData.siteId)
        postdata.append('occasionId', BookingData.occasionId)
        postdata.append('dates', state.slotDate)
        postdata.append('adults', BookingData.adults)
        postdata.append('api_secret', restSecret) // add secret to auth api call

        const res = await fetch(restApiZonal+'/areas', {
            method: 'POST',
            body: postdata
        })
        if (!res.ok) {
            setState({...state, loading: false})
            // This will activate the closest `error.js` Error Boundary
            throw new Error('Failed to fetch data')
        }
        let response = await res.json()

        console.log(BookingData.siteId, BookingData.occasionId, BookingData.adults, state.slotDate);
        console.log(response);
        if( response.StatusCode == 200 && response.data.areas ) {
            // --- filter only available items (item.available==true)
            let filteredArray = response.data.areas.filter(function(el){
                return el.available;
            });
            console.log(filteredArray)

            setState({...state,
                restAreas: filteredArray,
                restAreaName: false,
                loading: false
            })
            // --- default Area
            if( customBookingData?.areaId ) setBookingData({...BookingData, areaId: customBookingData.areaId})
        }else {
            setState({...state,
                loading: true,
                appMessage: 'No available Areas found'
            })
        }
    }

    async function getSlots(){
        if( !(BookingData.siteId && BookingData.occasionId && state.Date && BookingData.adults && BookingData.areaId && restSecret) ) return false
        console.log('> getSlots method fired...')

        setState({...state,
            loading: true,
            availableTimeslots: 0,
            appMessage: null,
            Time: 0
        })

        const postdata = new URLSearchParams()
        postdata.append('rest_code', BookingData.siteId)
        postdata.append('occasionId', BookingData.occasionId)
        postdata.append('date_mode', state.DateMode)
        postdata.append('booking_date', format(new Date(state.Date), 'yyyy-MM-dd'))
        postdata.append('party_size', BookingData.adults)
        postdata.append('area_id', BookingData.areaId)
        postdata.append('api_secret', restSecret) // add secret to auth api call

        const res = await fetch(restApiZonal+'/slots', {
            method: 'POST',
            body: postdata,
            next: { revalidate: 10 }
        })
        if (!res.ok) {
            setState({...state, loading: false})
            // This will activate the closest `error.js` Error Boundary
            throw new Error('Failed to fetch data')
        }
        let response = await res.json()

        console.log(response)
        if( response.StatusCode == 200 ) {

            // --- update availabletimeslots count
            let  availableTimeslots = 0
            let temp_restTimes = []
            let defaultTimeSlot = null

            if( response.data.dates.length > 0 ) {
                response.data.dates.forEach(function(item){
                    let sessions = [
                        {
                            name: 'Breakfast',
                            times: []
                        },
                        {
                            name: 'Lunch',
                            times: []
                        },
                        {
                            name: 'Dinner',
                            times: []
                        }
                    ];
                    // --- separate into sessions
                    if( item.times.length > 0 ){
                        availableTimeslots++;
                        item.times.forEach(function(timeItem){
                            // --- trim time (remove seconds if applicable)
                            let timeSlot = {
                                time: timeItem.time.split(':').length > 2 ? timeItem.time.substr(0,5) : timeItem.time,
                                available: timeItem.availabilities[0].available,
                                canEnquire: timeItem.availabilities[0].canEnquire,
                                isClosed: timeItem.availabilities[0].isClosed
                            };
                            if( timeItem.time < '12:00' ) sessions[0].times.push(timeSlot);
                            if( timeItem.time > '11:59' && timeItem.time < '17:00' ) sessions[1].times.push(timeSlot);
                            if( timeItem.time > '16:59' ) sessions[2].times.push(timeSlot);

                            if( timeSlot.time == customBookingData.time ) defaultTimeSlot = timeSlot
                        });

                        temp_restTimes.push({
                            date: item.date.split('T')[0],
                            sessions: sessions
                        });
                        console.log( temp_restTimes )
                    }
                })

            }

            // --- display message when no Timeslots available
            if( availableTimeslots > 0 ) {

                let localState = {
                    restTimes: temp_restTimes,
                    Time: 0,
                    availableTimeslots: availableTimeslots,
                    loading: false
                }

                if( defaultTimeSlot ) {
                    const slotType =  state.restBookingType == 'events' || ( !defaultTimeSlot.isClosed && !defaultTimeSlot.available && defaultTimeSlot.canEnquire ) ? 'enquiry' : 'booking'
                    // console.log('.....' + slotType)
                    localState.Time = defaultTimeSlot
                    localState.slotType = slotType
                }

                setState({...state, ...localState})

            }else{
                // --- default message
                console.log('NO timeslots available')
                setState({...state,
                    loading: true,
                    appMessage: 'If you cannot find the availability you are looking for, please contact us by <a href="mailto:'+ state.restEmail +'" style="word-break: break-word;">' + state.restEmail + '</a>.'})

                // === GA: // New Booking, timeslots selection not available
                if( typeof ga === 'function' ) {
                    ga('send', 'event', 'create_booking', 'date_session_not_available', 'error');
                    ga('brasserie.send', 'event', 'create_booking', 'date_session_not_available', 'error');
                }

                // === Facebook Pixel
                if( typeof fbq === 'function' ) {
                    console.log(state.pixelEventPrefix+' Abandoned cart');
                    fbq('trackSingleCustom', pixel, state.pixelEventPrefix + ' Abandoned cart');
                }
            }

        }else {
            setState({...state,
                loading: true,
                appMessage: 'No slots available. Please select a different date.'
            })
        }
    }

    async function getMenus(){
        if( !(BookingData.siteId && BookingData.occasionId && state.slotDate && BookingData.adults && state.Time.time && restSecret) ) return false
        console.log('> getMenus method fired...')

        // --- Turn Loader ON
        setState({...state,
            loading: true,
            appMessage: null
        })

        const postdata = new URLSearchParams()
        postdata.append('siteId', BookingData.siteId)
        postdata.append('occasionId', BookingData.occasionId)
        postdata.append('adults', BookingData.adults)
        postdata.append('dates', state.slotDate)
        postdata.append('time', state.Time.time)
        postdata.append('api_secret', restSecret) // add secret to auth api call

        const res = await fetch(restApiZonal+'/menus', {
            method: 'POST',
            body: postdata
        })
        if (!res.ok) {
            setState({...state, loading: false})
            // This will activate the closest `error.js` Error Boundary
            throw new Error('Failed to fetch data')
        }
        let response = await res.json()

        console.log(response);
        if( response.StatusCode == 200 && response.data.menus ) {
            let filteredArray = response.data.menus.filter(function(el){
                return el.available;
            })
            console.log(filteredArray)
            let defaultId = state.menuId;
            if( customBookingData?.menuIds ) {
                setBookingData({...BookingData, menuIds: customBookingData.menuIds})
            }else if( defaultId ) {
                setBookingData({...BookingData, menuIds: defaultId})
            }

            // --- Update Menu Name
            let temp_restMenuName = false
            if( filteredArray ) {
                filteredArray.forEach(function(item){
                    if( BookingData.menuIds == item.id ) temp_restMenuName = item.name
                });
            }
            // --- Get Restaurant Upsells
            getUpsells(filteredArray, temp_restMenuName)
        }else {
            setState({...state,
                loading: true,
                appMessage: 'No available Menus found'
            })
        }
    }

    async function getUpsells(restMenus, restMenuName){
        if( !(BookingData.siteId && BookingData.occasionId && state.slotDate && BookingData.adults && state.Time.time && restSecret) ) return false
        console.log('> getUpsells method fired...')

        // --- Turn Loader ON
        setState({...state,
            loading: true,
            appMessage: null
        })

        const postdata = new URLSearchParams()
        postdata.append('siteId', BookingData.siteId)
        postdata.append('occasionId', BookingData.occasionId)
        postdata.append('adults', BookingData.adults)
        postdata.append('dates', state.slotDate)
        postdata.append('time', state.Time.time)
        postdata.append('api_secret', restSecret) // add secret to auth api call

        const res = await fetch(restApiZonal+'/upsells', {
            method: 'POST',
            body: postdata
        })
        if (!res.ok) {
            setState({...state, loading: false})
            // This will activate the closest `error.js` Error Boundary
            throw new Error('Failed to fetch data')
        }
        let response = await res.json()

        console.log(response);
        if( response.StatusCode == 200 && response.data.upsells ) {
            // --- filter only available items (item.available==true)
            let filteredArray;

            if( response.data.upsells.length ) {
                filteredArray = response.data.upsells.filter(function(el){
                    return el.available;
                });
            }
            console.log(filteredArray);
            setState({...state,
                restUpsells: filteredArray,
                restMenus: restMenus,
                restMenuName: restMenuName,
                loading: false
            })
            console.log('Menus and Upsells has been updated...');
        }else {
            setState({...state,
                loading: true,
                restMenus: restMenus,
                restMenuName: restMenuName,
                appMessage: 'No available Upsells found'
            })
        }
    }

    async function addBooking(){
        console.log('start Add Booking');

        if( validateStep(2) ) {

            let upsellIds = [];
            console.log('validation OK');

            if( typeof fbq === 'function' ) {
                console.log(state.pixelEventPrefix+' Create');
                fbq('trackSingleCustom', pixel, state.pixelEventPrefix + ' Create');
            }

            // --- Turn Loader ON
            setState({...state,
                loading: true,
                appMessage: null
            })

            // --- Prepare Upsells Id's array to be sent
            if( BookingData.upsellIds && BookingData.upsellIds.length ) {
                BookingData.upsellIds.forEach(function(item){
                    upsellIds.push(item.id);
                });
                upsellIds = upsellIds.join();
            }

            const postdata = new URLSearchParams()
            postdata.append('siteId', BookingData.siteId)
            postdata.append('occasionId', BookingData.occasionId)
            postdata.append('menuIds', BookingData.menuIds)
            postdata.append('areaId', BookingData.areaId)
            postdata.append('upsellIds', upsellIds)
            postdata.append('date', state.slotDate)
            postdata.append('time', state.Time.time)
            postdata.append('adults', BookingData.adults)
            postdata.append('children', BookingData.children)
            postdata.append('firstname', BookingData.firstname)
            postdata.append('lastname', BookingData.lastname)
            postdata.append('emailAddress', BookingData.emailAddress)
            postdata.append('telephoneNumber', BookingData.telephoneNumber)
            postdata.append('EmailOptIn', BookingData.consent.email)
            postdata.append('SmsOptIn', BookingData.consent.sms)
            postdata.append('promocode', checkPromoConditions() ? state.promocode : null)
            postdata.append('api_secret', restSecret) // add secret to auth api call

            const res = await fetch(restApiZonal+'/'+state.slotType, {
                method: 'POST',
                body: postdata,
                next: { revalidate: 10 }
            })
            if (!res.ok) {
                setState({...state, loading: false})
                // This will activate the closest `error.js` Error Boundary
                throw new Error('Failed to fetch data')
            }
            let data = await res.json()

            console.log('ajax call success');
            console.log(data);

            if( data.response.StatusCode == 200 ) {

                // --- set restSessions
                console.log('Booking confirmed');

                setBookingData(data.response.data)
                console.log( data.response.data )

                setState({...state,
                    status: 'booked',
                    step: 3,
                    loading: false
                })

                scrollBookingTop()

                // === GA: New Booking, after booking reservation -> success
                if( typeof ga === 'function' ) {
                    ga('send', 'event', 'create_booking', 'success_booking', 'covers', data.response.data.adults);
                    ga('brasserie.send', 'event', 'create_booking', 'success_booking', 'covers', data.response.data.adults);
                }

                // === FB Pixel
                if( typeof fbq === 'function' ) {
                    console.log(state.pixelEventPrefix+' PartySize Change: ' + data.response.data.adults);
                    fbq('trackSingleCustom', pixel, state.pixelEventPrefix + ' PartySize Change', { value: data.response.data.adults });

                    console.log(state.pixelEventPrefix+' Success');
                    fbq('trackSingleCustom', pixel, state.pixelEventPrefix + ' Success');
                }

                // === GTM
                if( window ) {
                    console.log(brand, pubLocation)
                    window.dataLayer = window.dataLayer || []
                    window.dataLayer.push({
                        event: "generate_lead",
                        pub_location: pubLocation,
                        form_type: "Book a table"
                    })
                }

                // --- Add Attreemo contact if UTM campaign url parameters available
                if( state.restAtreemoId ) {
                    console.log('Atreemo: start call')
                    await addToAtreemo(data.response.data)
                }

            }else {
                setState({...state,
                    loading: true,
                    appMessage: data.response.data.message
                })
            }

        }else {
            scrollBookingTop()
        }
    }

    async function addToAtreemo(booking_data){

        const postdata = new URLSearchParams()
        postdata.append('firstname', BookingData.firstname)
        postdata.append('surname', BookingData.lastname)
        postdata.append('email', BookingData.emailAddress)
        postdata.append('venueRef', state.restAtreemoId)
        postdata.append('EmailOptIn', BookingData.consent.email)
        postdata.append('SmsOptIn', BookingData.consent.sms)
        postdata.append('MobilPhone', BookingData.telephoneNumber)
        postdata.append('bookingId', booking_data.bookingReference)
        postdata.append('bookingOccassion', state.restOccasionName)
        postdata.append('bookingArea', state.restAreaName)
        postdata.append('bookingMenu', state.restMenuName)
        postdata.append('bookingType', state.restBookingType == 'standard' ? 'Booking' : 'Enquiry')
        postdata.append('restName', state.restName)
        postdata.append('bookingDate', state.slotDate)
        postdata.append('bookingTime', state.Time.time)
        postdata.append('api_secret', restSecret) // add secret to auth api call

        utm_cookie = getCookie(UTM_COOKIENAME) ? JSON.parse(getCookie(UTM_COOKIENAME)) : false
        console.log(utm_source, utm_cookie)
        if( utm_cookie && !utm_source ) {
            console.log('utm_cookie fallback...')
            utm_source = utm_cookie.utm_source || null
            utm_medium = utm_cookie.utm_medium || null
            utm_campaign = utm_cookie.utm_campaign || null
            utm_term = utm_cookie.utm_term || null
            utm_content = utm_cookie.utm_content || null
            promocode = utm_cookie.promocode || null
        }

        if(utm_source) postdata.append('utmSource', utm_source)
        if(utm_medium) postdata.append('utmMedium', utm_medium)
        if(utm_campaign) postdata.append('utmCampaign', utm_campaign)
        if(utm_term) postdata.append('utmTerm', utm_term)
        if(utm_content) postdata.append('utmContent', utm_content)
        if(promocode) postdata.append('promocode', promocode)

        // --- Guestline booking ID
        if( transaction_id ) postdata.append('transaction_id', transaction_id)

        // --- Custom Atreemo SourceID
        if( bookingJson?.atreemoOrigin ) {
            console.log("Found custom SourceID: " + bookingJson.atreemoOrigin)
            postdata.append('origin', bookingJson.atreemoOrigin)
        }

        const res = await fetch(restApiAtreemo+'/add', {
            method: 'POST',
            body: postdata
        })
        if (!res.ok) {
            setState({...state, loading: false})
            // This will activate the closest `error.js` Error Boundary
            throw new Error('Failed to fetch data')
        }
        let data = await res.json()

        if( data.code == 200 || data.code == 201 ){
            console.log(data)
        }else{
            console.log('Atreemo newsletter signup: something went wrong on ajax call')
        }
    }

    // ======== Callbacks ========

    const changeSlotDateTime = (e, val, newDate, newType)=>{
        let target = e.target

        if( val === state.Time ) return false;
        console.log(`slotDateTime changed:`, target, val, newDate, format(new Date(newDate), "do MMMM y"), newType)
        if( newDate ) {
            setState({...state,
                Time: val,
                slotDate: format(new Date(newDate), "yyyy-MM-dd"),
                slotDateFormated: format(new Date(newDate), "do MMMM y"),
                slotType: newType
            })
        }
    }

    function upsellIdsChange(target, upsell){
        console.log(`upsellIds changed: ${target.checked}, ${upsell}`)
        let tempArray = BookingData.upsellIds
        if( target.checked ) {
            tempArray.push(upsell)
        }else {
            tempArray.splice(BookingData.upsellIds.indexOf(upsell), 1)
        }
        setBookingData({...BookingData, upsellIds: tempArray})
    }

    function updateAreas(){
        console.log('updateAreas method fired')

        setState({...state,
            loading: false,
            appMessageButtons: false,
            appMessage: null
        })

        if( BookingData.occasionId && BookingData.adults && state.Date ) {
            // --- Get Restaurant Upsells
            getAreas()
        }
    }

    function updateMenusUpsells(){
        console.log('updateMenusUpsells method fired')

        setState({...state,
            loading: false,
            appMessageButtons: false
        })

        if( BookingData.occasionId && BookingData.adults && state.Date && state.Time ) {
            // --- Get Restaurant Menus
            getMenus()
        }
    }

    function isPastTime(newDate){
        var today = new Date(),
            compareDate = new Date(newDate),
            date = today.getFullYear() + "-" + (String(today.getMonth()+1).padStart(2, "0")) + "-" + today.getDate()
        return today.getTime() > compareDate.getTime()
    }

    const TimeDropdownHandle = ()=>{
        const dropdownMenu = TimeDropdownRef.current.querySelector('.dropdown-menu')

        // console.log('scroll me into view please...');
        const pos = {top: dropdownMenu.closest('.form-group').offsetTop, left: dropdownMenu.closest('.form-group').offsetLeft},
              header = document.querySelector('.header'),
              offset = header ? header.style.clientHeight : 0

        console.log(pos, offset)
        // }
    }

    const requiredInputChange = (e)=>{
        validateField( e.target )
    }

    // =========== Watchers ==============
    useEffect(()=>{
        console.log(`siteId changed: ${BookingData.siteId}`)

        const option = widgetRef.current?.querySelector('option[value="'+ BookingData.siteId +'"]');
        if( transaction_id && option.dataset.roomsoccasionid ) {
            option.dataset.occasionid = option.dataset.roomsoccasionid
            console.log('TEST', option.dataset.occasionid)
        }
        // console.log(newData, option);
        if( BookingData.siteId && option ) {
            setRestaurantInfo( option )
            // === FB Pixel
            if( typeof fbq === 'function' && state.step == 1 ) {
                console.log(state.pixelEventPrefix+' Location change: ' + state.restName);
                fbq('trackSingleCustom', pixel, state.pixelEventPrefix + ' Location change', { value: state.restName });
            }
        }else {
            console.log('Location: nothing to change');
        }
    },[BookingData.siteId])

    useEffect(()=>{
        console.log(`Date changed: ${state.Date}`)
        if( state.Date ) {
            setBookingData({...BookingData,
                adults: 0,
                areaId: ''
            })
            setState({...state,
                restOccasions: null,
                restOccasionName: false,
                restAreas: null,
                restAreaName: false,
                restMenus: null,
                restMenuName: false,
                restUpsells: null,
                restTimes: null,
                Time: 0
            })
            // --- Get Restaurant occasions
            // if( state.restBookingType == 'standard' )
            // getOccasions()
            // === GA: New booking, on date pick
            if( typeof ga === 'function' ) {
                ga('send', 'event', 'create_booking', 'pick_date', 'pick')
                ga('brasserie.send', 'event', 'create_booking', 'pick_date', 'pick')
            }
        }
    },[state.Date])

    useEffect(()=>{
        if( !state.restOccasions ){
            getOccasions()
        }
    },[state.Date, state.restOccasions])

    useEffect(()=>{
        console.log(`restOccasionName changed: ${state.restOccasionName}`)
        if( state.restOccasionName && BookingData.occasionId ) {
            // --- Update Menus, Areas and Upsells dropdowns
            console.log('Update Restrictions...');
            getRestrictions()
        }
    },[state.restOccasionName])

    useEffect(()=>{
        console.log(`occasionId changed: ${BookingData.occasionId}`)
        if( BookingData.occasionId ) {
            // --- Update selected Occasion Name
            if( state.restOccasions ) {
                state.restOccasions.forEach(function(item){
                    if( BookingData.occasionId == item.id ) {
                        console.log('BookingData.occasionId: ' + item.name)
                        setState({...state,
                            restOccasionName: item.name,
                            restAreaName: false,
                            restAreas: null,
                            restMenus: null,
                            restMenuName: false,
                            restUpsells: null,
                            restTimes: null,
                            Time: 0
                        })
                    }
                })
            }
            // --- Update Partysize dropdown
            setBookingData({...BookingData, adults: 0, areaId: ''})
        }
    },[BookingData.occasionId])

    useEffect(()=>{
        console.log('Party size changed...')
        if( BookingData.adults ) {
            if( BookingData.adults > state.maxPartySize  ) {
                setState({...state,
                    loading: true,
                    appMessage: 'Looking to book a large table or party, click here to <span style="display:block; margin-top:0.5rem;"><a class="btn btn-primary" href="/enquiry/?location='+state.restID+'&date='+state.slotDate+'&editable">enquire now</a></span>'
                })
            }else if( state.Date && state.minPartySize <= BookingData.adults && BookingData.adults <= state.maxPartySize ) {
                // --- Reset Menus, Areas and Upsells
                console.log('Reset: Menus, Areas, Upsells')
                setState({...state,
                    restAreas: null,
                    restMenus: null,
                    restMenuName: false,
                    restUpsells: null,
                    restTimes: null,
                    Time: 0
                })
                setBookingData({...BookingData, areaId: ''})
            }

            // === GA: New booking, on party size selection
            if( typeof ga === 'function' ) {
                console.log('GA: New booking, on party size selection')
                ga('send', 'event', 'create_booking', 'pick_partysize', 'pick');
                ga('brasserie.send', 'event', 'create_booking', 'pick_partysize', 'pick');
            }
        }
    },[BookingData.adults])

    useEffect(()=>{
        if( BookingData.adults && state.Date && state.minPartySize <= BookingData.adults && BookingData.adults <= state.maxPartySize ) {
            console.log('Party size changed (second call)...')
            updateAreas()
        }
    },[BookingData.adults])

    useEffect(()=>{
        // --- Update selected Menu Name
        let temprestAreaName = false
        if( BookingData.areaId && state.restAreas ) {
            state.restAreas.forEach(function(item){
                if( BookingData.areaId == item.id ) {
                    temprestAreaName = item.name
                }
            })

            console.log(`areaId has changed: ${BookingData.areaId}, ${temprestAreaName}`)
            setState({...state,
                restAreaName: temprestAreaName,
                restMenus: null,
                restMenuName: false,
                restUpsells: null,
                restTimes: null,
                Time: 0
            })
        }

    },[BookingData.areaId])

    useEffect(()=>{
        console.log('restAreaName has changed: ' + state.restAreaName)
        if( state.restAreaName && BookingData.areaId ) {
            console.log('Get time slots')
            getSlots()
        }
    },[state.restAreaName])

    useEffect(()=>{
        let timeCtn = widgetRef.current.querySelector('.time-dropdown').closest('.form-group')

        // const restWithCustomAvailabilty = ['27', '30', '31'] // Sites ID's that will have custom Availabilty message [Ropemaker, White Hart Lewes, Royal Forrest]
        // const availMessage = restWithCustomAvailabilty && restWithCustomAvailabilty.includes(state.restID)
        //                     ? 'We can\'t find a table right now, but we might still be able to fit you in! You could try a different time; or make an enquiry (by clicking Enquire), and a member of the team will get back to you as soon as possible.'
        //                     : 'We can\'t find a table right now, but we might still be able to fit you in! You could try a different time; or make an enquiry so that a member of the team can get in touch with the restaurant\'s availability (please either click Enquire, or call us on '+ state.restPhone +').'
        // --- Replace above with one simply message (23.01.2025)
        const availMessage = 'We can\'t find a table right now, but we might still be able to fit you in! You could try a different time; or make an enquiry (by clicking Enquire), and a member of the team will get back to you as soon as possible.';
        if ( state.Time ) {
            console.log(state.Time)
            // --- Remove error style from Time control
            timeCtn.classList.remove('input-error')

            if( (state.slotType == 'enquiry' && state.restBookingType == 'standard') ||
                (state.slotType == 'booking' && state.Time.available == false) )
            {
                console.log('Timeslot is of type: enquiry');
                setState({...state,
                    loading: true,
                    appMessage: `<span style="display:block; margin-top:2rem;">${availMessage}</span>`,
                    appMessageButtons: true
                })
            }else {
                // --- Update Menus and Areas dropdowns
                console.log('Update Menus and Upsells...')
                setState({...state,
                    restMenus: null,
                    restMenuName: false,
                    restUpsells: null
                })
                updateMenusUpsells()
            }

            // === GA: New booking, on timeslot selection
            if( typeof ga === 'function' ) {
                ga('send', 'event', 'create_booking', 'pick_timeslot')
                ga('brasserie.send', 'event', 'create_booking', 'pick_timeslot')
            }

        }else {
            timeCtn.classList.add('input-error');
        }
    }, [state.Time])

    useEffect(()=>{
        console.log(`menuIds has changed: ${BookingData.menuIds}`)
        // --- Update selected Menu Name
        if( BookingData.menuIds && state.restMenus ) {
            state.restMenus.forEach(function(item){
                if( BookingData.menuIds == item.id ) setState({...state, restMenuName: item.name})
            })
        }
    },[BookingData.menuIds])

    // ===== React onMount
    useEffect(()=>{
        console.log(bookingJson.restBookingType + ' widget loaded')
        utm_cookie = getCookie(UTM_COOKIENAME) ? JSON.parse(getCookie(UTM_COOKIENAME)) : false
        // console.log(utm_source, utm_cookie)

        // TODO: Fix me
        if( typeof ga === 'function' ) {
            console.log(gtm+' GA Start')

            ga('create', 'UA-61208253-1', 'auto', {});
            ga('send', 'pageview');
            // Considering re-naming brasserie to 'second' and in the main scripts also
            ga('create', 'UA-61485414-1', 'auto', {'name':'brasserie','allowLinker':true});
            ga('brasserie.send', 'pageview');
        }
        if( typeof fbq === 'function' ) {
            console.log(pixel +' FB Start');
            fbq('trackSingleCustom', pixel, state.pixelEventPrefix+' Start');
        }

        // === START: Vue -> mounted
        let requiredInputs = widgetRef.current?.querySelectorAll('[required]'),
              ieMessageCtn = document.querySelector('.zonalevents-ie-message')
        // console.log(ieMessageCtn, requiredInputs)
        // --- make sure IE message is hidden
        ieMessageCtn.style.display = "none"

        // --- add error-tip holders to all required fields
        if( requiredInputs ) {
            requiredInputs.forEach(function(item){
                item.addEventListener('change', requiredInputChange);
            })
        }

        // --- get default location ID and trigger change if needed
        if( bookingJson.defaultLocation ) {
            // console.log('Default location: '+bookingJson.defaultLocation)
            const option = locationRef.current.querySelector('option[value="' + bookingJson.defaultLocation + '"]');
            if( option ) {
                setBookingData({...BookingData, siteId: option.value})
            }
        }else {
            setState({...state, loading: false})
        }

        // --- remove app loader

        // === GA: On widget load - success
        if( typeof ga === 'function' ) {
            ga('send', 'event', 'widget', 'init', 'success');
            ga('brasserie.send', 'event', 'widget', 'init', 'success');
        }
        // END: Vue => mounted

        return ()=>{
            let requiredInputs = widgetRef.current?.querySelectorAll('[required]')
            if( requiredInputs ){
                requiredInputs.forEach(function(item){
                    item.removeEventListener('change', requiredInputChange)
                })
            }
        }
    },[router])

    // === close DBM panel
    const DBM_nextTable = ()=>{
        setBookingData({...BookingData,
            adults:0,
            areaId: '',
            menuIds: '',
            upsellIds: [],
        })
        setState({...state,
            status: 'book',
            step: 1,
            loading: false,
            appMessage: null,
            appMessageButtons: false
        })
    }
    const DBM_closePanel = ()=>{
        const closeBtn =  top.window.document.querySelector('.gldbm-panel__close')
        console.log('Lets find closing button...')
        if( closeBtn ) {
            console.log('CLosing iframe now...')
            closeBtn.click()
        }
    }

    return (
    <div>
    <div className="zonalevents-ie-message">
        <p>We are currently experiencing technical issues with displaying our table reservation functionality in your web browser. Please open this website using one of the following web browsers to complete your booking:</p>
        <ul>
            <li>Microsoft Edge</li>
            <li>Google Chrome</li>
            <li>Firefox</li>
        </ul>
        <p>Alternatively, please call the restaurant directly and we will be happy to assist you with completing your reservation. </p>
    </div>
    <div ref={widgetRef} className="zonalevents-widget v1" id="zonalevents-app">

        {/* --- Guestline debug */}
        {(transaction_id && GuestlineApi) && <GuestlineBridge
                            BookingID={transaction_id}
                            BookingData={BookingData}
                            setBookingData={setBookingData}
                            state={state}
                            setState={setState}
                            GuestlineApi={GuestlineApi}
                            optHotelId={hotel_id}
                            />}

        <form id="zonalevents-booking-form" name="zonalevents-booking-form" action="" method="POST" className="text-center text-lg-start">

            <div className={`form-group ${state.step==1 && !bookingJson.defaultLocation ? '' : 'd-none'}`} >
                <label htmlFor="zonalevents_location_select" className="form-label text-center">Location</label>
                <select ref={locationRef} id="zonalevents_location_select" className="form-control" defaultValue={BookingData.siteId} onChange={(e)=>setBookingData({...BookingData, siteId: e.target.value})} >
                    <option value="0">Please select</option>
                    {bookingJson.locationOptions && bookingJson.locationOptions.map((item,index)=>(
                        <option key={`location-index-${index}`} value={item.zonalId}
                        data-id={item.id}
                        data-name={item.name}
                        data-phone={item.phone || ''}
                        data-email={item.email}
                        data-url={item.url}
                        data-atreemoid={item.atreemoid}
                        data-occasionid={item.occasionid}
                        data-roomsoccasionid={item.roomsoccasionid}
                        data-menuid={item.menuid}
                        data-menushow={item.menushow}
                        data-areaid={item.areaid}
                        data-areashow={item.areashow}
                        data-usepromocodes={item.usePromocodes}
                        data-upsellshow={item.upsellshow}
                        data-enquiryinfo={item.enquiryInfo}
                        data-confirmationinfo={item.confirmationInfo}
                        data-hotelid={item.hotelid}
                        data-glapiurl={item.glapiurl}
                        data-glapipass={item.glapipass}
                        data-glapioperator={item.glapioperator}
                        data-glapiinterface={item.glapiinterface}
                        data-mindate={item.mindate}
                        data-maxdate={item.maxdate}
                        dangerouslySetInnerHTML={{__html: item.name}}
                        ></option>
                ))}
                </select>
            </div>

            {!!bookingJson.booking_treats && <BookingWidgetTreats location={bookingJson.locationOptions?.find(item => item.id === state.restID)} />}


            <div id="header" className={BookingData.siteId && state.status != 'booked' ? '' : 'd-none'}>
                <div className="inner">
                    <h3 className="h6">{ state.restName } <br/>{state.restPhone }</h3>
                    {(state.usePromocodes && utm_source) && (
                        <h4>Joining us on a Thursday evening for a spot of Après ski? <br className="d-none d-md-inline" />Call us directly on { state.restPhone } so we can allocate you a table</h4>
                    )}
                    <div className={state.restBookingType == 'events' && state.step == 1 ? '' : 'd-none'}
                        dangerouslySetInnerHTML={{__html: EnquiryInfo}}></div>

                </div>
            </div>

            {/* ========== START: CREATE Booking section ========== */}
            <div className="widget-content">

                {/* <transition tag="div" name="slide"> */}
                {/* ===== Step 1 ===== */}
                <div ref={page1} id="zonalevents_page_1" className={`widget-page ${BookingData.siteId && state.step==1 ? '' : 'd-none'}`} data-role="page">

                    <div className={`inner`}>

                        <div className="form-group" >
                            <label htmlFor="zonalevents_Date" className="form-label text-center">Date</label>
                            <input type="text" class={`form-control ${customBookingData?.date ? '' : 'd-none'}`} value={ state.slotDateFormated }  disabled />
                            <div className={`zonalevents-date-wrapper ${customBookingData?.date ? 'd-none' : ''}`}>
                                <Calendar
                                instanceId={useId()}
                                className="mx-auto"
                                value={state.Date}
                                prevLabel={"Prev month"}
                                prev2Label={"Prev year"}
                                nextLabel={"Next month"}
                                next2Label={"Next year"}
                                disabled
                                onChange={(value, event)=>setState({...state, Date: value, slotDate: format(new Date(value), "yyyy-MM-dd")})}
                                minDate={state.minDate}
                                maxDate={state.maxDate}
                                activeStartDate={state.minActiveDate}
                                onActiveStartDateChange={onActiveStartDateChangeHandler}
                                formatLongDate={(locale, date) => format(new Date(date), 'yyyy-MM-dd')}
                                prevLabel={"Prev month"}
                                nextLabel={"Next month"}
                                next2Label={null}
                                prev2Label={null}
                                ></Calendar>
                            </div>
                        </div>

                        <div className={`form-group ${state.restOccasionShow && state.Date ? '' : 'd-none'}`}>
                            <label htmlFor="zonalevents_occasionId" className="form-label text-center">Occasion</label>
                            <select id="zonalevents_occasionId" name="zonalevents_occasionId" className="form-control" value={BookingData.occasionId} onChange={(e)=>setBookingData({...BookingData, occasionId: e.target.value})} required>
                                <option value="">Choose an occasion</option>
                                {state.restOccasions && state.restOccasions.map((item, i)=>{
                                    if(item.available ) return(<option key={`occasion-option-${i}`} value={item.id} >{ item.name }</option>)
                                })}
                            </select>
                            <span className="error-tip">This field is required</span>
                        </div>

                        <div className={`form-group ${BookingData.occasionId && state.Date && BookingData.occasionId ? '' : 'd-none'}`}>
                            <label htmlFor="zonalevents_AdultCovers" className="form-label text-center">Party Size</label>
                            <select id="zonalevents_AdultCovers" name="zonalevents_AdultCovers" className="form-control" value={BookingData.adults} onChange={(e)=>setBookingData({...BookingData, adults: e.target.value})} required>
                                <option value="0">How many guests?</option>
                                {(function (rows, i, len) {
                                    while (++i <= len) {
                                        rows.push(<option key={`partysize-index-${i}`} value={i}>{ i }</option>)
                                    }
                                    return rows;
                                })([], state.minPartySize-1, state.maxPartySize)}
                                <option value={state.maxPartySize + 1} className={state.restBookingType == 'standard' && state.restOccasionId == BookingData.occasionId ? '' : 'd-none'}>{ state.maxPartySize+1 } or more</option>
                            </select>
                            <span className="error-tip">This field is required</span>
                        </div>

                        {/* Hide when BookingData.adults > maxPartySize */}
                        <div id="area-wrapper" className={BookingData.adults <= state.maxPartySize && BookingData.occasionId ? '' : 'd-none'}>

                            {(BookingData.adults > 0 && state.restAreaShow && state.restAreas) && (
                                <div className="form-group">
                                    <label htmlFor="zonalevents_areaId" className="form-label text-center">Select your preferred venue area:</label>
                                    <select disabled={customBookingData?.areaId ? 'disabled' : ''} id="zonalevents_areaId" name="zonalevents_areaId" className="form-control" value={BookingData.areaId} onChange={(e)=>setBookingData({...BookingData, areaId: e.target.value})} required>
                                        <option value="null">Choose area</option>
                                        {state.restAreas && state.restAreas.map((area, i)=>{
                                            if(area.available) return(<option key={`area-option-${i}`} value={area.id} >{ area.name }</option>)
                                        })}
                                    </select>
                                    <span className="error-tip">This field is required</span>
                                </div>
                            )}

                            <div className={`form-group ${state.Date && BookingData.adults && BookingData.areaId && state.restTimes && state.availableTimeslots ? '' : 'd-none'}`}>
                                <label htmlFor="zonalevents_booking_time" className="form-label text-center">Time</label>
                                <div ref={TimeDropdownRef} className="dropdown time-dropdown">

                                    <input type="text" class={`form-control ${customBookingData?.time ? '' : 'd-none'}`} value={ state.Time ? state.Time.time : 'Choose Time' }  disabled />

                                    <Dropdown
                                        onToggle={TimeDropdownHandle}
                                        onSelect={()=> console.log('menu itme selected')}
                                        className={customBookingData?.time ? 'd-none' : 'time-dropdown'}>
                                        <Dropdown.Toggle className='form-control dropdown-toggle scroll-me-up' >
                                            { state.Time ? state.Time.time : 'Choose Time' }{ state.DateMode == 'range' && state.Time ? ' on ' + state.slotDate : '' }
                                        </Dropdown.Toggle>
                                        <Dropdown.Menu renderOnMount={true} as="div" className={`dropdown-menu ${state.restBookingType == 'events' ? 'no-legend' : ''}`}>
                                            <div className="dropdown-menu-inner">
                                                {state.restTimes && state.restTimes.map((day, dayIndex)=>(
                                                    <div key={`time-day-index-${dayIndex}`} className="time-session" >
                                                        <h4 className={`time-session-label ${state.restTimes.length > 1 ? '': 'd-none'}`}>{ day.date }</h4>
                                                        {day.sessions && day.sessions.map((session, sessionIndex)=>(
                                                            <div key={`time-session-index-${sessionIndex}`}>
                                                                <h4 className={`time-session-label ${session.times.length > 1 ? '' : 'd-none'}`}>{ session.name }</h4>
                                                                <ul className="time-list" >
                                                                    {session.times && session.times.map((item, index)=>(
                                                                        <li key={`time-slot-index-${index}`} className="time-item" >
                                                                            <Dropdown.Item onClick={(e)=>changeSlotDateTime(e, item, day.date, state.restBookingType == 'events' || ( !item.isClosed && !item.available && item.canEnquire ) ? 'enquiry' : 'booking')}>
                                                                                {(!item.isClosed && ( item.available || item.canEnquire )) && (
                                                                                    <input type="radio" id={`zonalevents_time_${dayIndex+sessionIndex}_${index}`} name="zonalevents_Time" className="sr-only"
                                                                                    data-date={day.date}
                                                                                    data-type={state.restBookingType == 'events' || ( !item.isClosed && !item.available && item.canEnquire ) ? 'enquiry' : 'booking'} />
                                                                                )}
                                                                                <label htmlFor={`zonalevents_time_${dayIndex+sessionIndex}_${index}`}
                                                                                disabled={item.isClosed || ( !item.available && !item.canEnquire ) || ( isPastTime(day.date+'T'+item.time) )}
                                                                                className={state.restBookingType == 'standard' && !item.isClosed && !item.available && item.canEnquire ? 'limited' : ''} >{ item.time }</label>
                                                                            </Dropdown.Item>
                                                                        </li>
                                                                    ))}
                                                                </ul>
                                                            </div>
                                                        ))}
                                                    </div>
                                                ))}
                                                <p className={`time-legend ${state.restBookingType == 'standard' ? '' : 'd-none'}`}>
                                                    <span >Available</span>
                                                    <span className="limited">Enquirie</span>
                                                    <span disabled>Unavailable</span>
                                                </p>
                                            </div>
                                        </Dropdown.Menu>
                                    </Dropdown>

                                </div>
                                <span className="error-tip">This field is required</span>
                            </div>

                            <div className={`form-group ${state.Time && BookingData.areaId && state.restMenuShow && state.restMenus && state.restMenus.length > 0 ? '' : 'd-none'}`}>
                                <label htmlFor="zonalevents_menuId" className="form-label text-center" >Select your preferred menu choice:</label>
                                <select disabled={customBookingData?.menuIds ? 'disabled' : ''} id="zonalevents_menuId" name="zonalevents_menuId" className="form-control" value={BookingData.menuIds} onChange={(e)=>setBookingData({...BookingData, menuIds: e.target.value})} required>
                                    <option value="null">Please select</option>
                                    {state.restMenus && state.restMenus.map((menu, index)=>{
                                        if(menu.available) return(<option key={`menu-index-${index}`} value={menu.id} >{ menu.name }</option>)
                                    })}
                                </select>
                                <span className="error-tip">This field is required</span>
                            </div>

                            <div className={`form-group ${state.Time && state.restUpsellShow && state.restUpsells ? '' : 'd-none'}`}>
                                {state.restUpsells && state.restUpsells.map((upsell, index)=>(
                                    <div className="form-group form-inline" key={`upsells-index-${index}`}>
                                        <input type="checkbox" id={`upsell${upsell.id}`} className="form-control" value={upsell.id} onChange={(e)=>upsellIdsChange(e.target,upsell)} />
                                        <label htmlFor={`upsell${upsell.id}`}><strong>{ upsell.name }{ upsell.price ? " &pound;"+upsell.price : "" }</strong><br/>{ upsell.description }</label>
                                    </div>
                                ))}
                            </div>

                        </div>


                    </div>

                    {/* Hide when BookingData.adults > maxPartySize */}
                    <div className={BookingData.adults <= state.maxPartySize ? '' : 'd-none'}>

                        {/* === Buttons: Book === */}
                        <div className={`inner text-center ${state.status == 'book' && state.step == 1 ? '' : 'd-none'}`}>
                            <input type="button" className="btn btn-primary" value="Next"
                                onClick={()=>validateAndGoto(1,2)}
                                disabled={BookingData.siteId == 0 || !valid_step1()} />
                        </div>

                    </div>

                </div>
                {/* </transition> */}

                {/* <transition tag="div" name="slide"> */}
                {/* ===== Step 2 ===== */}
                <div ref={page2} id="zonalevents_page_2" className={`widget-page ${BookingData.siteId && state.step==2 ? '' : 'd-none'}`} data-role="page">

                    <div className="inner">

                        <div className="form-group">
                            <label htmlFor="zonalevents_FirstName" className="form-label text-center">First Name</label>
                            <input type="text" id="zonalevents_FirstName" name="zonalevents_FirstName" value={BookingData.firstname} onChange={(e)=>setBookingData({...BookingData, firstname: e.target.value})} className="form-control" required />
                            <span className="error-tip">This field is required</span>
                        </div>

                        <div className="form-group">
                            <label htmlFor="zonalevents_LastName" className="form-label text-center">Last Name</label>
                            <input type="text" id="zonalevents_LastName" name="zonalevents_LastName" value={BookingData.lastname} onChange={(e)=>setBookingData({...BookingData, lastname: e.target.value})} className="form-control" required />
                            <span className="error-tip">This field is required</span>
                        </div>

                        <div className="form-group">
                            <label htmlFor="zonalevents_Email" className="form-label text-center">Email</label>
                            <input type="email" id="zonalevents_Email" name="zonalevents_Email" value={BookingData.emailAddress} onChange={(e)=>setBookingData({...BookingData, emailAddress: e.target.value})} className="form-control" required />
                            <span className="error-tip">This field is required</span>
                        </div>

                        <div className="form-group">
                            <label htmlFor="zonalevents_PhoneNumber" className="form-label text-center">Telephone Number (no spaces)</label>
                            <input type="tel" id="zonalevents_PhoneNumber" name="zonalevents_PhoneNumber" value={BookingData.telephoneNumber} onChange={(e)=>setBookingData({...BookingData, telephoneNumber: e.target.value})} className="form-control" required />
                            <span className="error-tip">This field is required</span>
                        </div>

                    </div>

                    <div className="inner">
                        <div className="form-group form-inline">
                            <input type="checkbox" id="zonalevents_EmailOptIn" name="zonalevents_EmailOptIn" onChange={(e)=>{ setBookingData({...BookingData, consent:{...BookingData.consent, email: e.target.checked}}) }} className="form-control" value="1" />
                            <label htmlFor="zonalevents_EmailOptIn">Select if you would like to receive emails from us regarding news, events and offers.</label>
                        </div>
                        <div className="form-group form-inline">
                            <input type="checkbox" id="zonalevents_SmsOptIn" name="zonalevents_SmsOptIn" onChange={(e)=>{ setBookingData({...BookingData, consent:{...BookingData.consent, sms: e.target.checked}}) }} className="form-control" value="1" />
                            <label htmlFor="zonalevents_SmsOptIn">Select if you would like to receive SMS/text messages about news, offers and events from us.</label>
                        </div>
                    </div>

                    <div className="inner">
                        <p>We will only send you marketing information from your favourite pub, Heartwood Inns and/or Brasserie Blanc if you consent to this by selecting the options above. From time to time we may also include information from trusted complementary third party brands with whom we are running events or offers but we will never share your details with them.</p>
                        <p>Please note you are able to unsubscribe from communications at any time.</p>
                        <p>The day after your booking you will receive an optional feedback request (which is not a marketing message).</p>
                        <span className={state.restBookingType == 'standard' ? '' : 'd-none'}>Our booking terms and conditions can be found <a href="http://bookings.liveres.co.uk/tc.html" target="_blank" rel="nofollow noopener" title="Booking terms and conditions">here</a> and apply to all bookings. </span>
                        <span className={state.restBookingType == 'events' ? '' : 'd-none'}>Our booking enquiries terms and conditions can be found <a href="http://bookings.liveres.co.uk/tc.html" target="_blank" rel="nofollow noopener" title="Booking enquiries terms and conditions">here</a> and apply to all bookings for events or groups. </span>
                        <span>Click <a href="//heartwoodcollection.com/legal/" target="_blank" title="Privacy Policy">here</a> to review our privacy policy. </span>
                    </div>

                    <h2>Booking details</h2>

                    <p className="booking-details">
                        <span className="single-line"><strong>Date:</strong> <span className="line-detail">{ state.slotDateFormated }</span></span>
                        <span className="single-line"><strong>Time:</strong> <span className="line-detail">{ state.Time.time }</span></span>
                        <span className="single-line"><strong>Party size:</strong> <span className="line-detail">{ BookingData.adults } guests</span></span>
                        {state.restOccasionShow && <span className="single-line"><strong>Occasion:</strong> <span className="line-detail">{ state.restOccasionName }</span></span> }
                        <span className="single-line"><strong>Venue:</strong> <span className="line-detail">{ state.restName }</span></span>
                        {state.restAreaName && <span className="single-line"><strong>Area:</strong> <span className="line-detail">{ state.restAreaName }</span></span>}
                        {state.restMenuName && <span className="single-line"><strong>Menu choice:</strong> <span className="line-detail">{ state.restMenuName }</span></span>}
                        {(BookingData.upsellIds && BookingData.upsellIds.length > 0) && (
                            <span className="single-line" className="zonalevents-upsell-name">
                                <strong>Extras: </strong>
                                { BookingData.upsellIds && BookingData.upsellIds.map((upsell,index)=>(
                                    <span key={`summary-upsells-index-${index}`}>{ upsell.name }{ upsell.price ? " &pound;"+upsell.price : "" }</span>
                                ))}
                            </span>
                        )}
                    </p>

                    <div className={`inner text-center d-flex flex-row align-items-center justify-content-center mb-50 ${state.status == 'book' ? '' : 'd-none'}`}>
                        <button type="button" className="btn btn-outline-primary mx-10" onClick={()=>goToStep(state.step-1)}>Back</button>
                        <input type="button" className="btn btn-primary mx-10" value={state.restBookingType == 'events' ? 'Enquire Now' : 'Book'} onClick={()=>addBooking()} />
                    </div>

                </div>
                {/* </transition> */}

                {/* <transition tag="div" name="slide"> */}
                {/* ===== Step 3 ===== */}
                <div ref={page3} id="zonalevents_page_3" className={`widget-page ${BookingData.siteId && state.step==3 ? '' : 'd-none'}`} data-role="page">

                    <div className="inner">

                        {state.restBookingType == 'standard' ? (
                            <div>
                                <h4>Thank you for making a dining reservation at { state.restName }.</h4>
                                <p className="booking-details">
                                    <span className="single-line"><strong>Confirmation code:</strong> <span className="line-detail">{ BookingData.bookingReference }</span></span>
                                    <span className="single-line"><strong>Date:</strong> <span className="line-detail">{ state.slotDateFormated }</span></span>
                                    <span className="single-line"><strong>Time:</strong> <span className="line-detail">{ state.Time.time }</span></span>
                                    <span className="single-line"><strong>Party size:</strong> <span className="line-detail">{ BookingData.adults } guests</span></span>
                                    {state.restOccasionShow && <span className="single-line"><strong>Occasion:</strong> <span className="line-detail">{ state.restOccasionName }</span></span> }
                                    <span className="single-line"><strong>Venue:</strong> <span className="line-detail">{ state.restName }</span></span>
                                    {state.restAreaName && <span className="single-line"><strong>Area:</strong> <span className="line-detail">{ state.restAreaName }</span></span>}
                                    {state.restMenuName && <span className="single-line"><strong>Menu choice:</strong> <span className="line-detail">{ state.restMenuName }</span></span>}
                                    {(BookingData.upsellIds && BookingData.upsellIds.length > 0) && (
                                        <span className="single-line" className="zonalevents-upsell-name">
                                            <strong>Extras: </strong>
                                            { BookingData.upsellIds && BookingData.upsellIds.map((upsell,index)=>(
                                                <span key={`summary-upsells-index-${index}`}>{ upsell.name }{ upsell.price ? " &pound;"+upsell.price : "" }</span>
                                            ))}
                                        </span>
                                    )}
                                </p>

                                {/* <p>If you have any queries regarding your booking or if we can be of further assistance please do not hesitate to contact us. </p> */}
                                {state.restUrl && <p>Why don't you look at <a href={`${state.restUrl}menus/`} title="Menus">our menus</a> ahead of your visit? </p>}
                            </div>
                        ) : (
                            <div>
                                <h4>Thank you for making an enquiry at { state.restName } for { BookingData.adults } people at { state.slotDateFormated } { state.Time.time }.</h4>
                                <p>A member of our team will be in touch shortly to discuss your enquiry in more detail. <br/>
                                The contact number we have noted for you is: { BookingData.telephoneNumber }</p>
                                <p>Enquiry reference code: { BookingData.bookingReference }</p>

                                <p>We look forward to speaking with you soon.</p>
                            </div>
                        )}

                        {transaction_id && (
                            <p className="text-center">
                                <button type="button" className="btn btn-primary btn-sm btn-narrow mx-10 ms-lg-0" onClick={DBM_nextTable}>Book another table</button>
                                {layout == "clean" ? (
                                    <button type="button" className="btn btn-primary btn-sm btn-narrow mx-10" onClick={DBM_closePanel}>Finish</button>
                                ) : (
                                    <Link href="/" className="btn btn-primary btn-sm btn-narrow mx-10">Finish</Link>
                                )}
                            </p>
                        )}

                        {/* update: 13.07.2022 */}
                        {ConfirmationInfo ? (
                            <div dangerouslySetInnerHTML={{__html: ConfirmationInfo}}></div>
                        ) : (
                            <div>
                                <p>Please note that we continue to be cashless so payment is by card only.</p>
                                <p>If you have any queries regarding your booking or if we can be of further assistance please do not hesitate to contact us. </p>
                                <p><em>Thank you and we look forward to seeing you.</em></p>
                            </div>
                        )}

                    </div>

                </div>
                {/* </transition> */}
            </div>
            {/* .widget-content */}
            {/* ========== END: CREATE Booking section ========== */}

        </form>

        <div className={`zonalevents-loader ${state.loading ? '' : 'd-none'}`}>
            <div className="loader">Loading...</div>
            <div className={`zonalevents-message text-center ${state.appMessage ? '' : 'd-none'}`}>
                <span className={`zonalevents-message-close ${!state.appMessageButtons ? '' : 'd-none'}`} onClick={(e)=>{ setState({...state, loading: false}) }}>×</span>
                <p dangerouslySetInnerHTML={{__html: state.appMessage}}></p>
                <p className={`${state.appMessageButtons ? '' : 'd-none'}`} style={{margin: '0.5rem 0 0'}}>
                    <a href="/enquiry/" className="btn btn-primary mb-15">Enquire</a>
                    <button className="btn btn-primary" onClick={()=>clearBookingForm()}>Try Again</button>
                </p>
            </div>
        </div>

    </div>
    </div>
    )
}
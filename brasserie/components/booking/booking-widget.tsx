'use client'
/**
 * Booking Widget
 * this component serves as a gate to choose from different widget versions & variations (A/B testing)
 */
import { useEffect, useState } from "react";
import { useSettingsContext } from "../providers/settingsProvider";
import BookingWidgetV3 from "./booking-widget-v3";
import { useRouter } from "next/router";

export default function BookingWidget(props){
    const {settings} = useSettingsContext()
    const [showWidget, setShowWidget] = useState(false)
    const router = useRouter()

    // --- NOTE: we no longer maintain legacy v1 and v2, v3 is the current version
    // const allowedFlags = settings?.acf?.optGeneral?.optAllowedFlags || false
    // const canUseFlag = allowedFlags?.includes('bookingWidgetV2') ? true : false
    // const isV2 = canUseFlag ? true : false

    useEffect(()=>{
        // Wait until router is ready before processing query params
        if (!router.isReady) return

        let queryParams = router.query

        // Fallback for ISR: parse URL params directly from window.location
        if (typeof window !== 'undefined' && Object.keys(queryParams).length <= 1) {
            const urlParams = new URLSearchParams(window.location.search)
            queryParams = {
                ...queryParams,
                location: urlParams.get('location'),
                date: urlParams.get('date'),
                partysize: urlParams.get('partysize')
            }
        }

        const {location, date, partysize} = queryParams

        console.log('router.query', queryParams)

        if( date ) {
            props.settings.booking_date = date
            console.log('Default date: ', date)
        }

        if( partysize ) {
            props.settings.booking_partysize = partysize
        }

        if( location ) {
            props.settings.locationOptions.filter((item)=>{
                return item.id == location
            }).map((item)=>{
                props.settings.defaultLocation = item.zonalId
            })
            console.log('Default location: ', location)
        }
        setShowWidget(true)
    },[router.isReady])

    return(
        !!showWidget && <BookingWidgetV3 {...props} type={props.settings.bookingLayout} />
    )
}

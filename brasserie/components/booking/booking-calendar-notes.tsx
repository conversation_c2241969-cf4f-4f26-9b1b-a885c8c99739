interface CalendarMessage {
    dates: Array<{ from: string; to?: string }>,
    message: string,
}

export default function BookingCalendarNotes(props) {
    const messages : Array<CalendarMessage> = props?.state?.calendarMessages || []
    const selectedDate = props?.state?.slotDate || null
    const message = messages.find((message) => {
        return message.dates.find((date) => {
            if (!date.to) {
                // If no 'to' date, selected date must equal 'from' date
                return date.from === selectedDate
            } else {
                // If both 'from' and 'to' exist, check if selected date is in range
                const isAfterFrom = date.from <= selectedDate
                const isBeforeTo = !date.to || date.to >= selectedDate
                return isAfterFrom && isBeforeTo
            }
        })
    })

    // console.log(selectedDate, messages, message)

    return (
        message && selectedDate && (
            <aside className="zonalevents-calendar-notes my-10 text-center">
                <div className="inview">
                    <hr className="m-0" />
                    <div className="m-10" dangerouslySetInnerHTML={{__html: message.message}} />
                    <hr className="m-0" />
                </div>
            </aside>
        )
    )
}
export default function BookingWidgetTreats(props) {
    const message = '"Treats" are not currently available at this pub. Please look out for exciting news soon.'
    const location = props.location || false
    console.log(location)
    if( !location || location.loyalty ) return null
    return (
        <p style={{
            "color": "red",
            "fontSize": "0.85em"
        }}>{message}</p>
    )
}
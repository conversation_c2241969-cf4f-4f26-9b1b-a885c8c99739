import { Col, Container, <PERSON> } from "react-bootstrap";
import { useSettingsContext } from "./providers/settingsProvider";
import Socials from './socials';

export default function HeaderInfo() {
    const {settings} = useSettingsContext(),
        showAddress = (!settings?.siteID || settings?.siteID != 3) ? true : false

    // console.log(settings)

    return(
        <div className="header-info d-none d-lg-block text-end py-10 position-absolute">
            <Container>
                <Row>
                    <Col>
                        {showAddress && <span>{settings.acf.optGeneral.optAddress}</span>}
                        <span className="ps-30">{settings.acf.optGeneral.optPhone}</span>
                        <a href={`mailto:${settings.acf.optGeneral.optEmail}`} className="ps-30">{settings.acf.optGeneral.optEmail}</a>
                        <Socials type="icons" className="d-inline text-center ps-10" />
                    </Col>
                </Row>
            </Container>
        </div>
    )
}
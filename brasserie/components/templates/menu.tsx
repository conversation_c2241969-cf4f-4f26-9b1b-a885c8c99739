import PageHeader from "@components/page-header";
import TenKitesEmbed from "@components/menus/TenKites-embed";
import Head from "next/head";
import Img from "@components/image";
import LocationsMenusNav from "@components/menus/locations-nav";
import parseHtml from "@lib/parser";
import { useSettingsContext } from "@components/providers/settingsProvider";

export default function TemplateMenu(props) {
    const post = props?.post || false
    if( !post ) return null
    const ancestors = post?.ancestors?.nodes || false
    // get last item from ancestors array
    const parentPost = ancestors ? ancestors[ancestors.length-1] : false
    const tenkitesMenu = post?.singleMenu?.menuMenu || false
    const menuBadge = post?.singleMenu?.menuBadge || false
    const {settings} = useSettingsContext()
    const menusJson = settings?.locationsMenusData ? JSON.parse(settings.locationsMenusData) : false

    return(
        <article className="entry-content mb-100">
            {!post?.pageOptions.hideTitle &&
                <PageHeader title={post.title} className="h2">
                    Brasserie Blanc {!!parentPost && parentPost.title}
                </PageHeader>}

            <div className="row flex-column align-items-center">

                <div className="col-12 col-lg-5 col-xl-4 mb-20">
                    <hr className="mt-10" />
                    {post.content && parseHtml(post.content)}
                </div>

                {menusJson && <LocationsMenusNav type="links" dropdownClass="position-relative z-3 col-lg-5 col-xl-4" json={menusJson} />}

                <div className="col-12">
                    <div className={`tenkites-menu${menuBadge ? ' has-badge':''}`}>
                        {menuBadge &&
                            <div className="text-center">
                                <figure className="menu-badge tripple-border__round d-inline-block">
                                    <Img image={menuBadge} alt={`Menu - ${post.title}`} />
                                </figure>
                            </div>
                        }
                        {tenkitesMenu && <TenKitesEmbed tenkitesUrl={tenkitesMenu} />}
                    </div>
                </div>
            </div>

        </article>
    )
}
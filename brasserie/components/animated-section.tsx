// @ts-nocheck
import { useInView } from "motion/react";
import { useRef, useState } from "react";

export default function AnimatedSection({ children, tag ='div', className='', animation=["fadeInBottom",0] }) {
    const ref = useRef(null);
    const [Tag, setTag] = useState(tag)
    const isInView = useInView(ref, { once: true });
    let type = animation && animation[0] || "fadeInBottom"
    let delay = animation && animation[1] ? (parseInt(animation[1]) / 1000).toFixed(2) : 0
    let time = animation && animation[2] ? (parseInt(animation[2]) / 1000).toFixed(2) : "0.8"
    // console.log(type, delay)

    const ease = `all ${time}s cubic-bezier(0.32, 0, 0.67, 0) ${delay}s`

    const styles = {
      fadeInBottom: {
        transform: "translateY(50px)",
        opacity: 0,
        transition: ease
      },
      fadeInLeft: {
        transform: "translateX(-50px)",
        opacity: 0,
        transition:ease
      },
      fadeInRight: {
        transform: "translateX(50px)",
        opacity: 0,
        transition: ease
      },
      scaleDown: {
        transform: "scale3d(1.2, 1.2, 1) ",
        initial: "scale3d(1,1,1)",
        opacity: 0,
        transition: ease
      },
      fullWidth: {
        transform: "scale3d(1, 1, 1) ",
        initial: "scale3d(0,1,1)",
        origin: "0 50%",
        opacity: 1,
        transition: ease
      },
      fullWidthInverse: {
        transform: "scale3d(1, 1, 1) ",
        initial: "scale3d(0,1,1)",
        origin: "100% 50%",
        opacity: 1,
        transition: ease
      },
      imageCover: { // same as "fadeInRight", but will add extra div with "fullwidth" to cover on top
        transform: "translateX(50px)",
        opacity: 0,
        transition: ease
      },
      imageCoverInverse: { // same as "fadeInRight", but will add extra div with "fullwidth" to cover on top
        transform: "translateX(-50px)",
        opacity: 0,
        transition:ease
      },
    }

    if( !styles[type] ) type = 'fadeInBottom'

    return (
      <Tag
      data-animation={animation}
      ref={ref}
      className={(isInView ? 'inview ' : '') + className}
      style={{
        transform: isInView ? (styles[type].initial || "none") : styles[type]?.transform,
        transformStyle: type == 'scaleDown' || type == 'fullWidth' ? "preserve-3D" : "initial",
        transformOrigin: styles[type].origin || "50% 50%",
        opacity: isInView ? 1 : styles[type]?.opacity,
        transition: styles[type]?.transition
      }}>
          {children}
          {type == 'imageCover' && <AnimatedSection animation={['fullWidth',0,1000]} className="image-cover" />}
          {type == 'imageCoverInverse' && <AnimatedSection animation={['fullWidthInverse',0,1000]} className="image-cover" />}
      </Tag>
    );
  }
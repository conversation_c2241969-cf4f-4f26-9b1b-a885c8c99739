import { useEffect, useId, useRef, useState } from "react";
import Select from "react-select";
import { DropdownIndicator } from "react-select/dist/declarations/src/components/indicators";
import { Icon } from "./icon";

export default function CustomSelect(props) {
    const selectRef = useRef(null)
    const onChange = (option, actionMeta) => {
        let select = selectRef.current.querySelector('select')
        if( select ) select.value = option.value
        // console.log('value changed', option, actionMeta, select.value)
        setValue(option)
    }
    const [options, setOptions] = useState(null)
    const [value, setValue] = useState(()=>{
        return {
            value: null,
            label: null
        }
    })
    const placeholder = props?.placeholder || 'Please choose an option'

    useEffect(()=>{
        const selectEl = selectRef.current.querySelector('select')
        const optionEl = selectRef.current.querySelectorAll('select option')
        let optionVal = []

        optionEl.forEach(el => {
            if( el.value ) optionVal.push({value: el.value, label: el.text})
        });
        setOptions(optionVal)
        // console.log(selectEl.value, selectEl.options[selectEl.selectedIndex].text)
        setValue({value: selectEl.value, label: selectEl.options[selectEl.selectedIndex].text})
    },[])


return (
   <div ref={selectRef} className="js-select-replace">
    {props.children}
    <Select instanceId={useId()}
    isSearchable={false}
    placeholder={placeholder}
    options={options}
    onChange={onChange}
    value={value.value ? value : null}
    className="react-select-container"
    classNamePrefix="react-select"
    components={{
        DropdownIndicator: () => (
            <span className="dropdown-indicator">
                <Icon name={`dropdown-arrow`} className='' />
            </span>
        )
    }}
    />
   </div>
);
}
import { Splide } from '@splidejs/react-splide';
import { URLHash } from '@splidejs/splide-extension-url-hash';
// Default theme
import '@splidejs/react-splide/css';

export default function Carousel(props) {
    const options = props?.options || {}
    // --- Reference: https://splidejs.com/guides/options/
    const defaultOptions = {
        type   : 'fade',
        rewind : true,
        perPage: 1,
        autoplay: options.autoplay,
        interval: 5000,
        speed: 800,
        focus  : 'center',
        arrows: true,
        pagination: false,
        pauseOnHover: options.autoplay ? true : false,
        pauseOnFocus: false
    }
    const title = 'Carousel'

    return(
        <Splide tag="div" className="carousel" aria-label={title}
        options={ {...defaultOptions, ...options} }
        extensions={{URLHash}}
        >
            {props.children}
        </Splide>
    )
}
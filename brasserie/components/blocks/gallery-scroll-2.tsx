import { useScroll, useTransform, motion } from "motion/react"
import { useRef } from "react"

export default function GalleryScroll2(props) {
    const containerRef = useRef(null)
    const { scrollYProgress } = useScroll({
        target: containerRef,
        offset: ["0 0", "1 1"]
    })

    // console.log(props.children)

    const items = props.children.filter((item) => {
        return item?.type ? true : false
    }).length // Number of items
    console.log("Items count: " + items)
    const x = useTransform(scrollYProgress, [0, 1], ["0%", `-${(items - 1) * 100}vw`])
    const progressScale = useTransform(scrollYProgress, [0, 1], [0, 1])

    return(
        <div //@ts-ignore
        className="gallery-wrap-framer" style={{"--items-count": items}}>
            <div className="edge-2-edge">
                <section ref={containerRef} className="img-group-container">
                    <div>
                        <motion.div className="img-group" style={{ x }}>
                            {props.children}
                        </motion.div>
                    </div>
                </section>
            </div>
            {/* <motion.div className="progress" style={{ scaleX: progressScale }} /> */}
        </div>
    )
}

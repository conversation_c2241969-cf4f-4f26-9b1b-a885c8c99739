import Link from "next/link"
import CoverImage from "./cover-image"
import {format} from "date-fns"
import { isPostExpired } from "../lib/utils"

export default function PostPreview({node, skin='default', type='news'}) {
  const title= node.title,
        slug= node.slug,
        coverImage= node.featuredImage,
        excerpt = node.excerpt,
        date = node.date ? format(new Date(node.date), "MMMM d, Y") : null,
        expiryDate = node.singlePost?.expiryDate ? node.singlePost?.expiryDate.replace(':00', '') : false,
        expiryDateNotime = expiryDate ? expiryDate.slice(0, expiryDate.indexOf('at')) : false,
        cats = node.categories ? node.categories.nodes.map((node)=>node.slug) : false,
        urlPrefix = type == 'recipes' ? 'recipes' : 'news'

  // Define type of posts from categories
  if(cats) {
    if( cats.indexOf('events') != -1 ) {
      type = 'events'
    }else if( cats.indexOf('offers') != -1 ) {
      type = 'offers'
    }
  }

  // console.log(type, node.singlePost?.expiryDate)
  // console.log(expiryDate, expiryDateNotime)

  const skin_vars = {
            default: {
              btn: 'btn btn-outline-secondary',
              title: ''
            },
            related: {
              btn: 'readmore text-warm',
              title: ' text-warm'
            }
          },
          headline = {
            news: date,
            offers: expiryDate ? 'Valid until '+expiryDateNotime : date,
            events: expiryDate ? 'Valid until '+expiryDateNotime : date,
            recipes: 'Recipes and tips',
            bedrooms: expiryDate ? 'Valid until '+expiryDateNotime : 'Rooms'
        }

  // --- Adjust headline if offer/event is no longer available
  const postExpiryTimestamp = expiryDate ? new Date(node.singlePost?.expiryDate.slice(0, expiryDate.indexOf('at'))).getTime() : false

  if( (type == 'offers') && postExpiryTimestamp && isPostExpired(postExpiryTimestamp) ) headline[type] = 'No longer available'
  if( (type == 'events') && postExpiryTimestamp && isPostExpired(postExpiryTimestamp) ) headline[type] = 'This event has concluded'

  return (
      <div className="entry text-center d-flex flex-column h-100 align-items-center pb-50">
        <Link href={`/${urlPrefix}/${slug}`} className="d-block w-100">
          {coverImage && <CoverImage title={title} coverImage={coverImage} className="ratio ratio-1x1 overflow-hidden"/>}
        </Link>
        <p className="entry-meta mt-30 mb-0"><em>{headline[type]}</em></p>
        <h2 className="fs-4 mt-0 mb-30 px-15">
          <Link href={`/${urlPrefix}/${slug}`} className={`text-decoration-none${skin_vars[skin].title}`} dangerouslySetInnerHTML={{__html: title}} />
        </h2>
        {excerpt && (<div className="post-excerpt px-15" dangerouslySetInnerHTML={{ __html: excerpt }}></div>)}
        <Link href={`/${urlPrefix}/${slug}`} className={`${skin_vars[skin].btn} mt-auto`} >
          Read more
        </Link>
      </div>
  )
}
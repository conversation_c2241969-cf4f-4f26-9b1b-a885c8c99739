import Favicons from '@components/favicons'

export default function Meta({ seo, asPath = '' }) {
  const siteDomain = process.env.NEXT_PUBLIC_SITE_DOMAIN || "heartwoodinns.com",
        api_url = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || false,
        replace = api_url ? api_url.substring(0, api_url.lastIndexOf('/')) : null,
        canonical = seo.canonical ?
                    seo.canonical.replaceAll(replace, 'https://'+siteDomain)
                    .replaceAll('https://hwc-cms.wearetesting.co.uk/ ','')
                    .replaceAll('https://cms.heartwoodcollection.com/ ','') : 'https://'+siteDomain+asPath
  let schemaRaw = null

  seo.opengraphUrl = 'https://'+siteDomain+asPath

  // --- fix Yoast url's
  if( api_url ) {
    let replace = api_url.substring(0, api_url.lastIndexOf('/'))
    schemaRaw = seo.schema.raw.replaceAll(replace, 'https://'+siteDomain)
    schemaRaw = schemaRaw.replaceAll('heartwoodcollection.com', siteDomain)
  }else {
    schemaRaw = seo.schema.raw
  }

  return (
    <>
    {/* RSS feed */}
    <link key="rss" rel="alternate" type="application/rss+xml" href="/feed.xml" />

    {/* Favicons */}
    <Favicons />

    {/* Yoast SEO */}
    <meta key="robots" name="robots" content={`${seo.metaRobotsNoindex}, ${seo.metaRobotsNofollow}`} />
    {seo.opengraphDescription && <meta key="description" name="description" content={seo.opengraphDescription}/>}
    <meta key="og:locale" property="og:locale" content="en_GB" />
    <meta key="og:type" property="og:type" content="article" />
    <meta key="og:title" property="og:title" content={seo.title} />
    <meta key="og:url" property="og:url" content={seo.opengraphUrl.replaceAll(replace, 'https://'+siteDomain)} />
    <meta key="og:site_name" property="og:site_name" content={seo.opengraphSiteName} />
    {seo.opengraphImage && <meta key="og:image" property="og:image" content={seo.opengraphImage.sourceUrl}/>}
    {seo.opengraphDescription && <meta key="og:description" property="og:description" content={seo.opengraphDescription} />}
    {seo.opengraphModifiedTime && <meta key="article:modified_time" property="article:modified_time" content={seo.opengraphModifiedTime} />}
    <script key="yoast-schema-graph" type="application/ld+json" className="yoast-schema-graph" dangerouslySetInnerHTML={{ __html: schemaRaw }}></script>
    </>
  )
}

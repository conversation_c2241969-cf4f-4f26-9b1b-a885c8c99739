import Link from 'next/link'
import { Container, Row, Col, Navbar, Offcanvas, Dropdown } from 'react-bootstrap'
import { Alert } from 'react-bootstrap';
import Menu from './menu'
import { useSettingsContext } from './providers/settingsProvider'
import { useEffect, useRef, useState } from 'react';
import { Icon } from './icon';
import { useRouter } from 'next/router';
import Socials from './socials';
import Image from 'next/image';
import { useLocationContext } from './providers/locationProvider';
import CustomSelect from './custom-select';

export default function Header(props) {
  const expand = 'false'
  const {settings, menus} = useSettingsContext()
  const optGeneral = settings?.acf.optGeneral || false
  const wordmark = optGeneral?.optLogoWordmark || false
  const sign = '/images/monogram-white.svg'
  const menu = props?.menu || menus?.primary
  const [menuOpen, setMenuOpen] = useState(false)
  const toggleMenu = () => {
    setMenuOpen(!menuOpen)
  }
  const router = useRouter()

  // --- Newsletter signup
  const optFooterBtn = optGeneral?.optFooterBtn || 'Sign up',
        optFooterBtnLink = optGeneral?.optFooterBtnLink || '/signup/',
        optFooterBtnNewtab = optGeneral?.optFooterBtnNewtab ? '_blank' : '_self'

  // --- Booking
  const disableBooking = settings?.acf?.optBooking.disableBooking || false

  // --- Loyalty
  const hasLoyaltyBtn = optGeneral?.optLoyaltyBtn && optGeneral?.optLoyaltyBtnLink
  const hasLoyaltyBtnLabel = optGeneral?.optLoyaltyBtnLabel || 'Le Club'

    // --- Location
  const location = useLocationContext()
  const template = location?.template?.templateName || false
  const isMainLocation = template ? template.toLowerCase().includes('location main') : false
  const [subnavItems, setSubnavItems] = useState([])
  const locationSubnav = useRef(null)

  // --- Subnav items
  useEffect(() => {
    if( !isMainLocation ) return
    // === find all direct children of div.entry-content with id attribute and return a list
    const content = document.querySelector('.entry-content')
    const children = content?.children
    const subnavItems = []
    for (let i = 0; i < children.length; i++) {
      if( children[i].id ) {
        if( children[i].id.includes('block_') ) continue
        subnavItems.push({
          id: children[i].id,
          // make first letter uppercase
          title: children[i].id.replace(/-/g, ' ').replace(/\b\w/g, c => c.toUpperCase())
        })
      }
    }
    // console.log(subnavItems)
    setSubnavItems(subnavItems)



  }, [template])

  // --- Subnav toggle
  useEffect(()=>{
    if( !isMainLocation || !subnavItems.length ) return
    const subnavToggle = ()=>{
      console.log('subnavToggle fired...')
      locationSubnav.current.classList.toggle('subnav-open')
    }
    // === add click event to location name
    locationSubnav?.current.addEventListener('click', subnavToggle)

    return ()=>{ if( locationSubnav?.current ) locationSubnav.current.removeEventListener('click', subnavToggle) }
  }, [subnavItems])


  const handleScrollEvent = ()=>{
    let scrollTop = window.scrollY
    if( scrollTop > 300 ) {
        // console.log('Header: ' + scrollTop)
        document.body.classList.add('nav-shrink')
    }else {
        document.body.classList.remove('nav-shrink')
    }
  }

  useEffect(()=>{
    document.addEventListener('scroll', handleScrollEvent)
    handleScrollEvent()
    // clean hook
    return () => {
        document.removeEventListener('scroll', handleScrollEvent)
    }
  }, [])

  return (
    <>
    <header id="main-header" className={`banner fixed-top text-warm`}>
        {props.preview && (
          <Alert variant='secondary' className='mb-0'>
            <div className="py-10 text-center text-sm text-dusk">
                  This is a page preview.{' '}
                  <Alert.Link href="/api/exit-preview">Click here</Alert.Link> to exit preview mode.
            </div>
          </Alert>
        )}
      <Navbar
      expand={expand}
      variant='dark'
      >
        <Container fluid className='position-relative'>

          <Row className='flex-column flex-lg-row justify-content-md-center align-items-start align-items-md-center justify-content-xl-between flex-grow-1'>
            <div className='col-auto mx-0 mx-md-auto mx-lg-0 my-10 my-md-0'>
              <Link className="navbar-brand lh-1 d-inline-block mb-md-10 mb-xl-0" href="/" title={settings?.general.title}>
                <Icon name={`wordmark-${wordmark}`} className='wordmark-bb' />
              </Link>
            </div>
            <div className='col-auto'>
              <aside className="nav-actions d-md-flex flex-row justify-content-center justify-content-xl-end align-items-center flex-grow-1">
                <Link href='/brasseries/' className={`d-none d-md-block nav-link me-10 me-xl-20${router?.asPath == '/brasseries/' ? ' current-menu-item':''}`}>Brasseries</Link>
                <Link href='/menu/' className={`d-none d-md-block nav-link me-10 me-xl-20${router?.asPath == '/menu/' ? ' current-menu-item':''}`}>Menus</Link>

                {/* --- Loyalty */}
                { hasLoyaltyBtn && (
                    <Link href={optGeneral?.optLoyaltyBtnLink} className='nav-link d-none d-md-block me-10 me-xl-20'>{hasLoyaltyBtnLabel}</Link>
                )}

                {/* Sign up */}
                { !hasLoyaltyBtn && (
                  <Link href={optFooterBtnLink} target={optFooterBtnNewtab} className={`d-none d-md-block nav-link me-10 me-xl-20${optFooterBtnLink == '/signup/' && router?.asPath == '/signup/' ? ' current-menu-item':''}`}>{optFooterBtn}</Link>
                ) }

                {/* --- Book a Table */}
                {!disableBooking && <Link href="/book/" className={`d-none d-md-block btn btn-outline-warm`} >Book Now</Link>}

                {/* Socials */}
                <Socials type="icons" className="ms-10 ms-md-20 d-none d-md-block" />

                <button type="button" className="navbar-toggler ms-10 ms-md-lg-20 d-block" aria-controls="offcanvasNavbar" aria-label="Toggle navigation" onClick={toggleMenu}>
                  <div className={`burger${menuOpen ? ' open':''} btn`}>
                      <div className="icon">
                        <div className="line line--1"></div>
                        <div className="line line--2"></div>
                        <div className="line line--3"></div>
                      </div>
                  </div>
                </button>
              </aside>
            </div>
          </Row>

          <Navbar.Offcanvas
            id={`offcanvasNavbar`}
            aria-labelledby={`offcanvasNavbarLabel`}
            placement="end"
            show={menuOpen}
          >

            <Offcanvas.Body className='d-flex flex-column justify-content-between'>
              {/* Close button */}
              <button type="button" className="navbar-toggler" aria-controls="offcanvasNavbar" aria-label="Toggle navigation" onClick={toggleMenu}>
                <div className={`burger${menuOpen ? ' open':''} btn p-10`}>
                    <div className="icon">
                      <div className="line line--1"></div>
                      <div className="line line--2"></div>
                      <div className="line line--3"></div>
                    </div>
                </div>
              </button>

              <div className='mb-auto'>
                {/* Menu */}
                <Menu items={menu} menuClass="primary-nav" allowDropdowns wrapLinks></Menu>

                {/* --- Book a Table */}
                {!disableBooking && <Link href="/book/" className="btn btn-secondary mt-20" >Book Now</Link>}
              </div>

              <div className="footer-details d-flex align-items-center mt-20 mb-50 mb-lg-25">
                {/* BB sign */}
                <Image src={sign} width={17} height={47} alt={settings?.general.title} className='bb-sign img-fluid' />
                {/* Socials */}
                <Socials type="icons" className="d-none d-lg-block" />
              </div>

            </Offcanvas.Body>

            <div className="offcanvas-submenu">
              <div className="inner">&nbsp;</div>
            </div>

          </Navbar.Offcanvas>
        </Container>
      </Navbar>

      {/* ===== Location subnav ===== */}
      {(isMainLocation && subnavItems.length > 0) && (
        <aside ref={locationSubnav} className="location-subnav bg-green">
          <div className="container-fluid px-0 px-md-20">

            <nav>
              <ul className='list-unstyled d-flex flex-column flex-lg-row justify-content-between m-0'>
                <li className='location-name nav-item ms-lg-0 me-lg-auto'>
                  <span className='nav-link'>{location.title}</span>
                  <span className="dropdown-indicator d-lg-none">
                    <Icon name={`dropdown-arrow`} className='' />
                  </span>
                </li>
                {subnavItems.map((item, index)=> (
                  <li key={`subnav-item-${index}`} className='nav-item'>
                    <a href={`#${item.id}`} className='nav-link'>{item.title}</a>
                  </li>
                ))}
              </ul>
            </nav>

          </div>
        </aside>
      ) }

    </header>

    <aside className="mobile-nav bg-red d-block d-lg-none">
      <ul className='nav-list list-unstyled'>

        {/* Socials */}
        <li className="nav-item">
          <Socials type="icons" />
        </li>

        {/* --- Loyalty */}
        { hasLoyaltyBtn && (
          <li className="nav-item">
            <Link href={optGeneral?.optLoyaltyBtnLink} className='nav-link'>{hasLoyaltyBtnLabel}</Link>
          </li>
        )}

        {/* Sign up */}
        { !hasLoyaltyBtn && (
          <li className="nav-item">
            <Link href={optFooterBtnLink} target={optFooterBtnNewtab} className={`nav-link ${optFooterBtnLink == '/signup/' && router?.asPath == '/signup/' ? ' current-menu-item':''}`}>{optFooterBtn}</Link>
          </li>
        ) }

        {/* Booking */}
        {!disableBooking && (
          <li className='nav-item'>
            <Link href="/book/" className={`nav-link${router?.asPath == '/book/' ? ' current-menu-item':''}`}>Book Now</Link>
          </li>
        )}

      </ul>
    </aside>
    </>
  )
}

import { useRef, isValidElement, useState } from "react"
import { useRouter } from "next/router"
import { motion } from "motion/react";
// -- Lightbox
import Lightbox from "yet-another-react-lightbox";
import Captions from "yet-another-react-lightbox/plugins/captions";
// import Fullscreen from "yet-another-react-lightbox/plugins/fullscreen";
import Thumbnails from "yet-another-react-lightbox/plugins/thumbnails";
import Video from "yet-another-react-lightbox/plugins/video";
import "yet-another-react-lightbox/styles.css";
import "yet-another-react-lightbox/plugins/captions.css";
import "yet-another-react-lightbox/plugins/thumbnails.css";
import { Icon } from "./icon";

export default function Gallery(props) {
    const router = useRouter(),
          slider = useRef()
    let itemsCount = 0


    if( props?.children ) {
      props.children.map((item,index)=>{
        if( item?.type == 'figure' || isValidElement(item) ) itemsCount++
      })
    }

    // console.log(props.children)

    // ===== Lightbox
    const [index, setIndex] = useState(-1);
    const galleryItems = props.children.filter(function(item) {
      if (item?.type) {
        return true
      }else {
        return false
      }
    })
    // console.log(galleryItems)

    const slides = galleryItems.map((item) => {
      let realEl = Array.isArray(item.props.children) ? item.props?.children[0] : item.props?.children
      realEl = realEl.type == "a" ? realEl.props.children : realEl
      const width = realEl.props.width;
      const height = realEl.props.height;
      const src = realEl.props.src
      return realEl.type == 'video' ? {
        type: 'video',
        poster: realEl.props.poster,
        sources: [
          {
            src: src,
            type: "video/mp4",
          },
        ]
      } : {
        src: src,
        width: width,
        height: height,
        description: item.props?.children[1] ? item.props?.children[1]?.props?.children : '',
      }
    })

    // console.log(galleryItems)

    const galleryItem = (item,index)=>{
      let realEl = Array.isArray(item.props.children) ? item.props?.children[0] : item.props?.children
      const type = typeof realEl.type == 'object' ? 'image' : realEl.type
      return (
        <motion.figure
        key={`gallery-item-${index}`}
        className={`${item.props.className}`}
        transition={{ type: "spring", stiffness: 400, damping: 17 }} >
                <div className={`inner`}
                  data-type={type} data-index={index} onClick={() => type !== 'video' ? setIndex(index) : null} >
                    {realEl.type == 'a' ? (
                          realEl.props.children
                    ): (
                        realEl
                    )}
                </div>
                {item.props.children[1]}
        </motion.figure>
      )
    }

    return(
        <div ref={slider} className={`wp-block-gallery row`}>
            {galleryItems && galleryItems.map((item,index)=>{
                if( item?.type == 'figure' ) {
                  return galleryItem(item,index)
                }else if(isValidElement(item)) {
                    return (
                        <motion.div
                            key={`gallery-item-${index}`}
                            className={`wp-block-image`}
                            transition={{ type: "spring", stiffness: 400, damping: 17 }} >
                                <div className={`inner`}>
                                {item}
                                </div>
                        </motion.div>
                    )
                }
            })}

            {!!slides.length && <Lightbox
              plugins={[Captions,Thumbnails, Video]}
              index={index}
              slides={slides}
              captions={{descriptionTextAlign: 'center'}}
              styles={{ root: {
                "--yarl__color_backdrop": "#415143",
                "--yarl__slide_captions_container_background": "rgb(110 163 148 / 50%)",
                "--yarl__color_button": "#FFF9E6",
                "--yarl__button_filter": "none"
              } }}
              carousel={{
                finite: true
              }}
              open={index >= 0}
              close={() => setIndex(-1)}
              video={
                {
                  autoPlay: false,
                  controls: true,
                  // controlsList: string,
                  // crossOrigin: string,
                  // preload: string,
                  // loop: boolean,
                  // muted: boolean,
                  playsInline: true,
                  // disablePictureInPicture: boolean,
                  // disableRemotePlayback: boolean,
                }
              }
              render={{
                iconPrev: () => <Icon name="arrow-down" />,
                iconNext: () => <Icon name="arrow-down" />,
              }}
            />}
        </div>
    )
}
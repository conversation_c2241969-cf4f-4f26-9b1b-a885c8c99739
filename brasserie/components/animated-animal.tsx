//@ts-nocheck
import { useEffect, useId, useRef } from "react"

export default function AnimatedAnimal(props) {
    const animalRef = useRef(null),
          id = useId(),
          type = props.type || 'bird',
          text = props.text || 'Default text to animate',
          itemClass = props.class || '',
          offset = 100,
          ratio = type == 'bee' ? 1.5 : 1.3


    useEffect(()=>{
        if( type == 'video-dog' ) return
        let textPath = animalRef.current.querySelector('.text-path');
        let textContainer = animalRef.current.querySelector('.text-container');
        let path = animalRef.current.querySelector('.text-curve');
        let pathLength = path.getTotalLength();
        // console.log(pathLength);

        function updateTextPathOffset(offset){
            let calcOffset = ['dog-reverted', 'bee'].indexOf(type) != -1 ? window.innerHeight - offset : offset
            textPath.setAttribute('startOffset', calcOffset);
        }

        updateTextPathOffset(pathLength);

        function onScroll(){
            requestAnimationFrame(function(){
                let rect = textContainer.getBoundingClientRect();
                let scrollPercent = rect.y / window.innerHeight;
                // console.log(scrollPercent);
                updateTextPathOffset( scrollPercent * ratio * pathLength );
            });
        }

        window.addEventListener('scroll',onScroll);
        return ()=>{ window.removeEventListener('scroll', onScroll) }
    },[animalRef])

    return (
        <div ref={animalRef} className={`animated-thing ${type} ${itemClass}`}>
            { type == 'video-dog' ? (
                <div className="ratio ratio-dog">
                    <video autoPlay loop muted playsInline="">
                        <source src="https://cms.heartwoodcollection.com/wp-content/uploads/2024/03/4-Dog-1664x880-hevc-safari.mp4" type="video/mp4; codecs=hvc1"></source>
                        <source src="https://cms.heartwoodcollection.com/wp-content/uploads/2024/03/4-Dog-1664x880-vp9-chrome.webm" type="video/webm"></source>
                    </video>
                </div>
            ) : (
            <svg className={`text-container`} viewBox="0 0 1000 200" xmlns="http://www.w3.org/2000/svg">
                <path className="text-curve" id={`el-${id}text-curve`} d="M0 100s269.931 86.612 520 0c250.069-86.612 480 0 480 0" fill="none"/>
                <text y="40" fontSize="16">
                    <textPath className={`text-path`} href={`#el-${id}text-curve`} startOffset={offset}>{text}</textPath>
                </text>
            </svg>
            ) }
        </div>
    )
}

// is this stuck?

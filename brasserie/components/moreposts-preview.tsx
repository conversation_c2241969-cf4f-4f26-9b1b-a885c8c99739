import Link from "next/link"
import CoverImage from "./cover-image"
import AnimatedSection from "./animated-section"

export default function MorePostsPreview({node, layout='stacked'}) {
  const title= node.title,
        slug= node.slug,
        coverImage= node.featuredImage,
        expiryDate = node.singlePost?.expiryDate ? node.singlePost?.expiryDate.replace(':00', '') : false,
        expiryDateNotime = expiryDate ? expiryDate.slice(0, expiryDate.indexOf('at')) : false,
        excerptLength = 130,
        excerpt = node?.excerpt.length > excerptLength+3 ? node.excerpt.slice(0, excerptLength)+'...' : node.excerpt

  const itemHtml = ()=>{
    return(
      <div className={`entry row justify-content-center align-items-center pt-70`}>
        <div className={`col-12 col-lg-${layout == 'scroll' ? '5' : '3'}`}>
          <Link href={`/news/${slug}`} className="d-block">
            {coverImage && <CoverImage title={title} coverImage={coverImage} className="ratio ratio-1x1 overflow-hidden"/>}
          </Link>
        </div>
        <div className={`col-12 col-lg-${ layout == 'scroll' ? '4' : '4'} offset-lg-1`}>
          {expiryDate && <p className="entry-meta h5 text-center mt-0 mb-0">Valid until {expiryDateNotime}</p>}
          <h2 className="mt-10 mt-lg-0 text-center">
            <Link href={`/news/${slug}`} className={`text-decoration-none text-warm`} dangerouslySetInnerHTML={{__html: title}} />
          </h2>
          {excerpt && (<div className="post-excerpt mb-10" dangerouslySetInnerHTML={{ __html: excerpt }}></div>)}
          <div className="entry-buttons text-center">
            <Link href={`/news/${slug}`} className={`btn btn-red m-10`} >
              Read more
            </Link>
          </div>
        </div>
        {layout == 'stacked' && (
          <div className="col-12 col-lg-8">
            <hr className="white mt-70 mb-0" />
          </div>
        )}
      </div>
    )
  }

  return (
    layout == 'scroll' ? (
      <div className="block-gallery-scroll-item">
        <div className="container">
          {itemHtml()}
        </div>
      </div>
    ) : (
      <AnimatedSection>
        {itemHtml()}
      </AnimatedSection>
    )
  )
}
/**
 * Hero Content (ACF)
 * Layout options:
 * - Image
 * - Image Carousel
 * - Video
 */
import { useDeviceSelectors } from 'react-device-detect'
import { useCallback, useEffect, useRef, useState } from 'react'
import { debounce } from '../../lib/utils'
import AnimatedSection from '../animated-section'
import { useSettingsContext } from '../providers/settingsProvider'
import parseHtml from '../../lib/parser'
import Video from '../video'
import Img from '@components/image'
import { Splide, SplideSlide } from '@splidejs/react-splide'

export default function HeroContent({hero, ctx, postType='post', video=null}) {
    const {settings} = useSettingsContext()
    const hpType = hero.hpType || 'image', // (image, carousel, video)
        hpOverlay = hero.hpOverlay || 'none',
        hpCtnBg = hero.hpCtnBg ? 'bg-'+hero.hpCtnBg : '',
        hpTitle = hero.hpTitle,
        hpContent = hero.hpContent || false,
        hpBgImage = hero.hpBgImage || false,
        hpBgImageMobile = hero.hpBgImageMobile || hpBgImage,
        hpCarouselImages = hero.hpCarouselImages || false,
        hpCarouselImagesMob = hero.hpCarouselImagesMob || hpCarouselImages,
        hpVideoAutoplay = hero.hpVideoAutoplay || false,
        autoplay_string = hpVideoAutoplay ? '&autoplay=1&loop=1&muted=1&background=1' : '',
        hpVideoMobile = hero.hpVideoMobile || false,
        hpVideoMobileRatio = hero.hpVideoMobileRatio || 56.25,
        hpVideo = hero.hpVideo || false,
        hpVideoRatio = hero.hpVideoRatio || false,
        heroPanel = useRef(null),
        contentClass = postType == 'room' ? '' : 'mb-50'

    const carouselOptions = {
        type: 'fade',
        rewind : true,
        perPage: 1,
        autoplay: true,
        interval: 5000,
        speed: 800,
        drag: true,
        arrows: false,
        pagination: false,
        pauseOnHover: true,
        pauseOnFocus: false,
        // lazyLoad: 'nearby', // boolean | 'nearby' | 'sequential' = false
    }

    const [state, setState] = useState(()=>{
        return{
            device: null,
            video: hpVideoMobile || hpVideo,
            videoRatio: hpVideoMobileRatio || hpVideoRatio,
            bgImage: hpBgImageMobile || hpBgImage,
            carouselImages: hpCarouselImagesMob || hpCarouselImages
        }
    })
    const savedCallback = useRef(null)

    const updateDeviceCallback = useCallback(() => {
        const [selectors] = useDeviceSelectors(null)
        if(state.device !== selectors ) {
            // changeDevice(selectors)
            setState({
                device: selectors,
                video: selectors.isMobile && !selectors.isTablet ? hpVideoMobile : hpVideo,
                videoRatio: selectors.isMobile && !selectors.isTablet ? hpVideoMobileRatio : hpVideoRatio,
                bgImage: selectors.isMobile && !selectors.isTablet ? hpBgImageMobile : hpBgImage,
                carouselImages: selectors.isMobile && !selectors.isTablet ? hpCarouselImagesMob : hpCarouselImages
            })
        }
    },[state.device])
    const updateDevice = debounce(updateDeviceCallback, 500)

    useEffect(()=>{
        savedCallback.current = updateDeviceCallback
        savedCallback.current()
        // console.log(state.bgImage)
        window.addEventListener("load", updateDevice, false);
        window.addEventListener('resize', updateDevice, false)
        return ()=>{
            window.removeEventListener('load', updateDevice, false)
            window.removeEventListener('resize', updateDevice, false)
        }
    },[])

    const videoOutput = ()=>{
        if(!video) return
        let videoUrl = null
        switch (video) {
            case "woody":
                videoUrl = "https://cms.heartwoodcollection.com/wp-content/uploads/sites/4/2023/07/Heartwood_Collection-Dog_4K_V1000.mp4"
                break
            default:
                break
        }
        if( !videoUrl ) return
        return(
            <figure className="wp-block-video video-about-dog">
                <video autoPlay loop muted playsInline src={videoUrl}></video>
            </figure>
        )
    }

    const handleScrollEvent = ()=>{
        let scrollTop = window.scrollY,
            heroHeight = heroPanel.current.clientHeight
        if( scrollTop < heroHeight ) {
            // console.log('Hero: ' + scrollTop)
            document.body.classList.add('nav-transparent')
        }else {
            document.body.classList.remove('nav-transparent')
        }
    }

    useEffect(()=>{
        document.body.classList.add('has-hero')
        // add scroll event
        document.addEventListener('scroll', handleScrollEvent)
        handleScrollEvent()
        // clean hook
        return () => {
            document.removeEventListener('scroll', handleScrollEvent)
        }
    }, [])

    return(
        <>
        <div ref={heroPanel} className={`panel-hero ${hpCtnBg} has-${hpType} ${hpTitle || hpContent ? '' : 'mb-0'}`}>
            {/* Type: Video */}
            {hpType == 'video' && state.video && (
                <div className={`position-relative has-overlay-${hpOverlay}`}>
                {/* <AnimatedSection> */}
                    <div // @ts-ignore
                        className={`hero-video mx-auto mb-50 ratio`} style={{"--bs-aspect-ratio": state.videoRatio+"%"}}>
                        <Video video={`${state.video}?title=0&byline=0&portrait=0&dnt=1${autoplay_string}`} title={hpTitle || settings?.general.title + " hero video"} />
                    </div>
                    <div className={`panel-hero-overlay bg-${hpOverlay}`}>&nbsp;</div>
                {/* </AnimatedSection> */}
                </div>
            )}
            {/* Type: Carousel */}
            {hpType == 'carousel' && state.carouselImages && (
                <div className={`position-relative has-overlay-${hpOverlay}`}>

                    <AnimatedSection className={`mx-auto ratio ratio-9x16 ratio-lg-16x9 edge-2-edge`}>
                        <Splide tag="div" className="carousel position-absolute" role='region' aria-label={`${hpTitle || ctx.title} hero carousel`}
                                options={ carouselOptions }
                                >
                                { state.carouselImages.map((item, index) => (
                                    <SplideSlide key={`hero-slide-${index}`} className="slide" role='figure' aria-label={`Slide ${index+1} of ${state.carouselImages.length}`}>
                                        <div className="ratio ratio-9x16 ratio-lg-16x9">
                                            <Img image={item} className="img-fluid" alt={item?.altText || `${hpTitle || ctx.title} - slide ${index+1}`} priority={index==0}
                                            width={state.device?.isMobile && !state.device?.isTablet ? 400 : 1350}
                                            height={state.device?.isMobile && !state.device?.isTablet ? 300 : 768}
                                            />
                                        </div>
                                    </SplideSlide>
                                )) }
                        </Splide>
                        <div className={`panel-hero-overlay bg-${hpOverlay}`}>&nbsp;</div>
                    </AnimatedSection>

                    {(hpTitle || hpContent) && (
                    <div className="container position-absolute inset-0 d-flex flex-column">
                        <div className="panel-hero-row row align-items-center flex-grow-1">
                            <div className="col-12 ">
                                <div className="row text-center text-lg-start">
                                    <div className="col-12 col-lg-4 offset-lg-1">

                                    </div>
                                    <div className="col-12 col-lg-5 offset-lg-1 text-warm">
                                        {hpTitle && <h1 className="title mt-0" dangerouslySetInnerHTML={{__html: hpTitle}}></h1>}
                                        {hpContent && parseHtml(hpContent)}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    )}

                </div>
            )}
            {/* Type: Image */}
            <div className="overflow-hidden">
            {(hpType == 'image' && state.bgImage) && (
                <AnimatedSection className="hero-img-wrap position-relative">

                    {/* TODO: I added responsive ratio classes; change to srcset */}
                    <div className="ratio ratio-9x13 mx-auto overflow-hidden d-block d-md-none">
                        {hpBgImageMobile &&
                            <Img image={hpBgImageMobile} className="img-fluid mx-auto d-block" alt={hpTitle || ctx.title} priority={true} />}
                        <div className={`panel-hero-overlay bg-${hpOverlay}`}>&nbsp;</div>
                    </div>
                    <div className="ratio ratio-16x9 mx-auto overflow-hidden d-none d-md-block">
                        {hpBgImage &&
                            <Img image={hpBgImage} className="img-fluid mx-auto d-block" alt={hpTitle || ctx.title} priority={true} />}
                        <div className={`panel-hero-overlay bg-${hpOverlay}`}>&nbsp;</div>
                    </div>
                    <aside className='scroll-prompt position-absolute w-100 d-flex justify-content-center'>
                        <div className='text-center bg-plaster p-10 w-auto rounded-circle'>
                            <svg className='icon-arrow-down' fill='currentColor' viewBox="0 0 12 18">
                                <use href={`/images/icon.svg` + `#arrow-down`} />
                            </svg>
                        </div>
                    </aside>
                </AnimatedSection>
            )}
            </div>

            {hpType != 'carousel' && (hpTitle || hpContent) && (
                <div className="container">
                    <div className={`panel-hero-row mt-50 row align-items-center${hpType=='image' && !hpBgImage ? ' pt-50':''}`}>
                        <div className="col-12">
                            <div className={`row text-center text-lg-start ${contentClass}`}>
                                <div className="col-12 col-lg-4 offset-lg-1">
                                    {hpTitle && (
                                        <h1 className="title mt-0" dangerouslySetInnerHTML={{__html: hpTitle}}></h1>
                                    )}
                                    {videoOutput()}
                                </div>
                                <div className="col-12 col-lg-5 offset-lg-1">
                                    {/* {hpContent && (<div dangerouslySetInnerHTML={{__html: hpContent}} />)} */}
                                    {hpContent && parseHtml(hpContent)}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
        <hr className='m-0'/>
        </>
    )
}
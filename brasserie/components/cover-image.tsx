import cn from 'classnames'
import Image from 'next/image'

interface Props {
  title: string
  coverImage: {
    node: {
      sourceUrl: string
      mediaDetails?: {
        sizes: {
          width: number
          height: number
        }[]
        width
        height
      }
    }
  },
  className?: string
}

export default function CoverImage({ title, coverImage, className='' }: Props) {
  const width = coverImage.node?.mediaDetails?.sizes ? coverImage.node?.mediaDetails.sizes[0].width : coverImage.node?.mediaDetails.width,
        height = coverImage.node?.mediaDetails?.sizes ? coverImage.node?.mediaDetails.sizes[0].height : coverImage.node?.mediaDetails.height
  // console.log(width, height)
  const image = (
    <Image
      width={width}
      height={height}
      alt={`Cover Image for ${title}`}
      src={coverImage?.node.sourceUrl}
      className="img-fluid"
    />
  )
  return (
    <div className={`featured-image text-center ${className}`}>
        {image}
    </div>
  )
}

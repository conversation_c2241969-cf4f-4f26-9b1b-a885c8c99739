export const Icon = ({ name, color = "currentColor", className='' }) => {
  const viewbox = {
    "facebook": "0 0 56.69 56.69",
    "linkedin": "0 0 56.69 56.69",
    "twitter": "0 0 56.69 56.69",
    "instagram": "0 0 56.69 56.69",
    "arrow-down": "0 0 12 18",
    "close": "0 0 30.001 24.001",
    "rene": "0 0 141.73 163.12",
    "drinks": "0 0 128 128",
    "dog-walks": "0 0 128 128",
    "child-entertainment": "0 0 128 128",
    "evening-entertainment": "0 0 128 128",
    "live-music": "0 0 128 128",
    "pre-theatre": "0 0 128 128",
    "quiz-night": "0 0 128 128",
    "roasts": "0 0 128 128",
    "seasonal-menu": "0 0 128 128",
    "steak-night": "0 0 128 128",
    "wordmark-bb-logo": "0 0 573.73 14.56",
    "wordmark-bb-logo-round": "0 0 130.3 130.9",
    "download-on-app-store": "0 0 119.66 40",
    "get-it-on-google-play": "0 0 135 40",
    "dropdown-arrow": "0 0 16 12",
  }
  return (
    <svg className={`icon-${name} ${className}`} fill={color} viewBox={viewbox[name]}>
      <use href={`/images/icon.svg` + `#${name}`} />
    </svg>
  )
}
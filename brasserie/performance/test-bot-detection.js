/**
 * Test script for bot detection functionality
 * Run with: node scripts/test-bot-detection.js
 */

// Bot detection functions (JavaScript version for testing)
const BOT_USER_AGENTS = [
  // PageSpeed Insights
  'Chrome-Lighthouse',
  'PageSpeed Insights',
  'Google Page Speed Insights',
  'PTST',

  // Lighthouse
  'lighthouse',
  'Lighthouse',

  // Google bots
  'Googlebot',
  'GoogleBot',
  'Google-PageRenderer',
  'Google-InspectionTool',
  'Google-Read-Aloud',
  'Google-Structured-Data-Testing-Tool',

  // Other performance testing tools
  'GTmetrix',
  'WebPageTest',
  'Pingdom',
  'Site24x7',
  'UptimeRobot',
  'StatusCake',

  // SEO crawlers
  'bingbot',
  'Bingbot',
  'Slurp',
  'DuckDuckBot',
  'Baiduspider',
  'YandexBot',
  'facebookexternalhit',
  'Twitterbot',
  'LinkedInBot',
  'WhatsApp',
  'Applebot',

  // Generic bot patterns
  'bot',
  'Bo<PERSON>',
  'crawler',
  'Crawler',
  'spider',
  'Spider',
  'scraper',
  'Scraper'
];

function isBotUserAgent(userAgent) {
  if (!userAgent) return false;

  const lowerUserAgent = userAgent.toLowerCase();
  return BOT_USER_AGENTS.some(botAgent =>
    lowerUserAgent.includes(botAgent.toLowerCase())
  );
}

function isBot(userAgent, headers) {
  // Check user agent
  if (isBotUserAgent(userAgent)) {
    return true;
  }

  // Server-side additional checks
  if (headers) {
    // Check for bot-specific headers
    if (headers['x-purpose'] === 'preview' ||
        headers['purpose'] === 'prefetch' ||
        headers['x-moz'] === 'prefetch') {
      return true;
    }

    // Check for missing common browser headers
    if (!headers['accept-language'] || !headers['accept-encoding']) {
      return true;
    }
  }

  return false;
}

// Test user agents
const testCases = [
  // PageSpeed Insights bots
  { userAgent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36 Chrome-Lighthouse', expected: true, name: 'Chrome Lighthouse' },
  { userAgent: 'Mozilla/5.0 (compatible; Google Page Speed Insights)', expected: true, name: 'PageSpeed Insights' },

  // Google bots
  { userAgent: 'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)', expected: true, name: 'Googlebot' },
  { userAgent: 'Mozilla/5.0 (Linux; Android 6.0.1; Nexus 5X Build/MMB29P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Mobile Safari/537.36 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)', expected: true, name: 'Googlebot Mobile' },

  // Performance testing tools
  { userAgent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36 GTmetrix', expected: true, name: 'GTmetrix' },
  { userAgent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36 WebPageTest', expected: true, name: 'WebPageTest' },

  // Real browsers (should not be detected as bots)
  { userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36', expected: false, name: 'Chrome Windows' },
  { userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36', expected: false, name: 'Chrome macOS' },
  { userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15', expected: false, name: 'Safari macOS' },
  { userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0', expected: false, name: 'Firefox Windows' },

  // Social media crawlers
  { userAgent: 'facebookexternalhit/1.1 (+http://www.facebook.com/externalhit_uatext.php)', expected: true, name: 'Facebook Bot' },
  { userAgent: 'Twitterbot/1.0', expected: true, name: 'Twitter Bot' },
];

console.log('🤖 Testing Bot Detection\n');
console.log('=' .repeat(80));

let passed = 0;
let failed = 0;

testCases.forEach((testCase) => {
  const result = isBotUserAgent(testCase.userAgent);
  const status = result === testCase.expected ? '✅ PASS' : '❌ FAIL';

  if (result === testCase.expected) {
    passed++;
  } else {
    failed++;
  }

  console.log(`${status} ${testCase.name}`);
  console.log(`   Expected: ${testCase.expected}, Got: ${result}`);
  console.log(`   User Agent: ${testCase.userAgent.substring(0, 80)}...`);
  console.log('');
});

console.log('=' .repeat(80));
console.log(`\n📊 Results: ${passed} passed, ${failed} failed`);

if (failed === 0) {
  console.log('🎉 All tests passed! Bot detection is working correctly.');
} else {
  console.log('⚠️  Some tests failed. Please review the bot detection logic.');
  process.exit(1);
}

// Test server-side detection with headers
console.log('\n🌐 Testing Server-side Detection with Headers\n');

const serverTestCases = [
  {
    userAgent: 'Chrome-Lighthouse',
    headers: {
      'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
      'accept-language': 'en-US,en;q=0.5',
      'accept-encoding': 'gzip, deflate'
    },
    expected: true,
    name: 'Lighthouse with normal headers'
  },
  {
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    headers: {
      // Missing common browser headers - suspicious
    },
    expected: true,
    name: 'Suspicious request with missing headers'
  },
  {
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    headers: {
      'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
      'accept-language': 'en-US,en;q=0.5',
      'accept-encoding': 'gzip, deflate'
    },
    expected: false,
    name: 'Normal browser request'
  }
];

serverTestCases.forEach((testCase) => {
  const result = isBot(testCase.userAgent, testCase.headers);
  const status = result === testCase.expected ? '✅ PASS' : '❌ FAIL';

  console.log(`${status} ${testCase.name}`);
  console.log(`   Expected: ${testCase.expected}, Got: ${result}`);
  console.log('');
});

console.log('🔍 Bot detection testing complete!');

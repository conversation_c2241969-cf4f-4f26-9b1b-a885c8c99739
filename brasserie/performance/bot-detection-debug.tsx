import React, { useState, useEffect } from 'react'
import { useIsBotDetection, quickBotCheck } from './bot-detection'

interface BotDetectionDebugProps {
  show?: boolean
}

/**
 * Debug component to show bot detection status
 * Only shows in development mode by default
 */
export default function BotDetectionDebug({ show = process.env.NODE_ENV === 'development' }: BotDetectionDebugProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [userAgent, setUserAgent] = useState('')
  const [botMeta, setBotMeta] = useState('')
  const isBot = useIsBotDetection()
  const quickBot = quickBotCheck()

  useEffect(() => {
    if (typeof window !== 'undefined') {
      setUserAgent(navigator.userAgent)
      const meta = document.querySelector('meta[name="x-is-bot"]')
      setBotMeta(meta ? meta.getAttribute('content') || '' : 'not found')
    }
  }, [])

  if (!show) return null

  return (
    <>
      {/* Toggle button */}
      <button
        onClick={() => setIsVisible(!isVisible)}
        style={{
          position: 'fixed',
          bottom: '20px',
          right: '20px',
          zIndex: 9999,
          backgroundColor: isBot ? '#ff4444' : '#44ff44',
          color: 'white',
          border: 'none',
          borderRadius: '50%',
          width: '60px',
          height: '60px',
          fontSize: '24px',
          cursor: 'pointer',
          boxShadow: '0 2px 10px rgba(0,0,0,0.3)'
        }}
        title="Bot Detection Debug"
      >
        🤖
      </button>

      {/* Debug panel */}
      {isVisible && (
        <div
          style={{
            position: 'fixed',
            bottom: '90px',
            right: '20px',
            zIndex: 9998,
            backgroundColor: 'white',
            border: '1px solid #ccc',
            borderRadius: '8px',
            padding: '16px',
            maxWidth: '400px',
            boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
            fontSize: '14px',
            fontFamily: 'monospace'
          }}
        >
          <h3 style={{ margin: '0 0 12px 0', fontSize: '16px', fontWeight: 'bold' }}>
            🤖 Bot Detection Debug
          </h3>

          <div style={{ marginBottom: '8px' }}>
            <strong>Status:</strong>{' '}
            <span style={{
              color: isBot ? '#ff4444' : '#44ff44',
              fontWeight: 'bold'
            }}>
              {isBot ? 'BOT DETECTED' : 'HUMAN USER'}
            </span>
          </div>

          <div style={{ marginBottom: '8px' }}>
            <strong>Quick Check:</strong>{' '}
            <span style={{ color: quickBot ? '#ff4444' : '#44ff44' }}>
              {quickBot ? 'BOT' : 'HUMAN'}
            </span>
          </div>

          <div style={{ marginBottom: '8px' }}>
            <strong>Server Meta:</strong>{' '}
            <span style={{ color: botMeta === '1' ? '#ff4444' : '#44ff44' }}>
              {botMeta}
            </span>
          </div>

          <div style={{ marginBottom: '12px' }}>
            <strong>User Agent:</strong>
            <div style={{
              backgroundColor: '#f5f5f5',
              padding: '8px',
              borderRadius: '4px',
              marginTop: '4px',
              wordBreak: 'break-all',
              fontSize: '12px'
            }}>
              {userAgent || 'Loading...'}
            </div>
          </div>

          <div style={{ marginBottom: '12px' }}>
            <strong>Third-party Scripts:</strong>
            <div style={{ color: isBot ? '#ff4444' : '#44ff44' }}>
              {isBot ? '❌ BLOCKED' : '✅ LOADED'}
            </div>
          </div>

          <div style={{ fontSize: '12px', color: '#666' }}>
            <strong>Performance Impact:</strong><br />
            {isBot ? 'Scripts blocked for better PageSpeed scores' : 'All tracking scripts active'}
          </div>

          <button
            onClick={() => setIsVisible(false)}
            style={{
              marginTop: '12px',
              backgroundColor: '#007cba',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              padding: '6px 12px',
              cursor: 'pointer',
              fontSize: '12px'
            }}
          >
            Close
          </button>
        </div>
      )}
    </>
  )
}

/**
 * Simple inline debug info (for development)
 */
export function BotDetectionInlineDebug() {
  const isBot = useIsBotDetection()

  if (process.env.NODE_ENV !== 'development') return null

  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      left: '10px',
      backgroundColor: isBot ? '#ff4444' : '#44ff44',
      color: 'white',
      padding: '4px 8px',
      borderRadius: '4px',
      fontSize: '12px',
      fontWeight: 'bold',
      zIndex: 9999
    }}>
      {isBot ? '🤖 BOT' : '👤 HUMAN'}
    </div>
  )
}

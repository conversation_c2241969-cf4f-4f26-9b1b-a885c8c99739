# PageSpeed Optimization Guide

This document outlines the comprehensive strategy implemented to achieve the best possible PageSpeed Insights scores by preventing third-party scripts from loading for performance testing bots.

## Overview

The optimization strategy focuses on:
1. **Client-Side Bot Detection** - Identifying PageSpeed Insights, Lighthouse, and other performance testing bots after page load
2. **Dynamic Script Loading** - Preventing third-party scripts from loading for bots using runtime detection
3. **ISR/SSG Compatibility** - Works with Incremental Static Regeneration and Static Site Generation
4. **Performance Headers** - Adding optimal caching and performance headers
5. **Next.js Optimizations** - Leveraging Next.js performance features

## The ISR/SSG Challenge

When using ISR or static generation, traditional server-side bot detection doesn't work because:

1. **Pages are pre-generated** with `isBot = false` during build time
2. **Bots receive cached static HTML** with scripts already included
3. **Bot detection happens too late** in the process

## Solution: Runtime Dynamic Loading

Our solution moves bot detection to **runtime** rather than build time:

- Bot detection happens **after page load** on the client-side
- Scripts are **dynamically injected** only for real users
- **No scripts in initial HTML** for bots visiting static pages

## Implementation

### 1. Bot Detection System (`performance/bot-detection.ts`)

The bot detection system identifies performance testing bots using multiple methods:

- **User Agent Detection**: Checks for known bot user agents including:
  - Chrome-Lighthouse
  - PageSpeed Insights
  - Googlebot
  - GTmetrix, WebPageTest, Pingdom
  - And many more...

- **Client-side Detection**: Identifies headless browsers and automation tools
- **Runtime State Management**: Uses React hooks for delayed detection

```typescript
export function useIsBotDetection(): boolean {
  const [isDetectedBot, setIsDetectedBot] = useState(false);
  const [hasChecked, setHasChecked] = useState(false);

  useEffect(() => {
    // Only run on client-side after component mounts
    if (typeof window !== 'undefined' && !hasChecked) {
      // Small delay to ensure page is fully loaded
      const timeoutId = setTimeout(() => {
        const botDetected = isBot();
        setIsDetectedBot(botDetected);
        setHasChecked(true);
      }, 100);
    }
  }, [hasChecked]);

  return isDetectedBot;
}
```

### 2. Dynamic Script Loading

#### **`performance/dynamic-script-loader.tsx`**
- Wrapper component for conditional rendering
- Only renders children for real users after bot detection
- Provides hooks for dynamic script injection

#### **`components/cookiebot.tsx`** - Modified Components:
- Google Tag Manager (GTM) - Only loads for real users
- Stellar tracking - Skipped for bots
- Atreemo tracking - Skipped for bots

```typescript
<DynamicScriptLoader>
    {/* All third-party scripts wrapped here */}
    {stellarID && (
        <Head>
            <script async src={`https://stellar.js?apiKey=${stellarID}`}></script>
        </Head>
    )}

    {gtm && (
        <Head>
            {/* GTM scripts */}
        </Head>
    )}
</DynamicScriptLoader>
```

### 3. Next.js Configuration Optimizations

**`next.config.js`**
- SWC minification enabled
- Compression enabled
- Powered-by header removed
- Scroll restoration
- Performance headers for static assets
- Proper caching strategies

```javascript
const nextConfig = {
  swcMinify: true,
  compress: true,
  poweredByHeader: false,

  experimental: {
    scrollRestoration: true,
  },

  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          { key: 'X-DNS-Prefetch-Control', value: 'on' },
          { key: 'X-Frame-Options', value: 'DENY' },
          { key: 'X-Content-Type-Options', value: 'nosniff' },
        ]
      }
    ]
  }
}
```

## Usage Examples

### Basic Dynamic Script Loading
```typescript
import DynamicScriptLoader from '../performance/dynamic-script-loader'

function MyComponent() {
  return (
    <DynamicScriptLoader>
      <script src="heavy-third-party-script.js" />
    </DynamicScriptLoader>
  )
}
```

### Using the Bot Detection Hook
```typescript
import { useIsBotDetection } from '../performance/bot-detection'

function MyComponent() {
  const isBot = useIsBotDetection()

  useEffect(() => {
    if (!isBot) {
      // Load heavy resources only for real users
      loadHeavyResource()
    }
  }, [isBot])
}
```

### Dynamic Script Injection Hook
```typescript
import { useDynamicScript } from '../performance/dynamic-script-loader'

function MyComponent() {
  const { loaded, error, isBot } = useDynamicScript(
    'https://example.com/script.js',
    { id: 'my-script', async: true }
  )

  return (
    <div>
      {loaded && !isBot && <p>Script loaded for real user!</p>}
      {isBot && <p>Script blocked for bot</p>}
    </div>
  )
}
```

## Testing

### 1. Verify Bot Detection
**Normal User Test:**
1. Open your site in a browser
2. Open DevTools Console
3. Look for: `🤖 Bot Detection Result: { isBot: false }`
4. Scripts should load after ~200ms delay

**Bot Simulation Test:**
1. Open DevTools → Network tab
2. Change User Agent to "Chrome-Lighthouse"
3. Reload page
4. Look for: `🤖 Bot Detection Result: { isBot: true }`
5. Scripts should NOT load

### 2. Test Bot Detection Script
```bash
cd brasserie && node performance/test-bot-detection.js
```

### 3. Test with PageSpeed Insights
1. Deploy to staging/production
2. Run https://pagespeed.web.dev/ on your site
3. Verify third-party scripts are not loaded
4. Check improved performance scores

### 4. Debug Component
Add the debug component temporarily for testing:
```typescript
import BotDetectionDebug from '../performance/bot-detection-debug'

// In your component
<BotDetectionDebug />
```

### Performance Monitoring
- Monitor Core Web Vitals
- Check Lighthouse scores regularly
- Verify third-party script blocking is working

## Benefits

1. **Improved PageSpeed Scores**: Eliminates third-party script overhead for performance tests
2. **Better Core Web Vitals**: Reduced JavaScript execution time and network requests
3. **ISR/SSG Compatible**: Works perfectly with static generation and caching
4. **Maintained Functionality**: Real users still get full tracking and functionality
5. **SEO Benefits**: Better performance scores can improve search rankings
6. **User Experience**: Faster loading for performance-critical scenarios

## Architecture Benefits

- **Runtime Detection**: Bot detection happens after page load, not during build
- **Zero Impact on Static Generation**: Bots get clean HTML without scripts
- **Progressive Enhancement**: Scripts load dynamically for real users
- **Debugging Support**: Built-in logging and debug components
- **Flexible**: Easy to add new scripts or modify detection logic

## Maintenance

- Regularly update bot user agent list in `performance/bot-detection.ts`
- Monitor for new performance testing tools
- Test with actual PageSpeed Insights to verify effectiveness
- Update documentation when adding new third-party scripts
- Check debug logs in development for detection accuracy

## Files Modified

- `performance/bot-detection.ts` - Core bot detection logic
- `performance/dynamic-script-loader.tsx` - Dynamic loading wrapper
- `performance/bot-detection-debug.tsx` - Debug tools
- `performance/performance-optimized.tsx` - Performance wrapper component
- `performance/test-bot-detection.js` - Testing utilities
- `performance/PAGESPEED_OPTIMIZATION.md` - This documentation
- `components/cookiebot.tsx` - Third-party script management
- `next.config.js` - Performance optimizations

## Notes

- Bot detection is conservative to avoid false positives
- Real users always get full functionality
- Client-side detection provides ISR/SSG compatibility
- Scripts load with a small delay (~200ms) to ensure accurate detection
- Debug logging helps verify the system is working correctly

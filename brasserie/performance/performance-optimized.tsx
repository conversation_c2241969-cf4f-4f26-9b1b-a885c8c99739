import React, { ReactNode } from 'react'
import { useIsBotDetection } from './bot-detection'

interface PerformanceOptimizedProps {
  children: ReactNode
  fallback?: ReactNode
  loadForBots?: boolean
  className?: string
}

/**
 * Component that conditionally renders children based on bot detection
 * Useful for preventing heavy third-party scripts from loading for performance testing bots
 */
export default function PerformanceOptimized({
  children,
  fallback = null,
  loadForBots = false,
  className
}: PerformanceOptimizedProps) {
  const isBot = useIsBotDetection()

  // If it's a bot and we shouldn't load for bots, show fallback
  if (isBot && !loadForBots) {
    return fallback ? <div className={className}>{fallback}</div> : null
  }

  // Otherwise render children
  return <div className={className}>{children}</div>
}

/**
 * Hook to conditionally execute effects based on bot detection
 */
export function usePerformanceOptimized(loadForBots: boolean = false) {
  const isBot = useIsBotDetection()
  return !isBot || loadForBots
}

/**
 * Higher-order component for performance optimization
 */
export function withPerformanceOptimization<P extends object>(
  Component: React.ComponentType<P>,
  loadForBots: boolean = false
) {
  return function PerformanceOptimizedComponent(props: P) {
    const shouldLoad = usePerformanceOptimized(loadForBots)

    if (!shouldLoad) {
      return null
    }

    return <Component {...props} />
  }
}

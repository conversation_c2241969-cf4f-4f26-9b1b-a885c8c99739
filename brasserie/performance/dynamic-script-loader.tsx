"use client";
import { useEffect, useState } from 'react';
import { useIsBotDetection } from './bot-detection';

interface DynamicScriptLoaderProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * Component that dynamically loads scripts only for real users (not bots)
 * This ensures scripts are not included in the initial HTML for ISR/SSG pages
 */
export default function DynamicScriptLoader({ children, fallback = null }: DynamicScriptLoaderProps) {
  const [shouldRender, setShouldRender] = useState(false);
  const isBot = useIsBotDetection();

  useEffect(() => {
    // Wait for bot detection to complete, then decide whether to render scripts
    const timer = setTimeout(() => {
      if (!isBot) {
        console.log('✅ DynamicScriptLoader: Loading scripts for real user');
        setShouldRender(true);
      } else {
        console.log('🚫 DynamicScriptLoader: Blocking scripts for bot');
      }
    }, 200); // Small delay to ensure bot detection is complete

    return () => clearTimeout(timer);
  }, [isBot]);

  // Don't render anything during SSR/SSG
  if (typeof window === 'undefined') {
    return null;
  }

  // If it's a bot, show fallback or nothing
  if (isBot) {
    return fallback ? <>{fallback}</> : null;
  }

  // If we haven't decided yet, don't render
  if (!shouldRender) {
    return null;
  }

  // Render the scripts for real users
  return <>{children}</>;
}

/**
 * Hook to dynamically inject scripts into the document
 */
export function useDynamicScript(src: string, options: {
  id?: string;
  async?: boolean;
  defer?: boolean;
  onLoad?: () => void;
  onError?: () => void;
} = {}) {
  const [loaded, setLoaded] = useState(false);
  const [error, setError] = useState(false);
  const isBot = useIsBotDetection();

  useEffect(() => {
    // Don't load scripts for bots
    if (isBot) {
      console.log(`🚫 Blocking script for bot: ${src}`);
      return;
    }

    // Check if script already exists
    const existingScript = options.id ? document.getElementById(options.id) : null;
    if (existingScript) {
      setLoaded(true);
      return;
    }

    console.log(`✅ Loading script for real user: ${src}`);

    const script = document.createElement('script');
    script.src = src;
    script.async = options.async ?? true;
    script.defer = options.defer ?? false;

    if (options.id) {
      script.id = options.id;
    }

    script.onload = () => {
      setLoaded(true);
      options.onLoad?.();
    };

    script.onerror = () => {
      setError(true);
      options.onError?.();
    };

    document.head.appendChild(script);

    return () => {
      if (script.parentNode) {
        script.parentNode.removeChild(script);
      }
    };
  }, [src, isBot, options.id, options.async, options.defer, options.onLoad, options.onError]);

  return { loaded, error, isBot };
}

/**
 * Hook to dynamically inject inline scripts
 */
export function useDynamicInlineScript(scriptContent: string, dependencies: any[] = []) {
  const [executed, setExecuted] = useState(false);
  const isBot = useIsBotDetection();

  useEffect(() => {
    // Don't execute scripts for bots
    if (isBot || executed) {
      if (isBot) {
        console.log('🚫 Blocking inline script for bot');
      }
      return;
    }

    console.log('✅ Executing inline script for real user');

    try {
      // Create and execute script
      const script = document.createElement('script');
      script.textContent = scriptContent;
      document.head.appendChild(script);
      document.head.removeChild(script);
      setExecuted(true);
    } catch (error) {
      console.error('Error executing inline script:', error);
    }
  }, [scriptContent, isBot, executed, ...dependencies]);

  return { executed, isBot };
}

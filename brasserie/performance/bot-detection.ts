/**
 * Bot Detection Utility for PageSpeed Optimization
 * Detects various bots including PageSpeed Insights, Lighthouse, and other crawlers
 * to prevent third-party scripts from loading and improve performance scores
 */

// List of bot user agents to detect
const BOT_USER_AGENTS = [
  // === PageSpeed Insights
  'Chrome-Lighthouse',
  'PageSpeed Insights',
  'Google Page Speed Insights',
  'PTST',

  // === Lighthouse
  'lighthouse',
  'Lighthouse',

  // === Google bots
  'Googlebot',
  'GoogleBot',
  'Google-PageRenderer',
  'Google-InspectionTool',
  'Google-Read-Aloud',
  'Google-Structured-Data-Testing-Tool',

  // === Other performance testing tools
  'GTmetrix',
  'WebPageTest',
  'Pingdom',
  'Site24x7',
  // 'UptimeRobot',
  'StatusCake',

  // === SEO crawlers
  'bingbot',
  'Bingbot',
  'Slurp',
  'DuckDuckBot',
  'Baiduspider',
  'YandexBot',
  'facebookexternalhit',
  'Twitterbot',
  'LinkedInBot',
  'WhatsApp',
  'Applebot',

  // === Generic bot patterns
  // 'bot',
  // 'Bo<PERSON>',
  // 'crawler',
  // 'Crawler',
  // 'spider',
  // 'Spider',
  // 'scraper',
  // 'Scraper'
];

/**
 * Check if the current request is from a bot based on user agent
 */
export function isBotUserAgent(userAgent?: string): boolean {
  if (typeof window === 'undefined') {
    // Server-side: use provided userAgent or return false
    if (!userAgent) return false;
  } else {
    // Client-side: use navigator.userAgent
    userAgent = navigator.userAgent;
  }

  if (!userAgent) return false;

  // Convert to lowercase for case-insensitive matching
  const lowerUserAgent = userAgent.toLowerCase();

  // Check against known bot user agents
  return BOT_USER_AGENTS.some(botAgent =>
    lowerUserAgent.includes(botAgent.toLowerCase())
  );
}

/**
 * Check if the current request is from a bot based on various indicators
 */
export function isBot(userAgent?: string, headers?: Record<string, string>): boolean {
  // Check user agent
  if (isBotUserAgent(userAgent)) {
    return true;
  }

  // Server-side additional checks
  if (typeof window === 'undefined' && headers) {
    // Check for bot-specific headers
    const botHeaders = [
      'x-forwarded-for',
      'x-real-ip',
      'cf-connecting-ip'
    ];

    // Check for headless browser indicators
    if (headers['x-purpose'] === 'preview' ||
        headers['purpose'] === 'prefetch' ||
        headers['x-moz'] === 'prefetch') {
      return true;
    }

    // Check for missing common browser headers
    if (!headers['accept-language'] || !headers['accept-encoding']) {
      return true;
    }
  }

  // Client-side additional checks
  if (typeof window !== 'undefined') {
    // Check for headless browser indicators
    const windowAny = window as any;
    if (
      windowAny.navigator?.webdriver ||
      windowAny.phantom ||
      windowAny.callPhantom ||
      windowAny._phantom ||
      windowAny.__nightmare ||
      windowAny.Buffer ||
      windowAny.emit ||
      windowAny.spawn
    ) {
      return true;
    }

    // Check for missing properties that real browsers have
    // @ts-ignore - chrome and safari properties may not exist in all browsers
    if (!(window as any).chrome && !(window as any).safari && navigator.userAgent.includes('Chrome')) {
      return true;
    }

    // Check for automation indicators
    if (window.outerHeight === 0 || window.outerWidth === 0) {
      return true;
    }
  }

  return false;
}

import { useState, useEffect } from 'react';

/**
 * Hook for React components to check if current visitor is a bot
 * Uses client-side detection for ISR compatibility
 */

export function useIsBotDetection(): boolean {
  const [isDetectedBot, setIsDetectedBot] = useState(false);
  const [hasChecked, setHasChecked] = useState(false);

  useEffect(() => {
    // Only run on client-side after component mounts
    if (typeof window !== 'undefined' && !hasChecked) {
      // Small delay to ensure page is fully loaded
      const timeoutId = setTimeout(() => {
        const botDetected = isBot();
        setIsDetectedBot(botDetected);
        setHasChecked(true);

        // Debug logging
        // console.log('🤖 Bot Detection Result:', {
        //   userAgent: navigator.userAgent,
        //   isBot: botDetected,
        //   timestamp: new Date().toISOString()
        // });
      }, 100);

      return () => clearTimeout(timeoutId);
    }
  }, [hasChecked]);

  // Always return false during SSR/SSG for hydration consistency
  if (typeof window === 'undefined') {
    return false;
  }

  return isDetectedBot;
}

/**
 * Check if third-party scripts should be loaded
 * Returns false for bots to improve performance scores
 */
export function shouldLoadThirdPartyScripts(userAgent?: string, headers?: Record<string, string>): boolean {
  return !isBot(userAgent, headers);
}

/**
 * Get a performance-optimized user agent check for critical path
 */
export function quickBotCheck(): boolean {
  if (typeof window === 'undefined') return false;

  const ua = navigator.userAgent.toLowerCase();
  return ua.includes('lighthouse') ||
         ua.includes('pagespeed') ||
         ua.includes('chrome-lighthouse') ||
         ua.includes('googlebot');
}

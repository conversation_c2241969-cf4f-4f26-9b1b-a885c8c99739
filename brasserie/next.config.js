if (!process.env.WORDPRESS_API_URL) {
  throw new Error(`
    Please provide a valid WordPress instance URL.
    Add to your environment variables WORDPRESS_API_URL.
  `)
}

const path = require('path')

/** @type {import('next').NextConfig} */
const nextConfig = {
  // Match Wordpress
  trailingSlash: true,

  // Performance optimizations
  swcMinify: true,
  compress: true,
  poweredByHeader: false,

  // Experimental features for better performance
  experimental: {
    scrollRestoration: true,
    // strictNextHead: true,
  },

  // Allowed image hosts
  images: {
    domains: [
      process.env.WORDPRESS_API_URL.match(/(?!(w+)\.)\w*(?:\w+\.)+\w+/)[0], // Valid WP Image domain.
      process.env.WORDPRESS_DOMAIN,
      'hwc-cms.wearetesting.co.uk',
      'bb.hwc.wearetesting.co.uk',
      'cms.heartwoodcollection.com',
      'heartwoodinns.com',
      'brasserieblanc.com',
      '0.gravatar.com',
      '1.gravatar.com',
      '2.gravatar.com',
      'secure.gravatar.com',
    ],
    formats: ['image/webp'],
    deviceSizes: [500, 738, 932, 1200, 1400, 1920], // [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    // imageSizes: [412, 738, 932, 1140, 1320, 1920], // [16, 32, 48, 64, 96, 128, 256, 384],
  },
  sassOptions: {
    includePaths: [path.join(__dirname, 'styles')],
  },
  // Redirects allow you to redirect an incoming request path to a different destination path.
  async redirects() {
    return [
      {
        source: '/sitemap.xml',
        destination: '/sitemap_index.xml',
       permanent: true,
      },
  // ===== 1. Exceptions from reges
  {
    source: "/blog/1-souffles-mais-oui/",
    destination: "/recipes/cheese-souffle/",
   permanent: true
  },
  {
    source: "/blog/bb-cheese-souffle/",
    destination: "/recipes/cheese-souffle/",
   permanent: true
  },
  {
    source: "/blog/christmas-party-menu-early-bird-offer/",
    destination: "/christmas/",
   permanent: true
  },
  {
    source: "/blog/day11/",
    destination: "/recipes/blackcurrant-marshmallow-recipe/",
   permanent: true
  },
  {
    source: "/blog/day16/",
    destination: "/recipes/roast-pheasant-in-a-bag/",
   permanent: true
  },
  {
    source: "/blog/day2/",
    destination: "/recipes/mulled-wine/",
   permanent: true
  },
  {
    source: "/blog/day20/",
    destination: "/recipes/christmas-mince-jalousie/",
   permanent: true
  },
  {
    source: "/blog/day22/",
    destination: "/recipes/cranachan/",
   permanent: true
  },
  {
    source: "/blog/fathers-day-early-bird-booking-offer/",
    destination: "/fathers-day/",
   permanent: true
  },
  {
    source: "/blog/feel-good-friday-try-a-mojito/",
    destination: "/recipes/mojito/",
   permanent: true
  },
  {
    source: "/blog/getting-back-to-what-we-do-best/",
    destination: "/recipes/raymond-blanc-lemon-cake/",
   permanent: true
  },
  {
    source: "/blog/happy hour",
    destination: "/happy-hour/",
   permanent: true
  },
  {
    source: "/blog/hooray-for-cheese-souffle/",
    destination: "/recipes/cheese-souffle/",
   permanent: true
  },
  {
    source: "/blog/madeleines-de-commercy-2/",
    destination: "/recipes/madeleines-de-commercy/",
   permanent: true
  },
  {
    source: "/blog/our-happy-hour/",
    destination: "/happy-hour/",
   permanent: true
  },
  {
    source: "/blog/our-sustainable-history/",
    destination: "/sustainability/",
   permanent: true
  },
  {
    source: "/blog/raymond-clive-talk-tagine/",
    destination: "/recipes/lamb-tagine/",
   permanent: true
  },
  {
    source: "/blog/rhubarb-rhubarb-rhubarb/",
    destination: "/recipes/",
   permanent: true
  },
  {
    source: "/blog/strawberry-fool-2/",
    destination: "/recipes/strawberry-fool/",
   permanent: true
  },
  {
    source: "/blog/the-bb-christmas-countdown-is-on/",
    destination: "/christmas/",
   permanent: true
  },
  {
    source: "/blog/the-best-ever-brownies-from-chef-clive/",
    destination: "/recipes/the-best-ever-brownies/",
   permanent: true
  },
  {
    source: "/blog/win-50-bb-gift-card-today-festival-flavour/",
    destination: "/gifts/",
   permanent: true
  },
  {
    source: "/blog/win-50-bb-gift-card-today-festival-flavour-2/",
    destination: "/gifts/",
   permanent: true
  },
  {
    source: "/blog/win-50-bb-gift-card-today-festival-flavour-3/",
    destination: "/gifts/",
   permanent: true
  },
  {
    source: "/how-to/36927/",
    destination: "/recipes/golden-chocolate-feuillantine/",
   permanent: true
  },
  {
    source: "/how-to/bouef-bourguignon/",
    destination: "/recipes/raymond-blancs-best-ever-boeuf-bourguignon-recipe/",
   permanent: true
  },
  {
    source: "/news-cat/refreshed/",
    destination: "/brasseries/charlotte-street/",
   permanent: true
  },
  {
    source: "/news-cat/ring-in-the-new-year-with-us/",
    destination: "/new-years-eve/",
   permanent: true
  },
  {
    source: "/news-cat/riverside-dining-at-bb-fulham-reach/",
    destination: "/brasseries/fulham-reach/",
   permanent: true
  },
  {
    source: "/news-cat/southbank-brasserie-blanc-re-opens-monday-book-free-lunch-now/",
    destination: "/brasseries/southbank/",
   permanent: true
  },
  {
    source: "/news-cat/southbank-terrace/",
    destination: "/brasseries/southbank/",
   permanent: true
  },
  {
    source: "/news-cat/st-pauls-now-closed-as-lease-ends/",
    destination: "/brasseries/st-pauls/",
   permanent: true
  },
  {
    source: "/news-cat/sustainability-matters/",
    destination: "/sustainability/",
   permanent: true
  },
  {
    source: "/news-cat/un-petit-cadeau/",
    destination: "/gifts/",
   permanent: true
  },
  {
    source: "/offers/enjoy-25-off-your-dining-experience-bon-appetit/",
    destination: "/le-club/",
   permanent: true
  },
  {
    source: "/offers/its-national-butchers-week/",
    destination: "/steak-night/",
   permanent: true
  },
  {
    source: "/offers/kids-eat-free/",
    destination: "/le-club/",
   permanent: true
  },
  {
    source: "/special-events/weddings-at-brasserie-blanc/",
    destination: "/private-dining/",
   permanent: true
  },
  // ===== 2. missing exception from regex
  {
    source: "/blog/bbs-best-game-seasoning-recipe/",
    destination: "/recipes/bbs-best-game-seasoning-recipe/",
   permanent: true
  },
  {
    source: "/blog/best-ever-crumble/",
    destination: "/recipes/best-ever-crumble/",
   permanent: true
  },
  {
    source: "/blog/best-pancakes-ever/",
    destination: "/recipes/best-pancakes-ever/",
   permanent: true
  },
  {
    source: "/blog/brasserie-blancs-best-ever-bloody-mary/",
    destination: "/recipes/brasserie-blancs-best-ever-bloody-mary/",
   permanent: true
  },
  {
    source: "/blog/brasserie-blancs-bramble-cocktail/",
    destination: "/recipes/brasserie-blancs-bramble-cocktail/",
   permanent: true
  },
  {
    source: "/blog/brasserie-blancs-home-smoke-salmon-recipe/",
    destination: "/recipes/brasserie-blancs-home-smoke-salmon-recipe/",
   permanent: true
  },
  {
    source: "/blog/brasserie-blancs-how-to-home-smoke-beetroot-recipe/",
    destination: "/recipes/brasserie-blancs-how-to-home-smoke-beetroot-recipe/",
   permanent: true
  },
  {
    source: "/blog/brasserie-blancs-le-burger-recipe/",
    destination: "/recipes/brasserie-blancs-le-burger-recipe/",
   permanent: true
  },
  {
    source: "/blog/brasserie-blancs-wild-boar-sirloin-juniper-black-pepper/",
    destination: "/recipes/brasserie-blancs-wild-boar-sirloin-juniper-black-pepper/",
   permanent: true
  },
  {
    source: "/blog/brioche-butter-pudding/",
    destination: "/recipes/brioche-butter-pudding/",
   permanent: true
  },
  {
    source: "/blog/cherry-vinaigrette-recipe/",
    destination: "/recipes/cherry-vinaigrette-recipe/",
   permanent: true
  },
  {
    source: "/blog/chickpea-coriander-cakes/",
    destination: "/recipes/chickpea-coriander-cakes/",
   permanent: true
  },
  {
    source: "/blog/chocolate-mousse-crumble/",
    destination: "/recipes/chocolate-mousse-crumble/",
   permanent: true
  },
  {
    source: "/blog/confit-de-maquereau/",
    destination: "/recipes/confit-de-maquereau/",
   permanent: true
  },
  {
    source: "/blog/cooking-tip-bread/",
    destination: "/recipes/cooking-tip-bread/",
   permanent: true
  },
  {
    source: "/blog/cooking-tip-lemonlime-slices/",
    destination: "/recipes/cooking-tip-lemonlime-slices/",
   permanent: true
  },
  {
    source: "/blog/cooking-tip-non-stick-fish/",
    destination: "/recipes/cooking-tip-non-stick-fish/",
   permanent: true
  },
  {
    source: "/blog/cooking-tip-salting-meat/",
    destination: "/recipes/cooking-tip-salting-meat/",
   permanent: true
  },
  {
    source: "/blog/creamed-cauliflower-grana-padano-soup/",
    destination: "/recipes/creamed-cauliflower-grana-padano-soup/",
   permanent: true
  },
  {
    source: "/blog/crunchy-coleslaw/",
    destination: "/recipes/crunchy-coleslaw/",
   permanent: true
  },
  {
    source: "/blog/deep-fried-goats-cheese-parcel-chilli-tomato-chutney/",
    destination: "/recipes/deep-fried-goats-cheese-parcel-chilli-tomato-chutney/",
   permanent: true
  },
  {
    source: "/blog/easter-chocolate/",
    destination: "/recipes/easter-chocolate/",
   permanent: true
  },
  {
    source: "/blog/escargot/",
    destination: "/recipes/escargot/",
   permanent: true
  },
  {
    source: "/blog/espresso-martini-cocktail/",
    destination: "/recipes/espresso-martini-cocktail/",
   permanent: true
  },
  {
    source: "/blog/flavour-filled-autumn-pumpkin-recipes/",
    destination: "/recipes/flavour-filled-autumn-pumpkin-recipes/",
   permanent: true
  },
  {
    source: "/blog/fresh-sea-trout-new-potatoes-and-watercress-sauce/",
    destination: "/recipes/fresh-sea-trout-new-potatoes-and-watercress-sauce/",
   permanent: true
  },
  {
    source: "/blog/fruit-fool/",
    destination: "/recipes/fruit-fool/",
   permanent: true
  },
  {
    source: "/blog/gaspacho/",
    destination: "/recipes/gaspacho/",
   permanent: true
  },
  {
    source: "/blog/grilled-atlantic-sardines-tomato-sauce/",
    destination: "/recipes/grilled-atlantic-sardines-tomato-sauce/",
   permanent: true
  },
  {
    source: "/blog/harissa-glazed-aubergine-with-baba-ganoush/",
    destination: "/recipes/harissa-glazed-aubergine-with-baba-ganoush/",
   permanent: true
  },
  {
    source: "/blog/home-cured-salmon/",
    destination: "/recipes/home-cured-salmon/",
   permanent: true
  },
  {
    source: "/blog/homemade-marmalade-recipe/",
    destination: "/recipes/homemade-marmalade-recipe/",
   permanent: true
  },
  {
    source: "/blog/humble-crumble-winter-best/",
    destination: "/recipes/humble-crumble-winter-best/",
   permanent: true
  },
  {
    source: "/blog/indulgent-chocolate-almond-torte/",
    destination: "/recipes/indulgent-chocolate-almond-torte/",
   permanent: true
  },
  {
    source: "/blog/international-womens-day-recipe/",
    destination: "/recipes/international-womens-day-recipe/",
   permanent: true
  },
  {
    source: "/blog/kitchen-tip-add-flavour-remove-flour/",
    destination: "/recipes/kitchen-tip-add-flavour-remove-flour/",
   permanent: true
  },
  {
    source: "/blog/kitchen-tip-clean-oven/",
    destination: "/recipes/kitchen-tip-clean-oven/",
   permanent: true
  },
  {
    source: "/blog/kitchen-tip-crispy-crackling/",
    destination: "/recipes/kitchen-tip-crispy-crackling/",
   permanent: true
  },
  {
    source: "/blog/kitchen-tip-get-juice-fruit/",
    destination: "/recipes/kitchen-tip-get-juice-fruit/",
   permanent: true
  },
  {
    source: "/blog/kitchen-tip-get-vanilla-pods/",
    destination: "/recipes/kitchen-tip-get-vanilla-pods/",
   permanent: true
  },
  {
    source: "/blog/kitchen-tip-less-sticky-utensils-2/",
    destination: "/recipes/kitchen-tip-less-sticky-utensils-2/",
   permanent: true
  },
  {
    source: "/blog/kitchen-tip-less-sticky-utensils/",
    destination: "/recipes/kitchen-tip-less-sticky-utensils/",
   permanent: true
  },
  {
    source: "/blog/kitchen-tip-soften-hardened-brown-sugar/",
    destination: "/recipes/kitchen-tip-soften-hardened-brown-sugar/",
   permanent: true
  },
  {
    source: "/blog/kitchen-tip-stop-biscuits-going-stale/",
    destination: "/recipes/kitchen-tip-stop-biscuits-going-stale/",
   permanent: true
  },
  {
    source: "/blog/lamb-rump-with-pea-puree-pot-roasted-carrots-and-dauphinoise-potato/",
    destination: "/recipes/lamb-rump-with-pea-puree-pot-roasted-carrots-and-dauphinoise-potato/",
   permanent: true
  },
  {
    source: "/blog/melt-in-the-mouth-chocolate-fondant/",
    destination: "/recipes/melt-in-the-mouth-chocolate-fondant/",
   permanent: true
  },
  {
    source: "/blog/our-rich-and-delicious-mince-pie-recipe/",
    destination: "/recipes/our-rich-and-delicious-mince-pie-recipe/",
   permanent: true
  },
  {
    source: "/blog/passionfruit-batida/",
    destination: "/recipes/passionfruit-batida/",
   permanent: true
  },
  {
    source: "/blog/pea-broad-bean-red-pepper-buffalo-mozzarella-salad-recipe/",
    destination: "/recipes/pea-broad-bean-red-pepper-buffalo-mozzarella-salad-recipe/",
   permanent: true
  },
  {
    source: "/blog/pear-conde/",
    destination: "/recipes/pear-conde/",
   permanent: true
  },
  {
    source: "/blog/plum-compote-almond-crumble/",
    destination: "/recipes/plum-compote-almond-crumble/",
   permanent: true
  },
  {
    source: "/blog/plum-perfect/",
    destination: "/recipes/plum-perfect/",
   permanent: true
  },
  {
    source: "/blog/presentation-tip-clear-ice-cubes/",
    destination: "/recipes/presentation-tip-clear-ice-cubes/",
   permanent: true
  },
  {
    source: "/blog/presentation-tip-even-flour/",
    destination: "/recipes/presentation-tip-even-flour/",
   permanent: true
  },
  {
    source: "/blog/presentation-tip-making-candles-last-longer/",
    destination: "/recipes/presentation-tip-making-candles-last-longer/",
   permanent: true
  },
  {
    source: "/blog/raymond-blancs-best-ever-boeuf-bourguignon-recipe/",
    destination: "/recipes/raymond-blancs-best-ever-boeuf-bourguignon-recipe/",
   permanent: true
  },
  {
    source: "/blog/raymond-blancs-delicious-white-onion-soup/",
    destination: "/recipes/raymond-blancs-delicious-white-onion-soup/",
   permanent: true
  },
  {
    source: "/blog/roasted-mixed-pumpkins-soured-cream-toasted-almonds/",
    destination: "/recipes/roasted-mixed-pumpkins-soured-cream-toasted-almonds/",
   permanent: true
  },
  {
    source: "/blog/sea-trout-new-potatoes-watercress-sauce/",
    destination: "/recipes/sea-trout-new-potatoes-watercress-sauce/",
   permanent: true
  },
  {
    source: "/blog/seasonal-mushrooms-recipes-tips/",
    destination: "/recipes/seasonal-mushrooms-recipes-tips/",
   permanent: true
  },
  {
    source: "/blog/seasonal-sweetcorn-chowder/",
    destination: "/recipes/seasonal-sweetcorn-chowder/",
   permanent: true
  },
  {
    source: "/blog/stewed-apricots-with-crunchy-almonds/",
    destination: "/recipes/stewed-apricots-with-crunchy-almonds/",
   permanent: true
  },
  {
    source: "/blog/stickytoffeepudding/",
    destination: "/recipes/stickytoffeepudding/",
   permanent: true
  },
  {
    source: "/blog/swiss-chard-wild-mushroom-fricasse/",
    destination: "/recipes/swiss-chard-wild-mushroom-fricasse/",
   permanent: true
  },
  {
    source: "/blog/technical-tip-checking-fresh-baking-powder/",
    destination: "/recipes/technical-tip-checking-fresh-baking-powder/",
   permanent: true
  },
  {
    source: "/blog/technical-tip-maximise-volume-souffles-meringues/",
    destination: "/recipes/technical-tip-maximise-volume-souffles-meringues/",
   permanent: true
  },
  {
    source: "/blog/technical-tip-pastry-speed-resting-time/",
    destination: "/recipes/technical-tip-pastry-speed-resting-time/",
   permanent: true
  },
  {
    source: "/blog/technical-tip-removing-unwanted-duck-fat-whilst-cooking/",
    destination: "/recipes/technical-tip-removing-unwanted-duck-fat-whilst-cooking/",
   permanent: true
  },
  {
    source: "/blog/technical-tip-using-wine-marinating/",
    destination: "/recipes/technical-tip-using-wine-marinating/",
   permanent: true
  },
  {
    source: "/blog/the-best-every-cucumber-gazpaucho-soup/",
    destination: "/recipes/the-best-every-cucumber-gazpaucho-soup/",
   permanent: true
  },
  {
    source: "/blog/try-our-rhubarb-custard-recipe/",
    destination: "/recipes/try-our-rhubarb-custard-recipe/",
   permanent: true
  },
  {
    source: "/blog/vegan-harissa-miso-glazed-aubergine-recipe/",
    destination: "/recipes/vegan-harissa-miso-glazed-aubergine-recipe/",
   permanent: true
  },
  {
    source: "/blog/vegan-tofu-with-apple-puree-watercress-and-fennel-salad-recipe/",
    destination: "/recipes/vegan-tofu-with-apple-puree-watercress-and-fennel-salad-recipe/",
   permanent: true
  },
  {
    source: "/blog/vegan-winter-vegetable-fritter-recipe/",
    destination: "/recipes/vegan-winter-vegetable-fritter-recipe/",
   permanent: true
  },
  {
    source: "/complimentary-glass-of-wine/",
    destination: "/news/complimentary-glass-of-wine/",
   permanent: true
  },
  {
    source: "/inspiration/healthy-and-delicious-work-from-home-snack-inspiration/",
    destination: "/recipes/healthy-and-delicious-work-from-home-snack-inspiration/",
   permanent: true
  },
  {
    source: "/inspiration/inspiration-at-le-manoir/",
    destination: "/news/inspiration-at-le-manoir/",
   permanent: true
  },
  {
    source: "/inspiration/the-background-to-st-marcellin-cheese/",
    destination: "/news/the-background-to-st-marcellin-cheese/",
   permanent: true
  },
  {
    source: "/news-cat/raymond-blancs-galette-des-rois-recipe/",
    destination: "/recipes/raymond-blancs-galette-des-rois-recipe/",
   permanent: true
  },
  {
    source: "/news-cat/raymondsvegantreats/",
    destination: "/recipes/raymondsvegantreats/",
   permanent: true
  },
  {
    source: "/our-menus/polo-in-the-park-menu/",
    destination: "/news/polo-in-the-park-menu/",
   permanent: true
  },
  {
    source: "/our-menus/southbank-pre-theatre/",
    destination: "/brasseries/southbank/menu/pre-theatre/",
   permanent: true
  },
  {
    source: "/press/brasserie-blanc-comes-beaconsfield/",
    destination: "/news/brasserie-blanc-comes-beaconsfield/",
   permanent: true
  },
  {
    source: "/press/brasserie-blanc-opens-farnham/",
    destination: "/news/brasserie-blanc-opens-farnham/",
   permanent: true
  },
  {
    source: "/recipes-tips/bb-cheese-souffle/",
    destination: "/recipes/bb-cheese-souffle/",
   permanent: true
  },
  {
    source: "/recipes-tips/bbs-best-game-seasoning-recipe/",
    destination: "/recipes/bbs-best-game-seasoning-recipe/",
   permanent: true
  },
  {
    source: "/recipes-tips/best-ever-crumble/",
    destination: "/recipes/best-ever-crumble/",
   permanent: true
  },
  {
    source: "/recipes-tips/brasseri-blancs-christmas-jalousie/",
    destination: "/recipes/brasseri-blancs-christmas-jalousie/",
   permanent: true
  },
  {
    source: "/recipes-tips/brasserie-blanc-mulled-wine-video/",
    destination: "/recipes/brasserie-blanc-mulled-wine-video/",
   permanent: true
  },
  {
    source: "/recipes-tips/brasserie-blancs-bramble-cocktail/",
    destination: "/recipes/brasserie-blancs-bramble-cocktail/",
   permanent: true
  },
  {
    source: "/recipes-tips/brasserie-blancs-home-smoke-salmon-recipe/",
    destination: "/recipes/brasserie-blancs-home-smoke-salmon-recipe/",
   permanent: true
  },
  {
    source: "/recipes-tips/brasserie-blancs-how-to-home-smoke-beetroot-recipe/",
    destination: "/recipes/brasserie-blancs-how-to-home-smoke-beetroot-recipe/",
   permanent: true
  },
  {
    source: "/recipes-tips/creamed-cauliflower-grana-padano-soup/",
    destination: "/recipes/creamed-cauliflower-grana-padano-soup/",
   permanent: true
  },
  {
    source: "/recipes-tips/espresso-martini-cocktail/",
    destination: "/recipes/espresso-martini-cocktail/",
   permanent: true
  },
  {
    source: "/recipes-tips/home-cured-salmon/",
    destination: "/recipes/home-cured-salmon/",
   permanent: true
  },
  {
    source: "/recipes-tips/humble-crumble-winter-best/",
    destination: "/recipes/humble-crumble-winter-best/",
   permanent: true
  },
  {
    source: "/recipes-tips/madeleines-de-commercy-2/",
    destination: "/recipes/madeleines-de-commercy-2/",
   permanent: true
  },
  {
    source: "/recipes-tips/raymond-blancs-best-ever-boeuf-bourguignon-recipe/",
    destination: "/recipes/raymond-blancs-best-ever-boeuf-bourguignon-recipe/",
   permanent: true
  },
  {
    source: "/recipes-tips/roast-pheasant-in-a-bag/",
    destination: "/recipes/roast-pheasant-in-a-bag/",
   permanent: true
  },
  {
    source: "/recipes-tips/wild-wood-pigeon-salad-winter-coleslaw/",
    destination: "/recipes/wild-wood-pigeon-salad-winter-coleslaw/",
   permanent: true
  },
      // ===== 3. Global regex
      {
        source: '/search/:slug/',
        destination: '/',
       permanent: true,
      },
      {
        source: '/blog/:slug/',
        destination: '/news/:slug/',
       permanent: true,
      },
      {
        source: '/offers/:slug/',
        destination: '/news/:slug/',
       permanent: true,
      },
      {
        source: '/blog/page/:id/',
        destination: '/news/',
       permanent: true,
      },
      {
        source: '/news/page/:id/',
        destination: '/news/',
       permanent: true,
      },
      {
        source: '/brasserie-blanc/:slug/',
        destination: '/news/:slug/',
       permanent: true,
      },
      {
        source: '/career/:slug/',
        destination: '/news/:slug/',
       permanent: true,
      },
      {
        source: '/food-thought/:slug/',
        destination: '/news/:slug/',
       permanent: true,
      },
      {
        source: '/how-to/:slug/',
        destination: '/recipes/',
       permanent: true,
      },
      {
        source: '/news-cat/:slug/',
        destination: '/news/:slug/',
       permanent: true,
      },
      {
        source: '/special-events/:slug/',
        destination: '/news/:slug/',
       permanent: true,
      },
      // 4. Special pages
      {
        source: '/our-menus/',
        destination: '/menu/',
       permanent: true,
      },
      {
        source: '/our-menus/:slug/',
        destination: '/menu/',
       permanent: true,
      },
      {
        source: '/standard-booking/',
        destination: '/book/',
       permanent: true,
      },
      {
        source: '/book-table-2/',
        destination: '/book/',
       permanent: true,
      },
      {
        source: '/book-table/',
        destination: '/book/',
       permanent: true,
      },
      {
        source: '/book-table/0/',
        destination: '/book/',
       permanent: true,
      },
      {
        source: '/book-table/Tel no',
        destination: '/book/',
       permanent: true,
      },
    ];
  },
  // Rewrites allow you to map an incoming request path to a different destination path.
  // Rewrites act as a URL proxy and mask the destination path, making it appear the user hasn't changed their location on the site.
  // In contrast, redirects will reroute to a new page and show the URL changes.
  async rewrites() {
    return [
        {
            source: '/(.*)sitemap.xml',
            destination: '/api/upstream-proxy'
        },
        {
            source: '/sitemap(.*).xml',
            destination: '/api/upstream-proxy'
        },
        {
          source: '/feed',
          destination: '/api/upstream-proxy',
        },
        // generate an ENV specific robots.txt
        {
          source: '/robots.txt',
          destination: '/api/robots',
        }
      ]
  },

  // Performance headers
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-DNS-Prefetch-Control',
            value: 'on'
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin'
          }
        ]
      },
      {
        source: '/fonts/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable'
          }
        ]
      },
      {
        source: '/images/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable'
          }
        ]
      }
    ]
  }
}

const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
  openAnalyzer: true,
})

module.exports = withBundleAnalyzer(nextConfig)
// module.exports = nextConfig
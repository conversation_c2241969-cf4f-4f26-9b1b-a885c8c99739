import { addYears } from 'date-fns'
import { NextRequest, NextResponse } from 'next/server'
import fetch from 'isomorphic-unfetch'

const secret = process.env.BASIC_AUTH_PASSWORD || "secret"
const api_url = process.env.WORDPRESS_API_URL || false

export default async function middleware(req: NextRequest) {
  const basicAuth = req.headers.get('authorization')
  const url = req.nextUrl

  // === allow special routes
  if (req.nextUrl.pathname.startsWith('/api') ||
      req.nextUrl.pathname.startsWith('/_next')
      || req.nextUrl.pathname.startsWith('/images/')
      || req.nextUrl.pathname.includes("/fonts/")
      || req.nextUrl.pathname.includes("/favicon/")
  ) {
    return NextResponse.next()
  }

  // === Maintenance mode
  if( process.env.VERCEL_MAINTENANCE == "yes" && !req.nextUrl.host.includes("vercel.app") ) {
    req.nextUrl.pathname = `/maintenance`

    return NextResponse.rewrite(req.nextUrl, {status: 503})
  }

  // === START: Redirections
  if( api_url ) {
    let parts = api_url.split("/")

    // --- Inns specific
    let filename = parts[parts.length-2] || false
    if( filename ) {
      const res = await fetch(process.env.SITE_URL+'/api/staticData',{
        method: 'POST',
        body: JSON.stringify({filename}),
      })
      if (res.ok) {
        console.log(filename)
        let objectData = await res.json()
        let redirects = JSON.parse(objectData).redirects
        // console.log(redirects)
        if( redirects ) {
          // console.log(req.nextUrl.pathname)
          let match = redirects.find(o => o.source === req.nextUrl.pathname)
          if( match ) {
            // console.log(match)
            url.pathname = match.destination
            return NextResponse.redirect(url, 301)
          }
        }
      }
    }

    // --- General: redirect url's like: https://heartwoodinns.com/pubs/signup/ => https://heartwoodinns.com/signup/
    const pubFrontPath = parts[parts.length-2] || false
    // console.log(pubFrontPath)
    if( pubFrontPath && url.pathname.startsWith(`/${pubFrontPath}/`) ) {
      url.pathname = url.pathname.replace(`/${pubFrontPath}/`, '/')
      return NextResponse.redirect(url, 301)
    }
  }

  // --- Redirect case-sensitive
  if (url.pathname !== url.pathname.toLowerCase()) {
    url.pathname = url.pathname.toLowerCase()
    return NextResponse.redirect(url, 301);
  }
  // === END: Redirections

  // === allow special routes
  if (req.nextUrl.pathname.includes(".") ) {
    return NextResponse.next()
  }


  // === Basic Authorisation (Bypass)
  console.log(process.env.BASIC_AUTH_PASSWORD)
  if ( !req.nextUrl.host.includes("vercel.app") && (
    process.env.VERCEL_ENV == 'local' || process.env.VERCEL_ENV == 'production' ||
    !process.env.BASIC_AUTH_PASSWORD || req.cookies['let-me-in'] === process.env.BASIC_AUTH_PASSWORD
    ) ) {
    return NextResponse.next()
  }

  // === Basic Authorisation
  if (basicAuth) {
    const authValue = basicAuth.split(' ')[1]
    const [user, pwd] = atob(authValue).split(':')

    if (user === 'letmein' && pwd === secret) {
      const response = NextResponse.next()
      const expire = addYears(new Date(), 1).toUTCString()
      response.headers.set(
        'Set-Cookie',
        `let-me-in=${process.env.BASIC_AUTH_PASSWORD}; Domain=.example.com; Secure; HttpOnly; Expires='${expire}'`,
      )
      return response
    }
  }

  url.pathname = '/api/auth'
  return NextResponse.rewrite(url)
}
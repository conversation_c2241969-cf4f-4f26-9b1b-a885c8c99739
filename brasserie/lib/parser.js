import parse, { domToReact } from "html-react-parser";
import Link from "next/link";
import Image from "next/image";
import { Accordion, Dropdown } from "react-bootstrap";
import Gallery from "../components/gallery";
import AnimatedSection from "../components/animated-section";
import CustomSelect from "../components/custom-select";
import AnimatedAnimal from "../components/animated-animal";
import dynamic from "next/dynamic";
import CookiebotDeclaration from "../components/cookiebot-declaration";
import PostRelated from "../components/post-related";
import NewsletterSignup from "../components/blocks/newsletter-signup";
import BookingWidget from "../components/booking/booking-widget";
import LocationsListing from "../components/locations/locations-listing";
import MorePosts from "../components/more-posts";
import { Icon } from "../components/icon";
import OpeningTimes from "../components/opening-times";
import FeaturesCarousel from "../components/blocks/features-carousel";
import SwingSign from "../components/swingsign";
import Wordmark from "../components/wordmark";
import Carousel from "../components/blocks/carousel";
import CarouselSlide from "../components/blocks/carousel-slide";
import CalendarDropdown from "../components/calendar-dropdown";
import MenusDropdown from "@components/menus/dropdown";
import LocationsDropdown from "@components/locations/dropdown";
import GalleryScroll2 from "@components/blocks/gallery-scroll-2";
import MenusButtons from "@components/menus/buttons";
import LocationsMenusNav from "@components/menus/locations-nav";
import LocationsDetailsNav from "@components/locations/details-nav";
import {decodeJsonStrings } from "./utils";
// import LocationsListing from "../components/blocks/locations-listing";

// const Map = dynamic(() => import("../components/blocks/map"), { ssr:false })
// const LocationsListing = dynamic(() => import("../components/blocks/locations-listing"), { ssr:false })

export default function parseHtml(html) {
  const options = {
    replace: ({ name, attribs, children }) => {

      // Convert internal links to Next.js Link components.
      const isInternalLink = name === "a" && attribs["data-app-link"] === "true";

      // ===== wrap into AnimatedSection for subtle inView transitions
      let isAnimated = (
        (name && (attribs['data-animated'] === "true" || attribs.class && attribs.class.indexOf('animated') !== -1)) ||
        (name === 'div' && attribs.class && (attribs.class.indexOf('animated') !== -1 ))
        // || (name === 'figure' && attribs.class.indexOf('wp-block-image') !== -1 )
      ) ? true : false
      // disable animation if element has 'no-anim" class'
      if( (name === 'div' || name === 'figure') && attribs.class && attribs.class.indexOf('no-anim') !== -1 ) isAnimated = false
      const animationType = attribs && attribs['data-animation'] ? attribs['data-animation'] : null
      if( isAnimated ) {
      return (
        <AnimatedSection tag={name} className={attribs.class} animation={animationType && animationType.split(",")}>
          {domToReact(children, options)}
        </AnimatedSection>
      )
      }

      if (isInternalLink) {
        return (
          <Link href={attribs.href} className={`${attribs.class} internal-link`} target={attribs.target} rel={attribs.rel}>
            {domToReact(children, options)}
          </Link>
        )
      }

      // Convert images to serve via Next
      const isImage = name === 'img';
      if( isImage ) {
        return (
            <Image
            width={attribs.width || 400}
            height={attribs.height || 300}
            alt={attribs.alt}
            src={attribs.src}
            className={attribs.class}
            STYLE={attribs.style}
            />
        )
      }

      // ===== START -> React Bootstrap: Accordion =====
      // Accordion Parent
      const isAccordion = name === 'div' && attribs['data-bs-accordion'] === "true"
      if( isAccordion ) {
        return (
          <Accordion defaultActiveKey="0">
            {domToReact(children, options)}
          </Accordion>
        )
      }
      // Accordion.Item
      const isAccordionItem = name === 'div' && attribs['data-bs-accordion-item'] === "true"
      if( isAccordionItem ) {
        return (
          <AnimatedSection>
            <Accordion.Item eventKey={attribs['data-key']}>
              {domToReact(children, options)}
            </Accordion.Item>
          </AnimatedSection>
        )
      }
      // Accordion.Header
      const isAccordionHeader = name === 'div' && attribs['data-bs-accordion-header'] === "true"
      if( isAccordionHeader ) {
        return (
          <Accordion.Header as="h3" className="m-0 fs-3 text-start position-relative">
            <span className="label-wrap"><span>{domToReact(children, options)}</span></span>
            <Icon name={`dropdown-arrow`} className='' />
          </Accordion.Header>
        )
      }
      // Accordion.Body
      const isAccordionBody = name === 'div' && attribs['data-bs-accordion-body'] === "true"
      if( isAccordionBody ) {
        return (
          <Accordion.Body className="pt-0">
            {domToReact(children, options)}
          </Accordion.Body>
        )
      }
      // ===== END -> React Bootstrap: Accordion =====

      // ===== START: React Bootstrap: Dropdown
      // Dropdown Parent
      const isDropdown = name === 'div' && attribs['data-bs-dropdown'] === "true"
      if( isDropdown ) {
        return (
          <Dropdown className={attribs.class || ''}>
            {domToReact(children, options)}
          </Dropdown>
        )
      }
      // Dropdown Toggle
      const isDropdownToggle = name === 'button' && attribs['data-bs-dropdown-toggle'] === "true"
      if( isDropdownToggle ) {
        const toggleVariant = attribs['data-bs-dropdown-toggle-variant'] || 'primary'
        return (
          <Dropdown.Toggle variant={toggleVariant} className={attribs.class || ''}>
            {domToReact(children, options)}
          </Dropdown.Toggle>
        )
      }
      // Dropdown Menu
      const isDropdownMenu = name === 'ul' && attribs['data-bs-dropdown-menu'] === "true"
      if( isDropdownMenu ) {
        return (
          <Dropdown.Menu renderOnMount={true} as="ul" className={`${attribs.class || ''}`}>
            {domToReact(children, options)}
          </Dropdown.Menu>
        )
      }
      // ===== END: React Bootstrap: Dropdown

      // ===== START: Gallery =====
      // Gallery Parent
      const isGallery = name === 'figure' && attribs.class && attribs.class.indexOf('wp-block-gallery') !== -1
      if( isGallery ) {
        return (
          <Gallery className="make-img-square">
            {domToReact(children, options)}
          </Gallery>
        )
      }
      // ===== END: Gallery =====

      // ===== START: Gallery Scroll =====
      const isGalleryScroll = name === 'div' && attribs['data-gallery-scroll'] === "true"
      if( isGalleryScroll ) {
        return (
          <GalleryScroll2>
            {domToReact(children, options)}
          </GalleryScroll2>
        )
      }

      // ===== Replace default Select
      const isSelect = name === 'div' && attribs.class && attribs.class.indexOf('js-select-replace') !== -1
      if( isSelect ) {
        return (
          <CustomSelect >
            {domToReact(children, options)}
          </CustomSelect>
        )
      }

      // === Animated Animal thing
      const isAnimatedAnimal = name === 'div' && attribs['data-animated-animal'] === "true"
      if( isAnimatedAnimal ) {
        return (
          <AnimatedAnimal type={attribs['data-type']} text={attribs['data-text']} class={attribs.class}>
            {domToReact(children, options)}
          </AnimatedAnimal>
        )
      }

      // convert to Locations Map
      // const isLocationsMap = name === "div" && attribs['data-locations-map'] == 'true'
      // if( isLocationsMap ) {
      //   const markersJson = JSON.parse(attribs['data-markers'])
      //   const mapOptions = JSON.parse(attribs['data-options'])
      //   // console.log(markersJson)
      //   return (
      //     <Map markers={markersJson} options={mapOptions} />
      //   )
      // }

      // convert to Location Menus
      const isLocationMenus = name === "div" && attribs['data-location-menus'] == 'true'
      if( isLocationMenus ) {
        const type = attribs['data-type'] || 'dropdown'
        return (
          type === 'buttons' ?
          <MenusButtons /> :
          <MenusDropdown />
        )
      }

      // convert to Locations Listing
      const isLocationListing = name === "div" && attribs['data-locations-listing'] == 'true'
      if( isLocationListing ) {
        const locationsMarkersJson = decodeJsonStrings(JSON.parse(attribs['data-markers']))
        const locationMapOptions = JSON.parse(attribs['data-options'])
        return (
          <LocationsListing className={attribs.class} markers={locationsMarkersJson} options={locationMapOptions} >
             {domToReact(children, options)}
          </LocationsListing>
        )
      }

      // convert to Latest Blog (post-related.tsx)
      const isLatestBlog = name === "div" && attribs['data-latest-blog'] == 'true'
      if( isLatestBlog ) {
        const postsJson = JSON.parse(attribs['data-posts'])
        const title = attribs['data-title']
        const layout = attribs['data-layout'] || 'stacked'
        // console.log(postsJson)
        return (
          <PostRelated posts={postsJson} title={title} layout={layout}>
             {domToReact(children, options)}
          </PostRelated>
        )
      }

      // convert to Latest from category (more-offers.tsx)
      const isLatestCategory = name === "div" && attribs['data-latest-category'] == 'true'
      if( isLatestCategory ) {
        const postsJson = JSON.parse(attribs['data-posts'])
        const latestCategory = attribs['data-category']
        const title = attribs['data-title']
        const layout = attribs['data-layout'] || 'stacked'
        const options = JSON.parse(attribs['data-options'])
        // console.log(postsJson)
        return (
          <MorePosts posts={postsJson} category={latestCategory} title={title} layout={layout} options={options}>
             {domToReact(children, options)}
          </MorePosts>
        )
      }

      // convert to Newsletter signup
      const isNewsletterSignup = name === "div" && attribs['data-shortcode-newsletter'] == 'true'
      if( isNewsletterSignup ) {
        const newsletterIgnore = attribs['data-ignore'] || null
        return (
          <NewsletterSignup className={attribs.class} ignore={newsletterIgnore}>
             {domToReact(children, options)}
          </NewsletterSignup>
        )
      }

      const cookiebotDeclaration = name === "div" && attribs['data-cookiebot-declaration'] == "true"
      if(cookiebotDeclaration) {
        return (
          <CookiebotDeclaration>
             {domToReact(children, options)}
          </CookiebotDeclaration>
        )
      }

      const bookingWidget = name === "div" && attribs['data-booking-widget'] == "true"
      if(bookingWidget) {
        const bookingJson = attribs['data-settings'] ? decodeJsonStrings(JSON.parse(attribs['data-settings'])) : null
        return (
          <BookingWidget settings={bookingJson} >
            {domToReact(children, options)}
          </BookingWidget>
        )
      }

      const isIcon = name === "i" && attribs['data-icon']
      if(isIcon) {
        return (
          <Icon name={attribs['data-icon']} className={attribs.class}>
          {domToReact(children, options)}
          </Icon>
        )
      }

      // === Opening Times
      const isOpeningTimes = name == "div" && attribs['data-opening-times'] == "true"
      if( isOpeningTimes ) {
        const openingTimeIds = attribs['data-ids'] || null
        const headingStyle = attribs['data-style'] || null
        return(
          <OpeningTimes ids={openingTimeIds} headingStyle={headingStyle} />
        )
      }

      const featuresCarousel = name === "div" && attribs['data-features-carousel'] == "true"
      if( featuresCarousel ) {
        return(
          <FeaturesCarousel />
        )
      }

      const swingSign = name === "div" && attribs['data-swingsign'] == "true"
      if( swingSign ) {
        return(
          <SwingSign />
        )
      }

      const wordMark = name === "div" && attribs['data-wordmark'] == "true"
      if( wordMark ) {
        return(
          <Wordmark />
        )
      }

      // === Carousel
      const isCarousel = name === "div" && attribs['data-carousel'] == "true"
      if( isCarousel ) {
        const carouselOptions = JSON.parse(attribs['data-options'])
        return(
          <Carousel options={carouselOptions}>
             {domToReact(children, options)}
          </Carousel>
        )
      }

      // === Carousel slide
      const isSlide = name === "div" && attribs['data-carousel-slide'] == "true"
      if( isSlide ) {
        return(
          <CarouselSlide hash={attribs['data-hash']}>
             {domToReact(children, options)}
          </CarouselSlide>
        )
      }

      // === Calendar dropdown ===
      const isCalendarDropdown = name === "div" && attribs['data-calendar-dropdown'] == "true"
      if( isCalendarDropdown ) {
        return(
          <CalendarDropdown align={attribs['data-align']}>
             {domToReact(children, options)}
          </CalendarDropdown>
        )
      }

      // === Locations dropdown ===
      const isLocationsDropdown = name === "div" && attribs['data-locations-dropdown'] == "true"
      if( isLocationsDropdown ) {
        const postsJson = decodeJsonStrings(JSON.parse(attribs['data-json']))
        return(
          <LocationsDropdown json={postsJson} />
        )
      }

      // === Location Menus navigation
      const isLocationMenusNav = name === "div" && attribs['data-location-menus-nav'] == 'true'
      if( isLocationMenusNav ) {
        const postsJson = decodeJsonStrings(JSON.parse(attribs['data-json']))
        return (
          <LocationsMenusNav json={postsJson}>
             {domToReact(children, options)}
          </LocationsMenusNav>
        )
      }

      // === Location details navigation
      const isLocationDetailsNav = name === "div" && attribs['data-location-details-nav'] == 'true'
      if( isLocationDetailsNav ) {
        return (
          <LocationsDetailsNav>
             {domToReact(children, options)}
          </LocationsDetailsNav>
        )
      }

    },
  };

  return parse(html, options);
}
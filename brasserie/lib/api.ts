import { decodeJsonStrings } from "./utils"

const API_URL = process.env.WORDPRESS_API_URL
const REST_API_URL = process.env.NEXT_PUBLIC_WORDPRESS_REST_API

const ancestors = `
ancestors {
  nodes {
    databaseId
    ... on Location {
          title
        }
  }
}`

const optGeneralLocation = `
optGeneral {
  optHide
  optAddress
  optAddressMultiline
  optGroup
  optDirections
  optTown
  optCounty
  optPhone
  optEmail
  atreemoPubId
  disableSignup
  optSocials {
    link
    provider
  }
  optPubClosed
  optPubClosedText
  optPubClosedDate
  optTimesNew{
    name
    hideStatus
    hideSectionEntirely
    entries {
      label
      from
      to
      note
      closed
      days
    }
  }
  optFeaturesCarousel {
    enabled
    title
    items {
      icon
      label
      link
      newTab
    }
  }
}`

const optBooking = `
optBooking{
  disableBooking
  usePromocodes
  zonaleventsRestId
  zonaleventsDefaultOccasion
  zonaleventsExcludedOccasion{
    name
    id
  }
  zonaleventsDefaultMenu
  zonaleventsDefaultArea
  zonaleventsDefaultMenuShow
  zonaleventsDefaultAreaShow
  zonaleventsDefaultUpsellShow
  zonaleventsConfirmationInfo
  zonaleventsEnquiryInfo
}`

const optBookingLocation = `
optBooking{
  disableBooking
  usePromocodes
  zonaleventsRestId
  zonaleventsDefaultOccasion
  zonaleventsDefaultMenu
  zonaleventsDefaultArea
  zonaleventsDefaultMenuShow
  zonaleventsDefaultAreaShow
  zonaleventsDefaultUpsellShow
  zonaleventsConfirmationInfo
  zonaleventsEnquiryInfo
}`

const singleMenu = `
singleMenu {
  menuType
  menuHide
  menuMenu
  menuCustomTitle
  menuBadge {
    sourceUrl(size: MEDIUM_LARGE)
    mediaDetails {
      sizes(include: MEDIUM_LARGE) {
        width
        height
      }
      width
      height
    }
  }
}`

async function fetchAPI(query = '', { variables }: Record<string, any> = {}) {
  const headers = { 'Content-Type': 'application/json' }

  if (process.env.WORDPRESS_AUTH_REFRESH_TOKEN) {
    headers[
      'Authorization'
    ] = `Bearer ${process.env.WORDPRESS_AUTH_REFRESH_TOKEN}`
  }

  // WPGraphQL Plugin must be enabled
  const res = await fetch(API_URL, {
    headers,
    method: 'POST',
    body: JSON.stringify({
      query,
      variables,
    }),
  })

  const json = await res.json()
  if (json.errors) {
    console.error(json.errors)
    throw new Error('Failed to fetch API')
  }
  return json.data
}

async function fetchRestAPI(endpoint, formData) {
  let myHeaders = new Headers();
  // console.log(REST_API_URL)

  const res = await fetch(REST_API_URL+endpoint, {
    method: 'POST',
    headers: myHeaders,
    body: formData,
    // redirect: 'follow'
  })

  const json = await res.json()
  if (json.errors) {
    console.error(json.errors)
    throw new Error('Failed to fetch API')
  }
  return json
}

// endpoint: /contact-form-7/v1/contact-forms/3739/feedback
export async function sendCF7Form(id, formData) {
  const data = await fetchRestAPI(`/contact-form-7/v1/contact-forms/${id}/feedback`, formData)
  return data
}

export async function getPreviewPost(postType, id, idType = 'DATABASE_ID', PostIdType = 'PostIdType') {
  const data = await fetchAPI(
    `
    query PreviewPost($id: ID!, $idType: ${PostIdType}!) {
      ${postType}(id: $id, idType: $idType) {
        databaseId
        slug
        uri
        status
      }
    }`,
    {
      variables: { id, idType },
    }
  )
  return data
}

export async function wpSettings() {
  const data = await fetchAPI(`
  query GetSettings {
    siteID
    acf: heartwoodSettings {
      optGeneral {
        optBrand
        optLogoWordmark
        optLogoSign{
          sourceUrl
        }
        optAddress
        optAddressMultiline
        optPhone
        optEmail
        optSignupTitle
        optSignupDisable
        optSignupContent
        optSignupLink
        optSignupLabel
        optSignupNewtab
        optFooterBtn
        optFooterBtnLink
        optFooterBtnNewtab
        optSocials {
          link
          provider
        }
        optGtm
        optPixel
        optCookiebot
        optBing
        optStellar
        optMaintenanceBody
        optSplashSignup {
          show
          title
          source
          text
        }
        optSplashRecruitment {
          show
          title
          text
          btnLabel
          btnLink
        }
        optSplashMenus {
          show
          title
          text
          btnLabel
          btnLink
        }
        optSplashRoom {
          show
          title
          text
          btnLabel
          btnLink
        }
        optAllowedFlags
        optLoyaltyBtn
        optLoyaltyBtnLink
        optLoyaltyBtnLabel
      }
      ${optBooking}
      seoRobots {
        optRobots
      }
    }
    general: generalSettings {
        description
        language
        title
        url
    }
    reading: readingSettings {
      pageForPosts
      pageOnFront
      postsPerPage
    }
    headlessConfig {
      frontendUrl
      error404Page {
        ... on Page {
          id
          slug
        }
      }
    }
    locationsMenusData
  }
  `)
  return decodeJsonStrings(data)
}

export async function getAllPostsWithSlug() {
  const data = await fetchAPI(`
    {
      posts(first: 10000) {
        edges {
          node {
            slug
          }
        }
      }
    }
  `)
  return data?.posts
}

export async function getAllRecipesWithSlug() {
  const data = await fetchAPI(`
    {
      recipes(first: 10000) {
        edges {
          node {
            slug
          }
        }
      }
    }
  `)
  return data?.recipes
}

export async function getAllPagesWithSlug() {
  const data = await fetchAPI(`
    query GetPages {
      siteID
      headlessConfig {
        frontendUrl
        error404Page {
          ... on Page {
            id
            slug
          }
        }
      }
      pages: pages(first: 10000) {
        edges {
          node {
            slug
            uri
          }
        }
      }
    }
  `)
  // Filter out page: Home, Blog, categories, archive
  data.pages.edges = data.pages.edges.filter(({ node }) =>
      node.slug !== 'news' && node.slug !== 'home' &&
      node.slug !== 'announcements' && node.slug !== 'offers' && node.slug !== 'events' && node.slug !== 'recipes' && node.slug !== 'archive'
      && node.slug !== data.headlessConfig.error404Page?.slug
    )
  // console.log(data.pages.edges)
  return data?.pages
}

export async function getAllLocationsWithSlug() {
  const data = await fetchAPI(`
    {
      locations(first: 10000) {
        edges {
          node {
            slug
            uri
          }
        }
      }
    }
  `)
  return data?.locations
}

export async function getHomePage() {
  const data = await fetchAPI(
    `
    {
      page(id: "/", idType: URI) {
        id
        title
        uri
        content
        pageOptions {
          hideTitle
          showSignup
          cssClass
          customFloatingCta {
            title
            content
            link
            label
            newTab
          }
        }
        HeroContent {
          hpEnable
        }
        promoPopup{
          promoEnable
        }
        seo {
          canonical
          title
          metaRobotsNoindex
          metaRobotsNofollow
          opengraphTitle
          opengraphDescription
          opengraphImage {
            sourceUrl
          }
          opengraphUrl
          opengraphSiteName
          opengraphModifiedTime
          schema {
              raw
          }
        }
      }
    }
    `
  )

  return data
}

export async function getPostsPage(id, howMany = 6) {
  const data = await fetchAPI(
    `
    {
      page(id: "${id}", idType: DATABASE_ID) {
        id
        title
        slug
        content
        pageOptions {
          hideTitle
          showSignup
          cssClass
        }
        promoPopup{
          promoEnable
        }
        HeroContent {
          hpEnable
          hpType
          hpOverlay
          hpCtnBg
          hpTitle
          hpContent
          hpBgImage {
            sourceUrl(size: LARGE)
            mediaDetails {
              width
              height
            }
          }
          hpCarouselImages {
            sourceUrl
          }
          hpVideo
          hpVideoRatio
          hpVideoMobile
          hpVideoMobileRatio
          hpVideoAutoplay
        }
        seo {
          canonical
          title
          metaRobotsNoindex
          metaRobotsNofollow
          opengraphTitle
          opengraphDescription
          opengraphImage {
            sourceUrl
          }
          opengraphUrl
          opengraphSiteName
          opengraphModifiedTime
          schema {
              raw
          }
        }
      }
      posts: filteredPosts(first: ${howMany}) {
        pageInfo {
          offsetPagination {
            hasMore
            hasPrevious
            total
          }
        }
        edges {
          node {
            slug
            title
            date
            excerpt
            singlePost {
              expiryDate
            }
            categories {
              nodes {
                slug
              }
            }
            featuredImage {
              node {
                sourceUrl(size: MEDIUM_LARGE)
                mediaDetails {
                  sizes(include: MEDIUM_LARGE) {
                    width
                    height
                  }
                  width
                  height
                }
              }
            }
          }
        }
      }
    }
    `
  )
  return data
}

export async function getCategoryFilters() {
  // posts
  const cats = ["announcements", "offers", "events"]
  let query = ``
  cats.forEach(function(item){
    query += `
    ${item}: posts(where: {categoryName: "${item}"}) {
      p: pageInfo {
        o: offsetPagination {
          t: total
        }
      }
    }
    `
  })
  // recipes
  query += `
  recipes: recipes {
    p: pageInfo {
      o: offsetPagination {
        t: total
      }
    }
  }
  `
  const data = await fetchAPI(
    `
    {
    ${query}
    }
    `
  )
  return data
}

export async function getCategoryPage(slug, howMany = 6) {
  const data = await fetchAPI(
    `
    {
      page(id: "${slug}", idType: URI) {
        id
        title
        slug
        content
        pageOptions {
          hideTitle
          showSignup
          cssClass
        }
        promoPopup{
          promoEnable
        }
        HeroContent {
          hpEnable
          hpType
          hpOverlay
          hpCtnBg
          hpTitle
          hpContent
          hpBgImage {
            sourceUrl(size: LARGE)
            mediaDetails {
              width
              height
            }
          }
          hpCarouselImages {
            sourceUrl
          }
          hpVideo
          hpVideoRatio
          hpVideoMobile
          hpVideoMobileRatio
          hpVideoAutoplay
        }
        seo {
          canonical
          title
          metaRobotsNoindex
          metaRobotsNofollow
          opengraphTitle
          opengraphDescription
          opengraphImage {
            sourceUrl
          }
          opengraphUrl
          opengraphSiteName
          opengraphModifiedTime
          schema {
              raw
          }
        }
      }
      posts: filteredPosts(first: ${howMany}, after: "0", before: "${slug}") {
        pageInfo {
          offsetPagination {
            hasMore
            hasPrevious
            total
          }
        }
        edges {
          node {
            slug
            title
            date
            excerpt
            singlePost {
              expiryDate
            }
            categories {
              nodes {
                slug
              }
            }
            featuredImage {
              node {
                sourceUrl(size: MEDIUM_LARGE)
                mediaDetails {
                  sizes(include: MEDIUM_LARGE) {
                    width
                    height
                  }
                  width
                  height
                }
              }
            }
          }
        }
      }
    }
    `
  )
  return data
}

export async function getArchivePage(slug, howMany = 6) {
  const data = await fetchAPI(
    `
    {
      page(id: "${slug}", idType: URI) {
        id
        title
        slug
        content
        pageOptions {
          hideTitle
          showSignup
          cssClass
        }
        promoPopup{
          promoEnable
        }
        HeroContent {
          hpEnable
          hpType
          hpOverlay
          hpCtnBg
          hpTitle
          hpContent
          hpBgImage {
            sourceUrl(size: LARGE)
            mediaDetails {
              width
              height
            }
          }
          hpCarouselImages {
            sourceUrl
          }
          hpVideo
          hpVideoRatio
          hpVideoMobile
          hpVideoMobileRatio
          hpVideoAutoplay
        }
        seo {
          canonical
          title
          metaRobotsNoindex
          metaRobotsNofollow
          opengraphTitle
          opengraphDescription
          opengraphImage {
            sourceUrl
          }
          opengraphUrl
          opengraphSiteName
          opengraphModifiedTime
          schema {
              raw
          }
        }
      }
      posts: archivePosts(first: ${howMany}) {
        pageInfo {
          offsetPagination {
            hasMore
            hasPrevious
            total
          }
        }
        edges {
          node {
            slug
            title
            date
            excerpt
            singlePost {
              expiryDate
            }
            categories {
              nodes {
                slug
              }
            }
            featuredImage {
              node {
                sourceUrl(size: MEDIUM_LARGE)
                mediaDetails {
                  sizes(include: MEDIUM_LARGE) {
                    width
                    height
                  }
                  width
                  height
                }
              }
            }
          }
        }
      }
    }
    `
  )
  return data
}

export async function getPostAndMorePosts(slug, postsPerPage, preview, previewData) {
  const postPreview = preview && previewData?.post
  // The slug may be the id of an unpublished post
  const isId = Number.isInteger(Number(slug))
  const isSamePost = isId
    ? Number(slug) === postPreview.id
    : slug === postPreview.slug
  const isDraft = isSamePost && postPreview?.status === 'draft'
  const isRevision = isSamePost && postPreview?.status === 'publish'
  const data = await fetchAPI(
    `
    fragment PostFields on Post {
      title
      slug
      date
      singlePost {
        expiryDate
        terms
      }
    }
    query PostBySlug($id: ID!, $idType: PostIdType!) {
      post(id: $id, idType: $idType) {
        ...PostFields
        id
        content
        status
        postPassword
        broadcastedUrl
        categories {
          nodes {
            slug
          }
        }
        tags {
          nodes {
            name
          }
        }
        featuredImage {
          node {
            sourceUrl(size: LARGE)
            mediaDetails {
              sizes(include: LARGE) {
                width
                height
              }
              width
              height
            }
          }
        }
        seo {
          canonical
          title
          metaRobotsNoindex
          metaRobotsNofollow
          opengraphTitle
          opengraphDescription
          opengraphImage {
            sourceUrl
          }
          opengraphUrl
          opengraphSiteName
          opengraphModifiedTime
          schema {
              raw
          }
        }
        ${
          // Only some of the fields of a revision are considered as there are some inconsistencies
          isRevision
            ? `
        revisions(first: 1, where: { orderby: { field: MODIFIED, order: DESC } }) {
          edges {
            node {
              title
              excerpt
              status
              content
            }
          }
        }
        `
            : ''
        }
      }
    }
  `,
    {
      variables: {
        id: isDraft ? postPreview.id : slug,
        idType: isDraft ? 'DATABASE_ID' : 'SLUG',
      },
    }
  )

  // Draft posts may not have an slug
  if (isDraft) data.post.slug = postPreview.id
  // Apply a revision (changes in a published post)
  if (isRevision && data.post.revisions) {
    const revision = data.post.revisions.edges[0]?.node

    if (revision) Object.assign(data.post, revision)
    delete data.post.revisions
  }

  return data
}

export async function getRecipes(howMany = 6) {
  const data = await fetchAPI(
    `
    {
      posts: filteredRecipes(first: ${howMany}, after: "0") {
        pageInfo {
          offsetPagination {
            hasMore
            hasPrevious
            total
          }
        }
        edges {
          node {
            slug
            title
            date
            excerpt
            featuredImage {
              node {
                sourceUrl(size: MEDIUM_LARGE)
                mediaDetails {
                  sizes(include: MEDIUM_LARGE) {
                    width
                    height
                  }
                  width
                  height
                }
              }
            }
          }
        }
      }
    }
    `
  )
  return data
}

export async function getRecipeBySlug(slug, preview, previewData) {
  const postPreview = preview && previewData?.post
  // The slug may be the id of an unpublished post
  const isId = Number.isInteger(Number(slug))
  const isSamePost = isId
    ? Number(slug) === postPreview.id
    : slug === postPreview.slug
  const isDraft = isSamePost && postPreview?.status === 'draft'
  const isRevision = isSamePost && postPreview?.status === 'publish'
  const data = await fetchAPI(
    `
    query RecipeBySlug($id: ID!, $idType: RecipeIdType!) {
      recipe(id: $id, idType: $idType) {
        id
        slug
        date
        title
        content
        status
        postPassword
        broadcastedUrl
        tags {
          nodes {
            name
          }
        }
        featuredImage {
          node {
            sourceUrl(size: LARGE)
            mediaDetails {
              sizes(include: LARGE) {
                width
                height
              }
              width
              height
            }
          }
        }
        recipe {
          recipeServe
          recipeIngredients {
            item: recipeIngredientsItem
          }
          ingredientsSection {
            title: recipeSectionTitle
            list: recipeSectionList {
              item: recipeSectionItem
            }
          }
          recipeMethodList {
            item: recipeMethodItem
          }
          methodSections {
            title: recipeSectionTitle
            list: recipeSectionList {
              item: recipeSectionItem
            }
          }
          recipeVideo
          recipeVideoCaption
        }
        seo {
          canonical
          title
          metaRobotsNoindex
          metaRobotsNofollow
          opengraphTitle
          opengraphDescription
          opengraphImage {
            sourceUrl
          }
          opengraphUrl
          opengraphSiteName
          opengraphModifiedTime
          schema {
              raw
          }
        }
        ${
          // Only some of the fields of a revision are considered as there are some inconsistencies
          isRevision
            ? `
        revisions(first: 1, where: { orderby: { field: MODIFIED, order: DESC } }) {
          edges {
            node {
              title
              excerpt
              status
              content
            }
          }
        }
        `
            : ''
        }
      }
    }
  `,
    {
      variables: {
        id: isDraft ? postPreview.id : slug,
        idType: isDraft ? 'DATABASE_ID' : 'SLUG',
      },
    }
  )

  // Draft posts may not have an slug
  if (isDraft) data.recipe.slug = postPreview.id
  // Apply a revision (changes in a published post)
  if (isRevision && data.recipe.revisions) {
    const revision = data.recipe.revisions.edges[0]?.node

    if (revision) Object.assign(data.recipe, revision)
    delete data.recipe.revisions
  }

  return data
}

/**
 * Get Location (only top level)
 */
export async function getLocations() {
  const data = await fetchAPI(
    `
    {
      locations(first: 1000, where: {
        orderby: { field: TITLE, order: ASC },
        parent: 0
        }) {
        edges {
          node {
            id
            databaseId
            title
            slug
            uri
            status
            ${optGeneralLocation}
          }
        }
      }
    }
    `
  )
  return data.locations.edges
}

/**
 * Get All Menus by parent Location
 */
export async function getMenusByLocation(id) {
  const data = await fetchAPI(
    `
    {
      locations(first: 1000, where: { orderby: { field: TITLE, order: ASC } }) {
        edges {
          node {
            slug
            uri
            title
            template {
              templateName
            }
            ${ancestors}
            ${singleMenu}
          }
        }
      }
    }
    `
  )
  data.locations.edges = data.locations.edges.filter(({node}) => node.template.templateName.toLowerCase().includes('menu'))
  data.locations.edges = data.locations.edges.filter(({node}) => node.ancestors.nodes.filter((item) => item.databaseId == id).length > 0)
  return data.locations.edges
}

export async function getPageBySlug(slug, preview, previewData) {
  const pagePreview = preview && previewData?.post
  // The slug may be the id of an unpublished post
  const isId = Number.isInteger(Number(slug))
  const isSamePost = isId
    ? Number(slug) === pagePreview.id
    : slug === pagePreview.slug
  const isDraft = isSamePost && pagePreview?.status === 'draft'
  const isRevision = isSamePost && pagePreview?.status === 'publish'
  let id = isDraft ? pagePreview.id : slug,
        idType = isDraft ? 'DATABASE_ID' : 'URI'
  const data = await fetchAPI(
    `
    {
      page(id: "${id}", idType: ${idType}) {
        id
        title
        slug
        status
        content
        postPassword
        broadcastedUrl
        pageOptions {
          hideTitle
          showSignup
          layout
          cssClass
          customFloatingCta {
            title
            content
            link
            label
            newTab
          }
        }
        HeroContent {
          hpEnable
        }
        promoPopup{
          promoEnable
        }
        seo {
          canonical
          title
          metaRobotsNoindex
          metaRobotsNofollow
          opengraphTitle
          opengraphDescription
          opengraphImage {
            sourceUrl
          }
          opengraphUrl
          opengraphSiteName
          opengraphModifiedTime
          schema {
              raw
          }
        }
        ${
          // Only some of the fields of a revision are considered as there are some inconsistencies
          isRevision
            ? `
        revisions(first: 1, where: { orderby: { field: MODIFIED, order: DESC } }) {
          edges {
            node {
              title
              slug
              status
              content
            }
          }
        }
        `
            : ''
        }
      }
    }
  `
  )

  // Draft posts may not have an slug
  if (isDraft) data.page.slug = pagePreview.id
  // Apply a revision (changes in a published post)
  if (isRevision && data.page.revisions) {
    const revision = data.page.revisions.edges[0]?.node

    if (revision) Object.assign(data.page, revision)
    delete data.page.revisions
  }

  return data
}

export async function getPageByTemplate(template) {
  const data = await fetchAPI(
    `
    {
      pages(first: 1000) {
        edges {
          node {
            databaseId
            template {
              templateName
            }
          }
        }
      }
    }
    `
  )
  data.pages.edges = data.pages.edges.filter(({node}) => node.template.templateName.toLowerCase().includes(template))
  return data.pages.edges[0]
}

export async function getLocationBySlug(slug, preview, previewData) {
  const pagePreview = preview && previewData?.post
  // The slug may be the id of an unpublished post
  const isId = Number.isInteger(Number(slug))
  const isSamePost = isId
    ? Number(slug) === pagePreview.id
    : slug === pagePreview.slug
  const isDraft = isSamePost && pagePreview?.status === 'draft'
  const isRevision = isSamePost && pagePreview?.status === 'publish'
  const id = isDraft ? pagePreview.id : slug,
        idType = isDraft ? 'DATABASE_ID' : 'URI'
  const data = await fetchAPI(
    `
    {
      location(id: "${id}", idType: ${idType}) {
        id
        databaseId
        title
        slug
        status
        content
        postPassword
        template {
          templateName
        }
        ${ancestors}
        pageOptions {
          hideTitle
          showSignup
          cssClass
          customFloatingCta {
            title
            content
            link
            label
            newTab
          }
        }
        ${singleMenu}
        ${optGeneralLocation}
        ${optBookingLocation}
        HeroContent {
          hpEnable
        }
        promoPopup{
          promoEnable
        }
        seo {
          canonical
          title
          metaRobotsNoindex
          metaRobotsNofollow
          opengraphTitle
          opengraphDescription
          opengraphImage {
            sourceUrl
          }
          opengraphUrl
          opengraphSiteName
          opengraphModifiedTime
          schema {
              raw
          }
        }
        ${
          // Only some of the fields of a revision are considered as there are some inconsistencies
          isRevision
            ? `
        revisions(first: 1, where: { orderby: { field: MODIFIED, order: DESC } }) {
          edges {
            node {
              title
              slug
              status
              content
            }
          }
        }
        `
            : ''
        }
      }
    }
  `
  )

  // Draft posts may not have an slug
  if (isDraft) data.location.slug = pagePreview.id
  // Apply a revision (changes in a published post)
  if (isRevision && data.location.revisions) {
    const revision = data.location.revisions.edges[0]?.node

    if (revision) Object.assign(data.location, revision)
    delete data.location.revisions
  }

  return data
}

export async function getMenuItemsByLocation(location) {
  const data = await fetchAPI(
    `
    query MENU_ITEMS($location: MenuLocationEnum) {
      menuItems(first:999, where: {location: $location}) {
        nodes {
          key: id
          parentId
          title: label
          uri
          desc: description
          target
          cssClasses
        }
      }
    }
  `,
  {
    variables: {
      location: location
    }
  }
  )

  return data.menuItems
}

/** ==========================
 * ACF Field Groupd (metaboxes)
 * ===========================
 */

/**
 * Hero content
 */
export async function ACF_HeroContent(slug, preview, previewData, postType='page', idType='URI') {
  const pagePreview = preview && previewData?.post
  // The slug may be the id of an unpublished post
  const isId = Number.isInteger(Number(slug))
  const isSamePost = isId
    ? Number(slug) === pagePreview.id
    : slug === pagePreview.slug
  const isDraft = isSamePost && pagePreview?.status === 'draft'
  const id = isDraft ? pagePreview.id : slug

  if(isDraft) idType = 'DATABASE_ID'

  const data = await fetchAPI(
    `
    {
      ${postType}(id: "${id}", idType: ${idType}) {
        HeroContent {
          hpEnable
          hpType
          hpOverlay
          hpCtnBg
          hpTitle
          hpContent
          hpBgImage {
            sourceUrl(size: PORTFOLIO_FULL)
            mediaDetails {
              width
              height
              sizes(include: PORTFOLIO_FULL) {
                width
                height
              }
            }
            altText
          }
          hpBgImageMobile {
            sourceUrl(size: MEDIUM_LARGE)
            mediaDetails {
              width
              height
              sizes(include: MEDIUM_LARGE) {
                width
                height
              }
            }
            altText
          }
          hpCarouselImages {
            sourceUrl(size: PORTFOLIO_FULL)
            mediaDetails {
              width
              height
              sizes(include: PORTFOLIO_FULL) {
                width
                height
              }
            }
            altText
          }
          hpCarouselImagesMob {
            sourceUrl(size: MEDIUM_LARGE)
            mediaDetails {
              width
              height
              sizes(include: MEDIUM_LARGE) {
                width
                height
              }
            }
            altText
          }
          hpVideo
          hpVideoRatio
          hpVideoMobile
          hpVideoMobileRatio
          hpVideoAutoplay
        }
      }
    }
  `
  )
  return data[postType].HeroContent
}

/**
 * Promo Popup
 */
export async function ACF_PromoPopup(slug, preview, previewData, postType='page', idType='URI') {
  const pagePreview = preview && previewData?.post
  // The slug may be the id of an unpublished post
  const isId = Number.isInteger(Number(slug))
  const isSamePost = isId
    ? Number(slug) === pagePreview.id
    : slug === pagePreview.slug
  const isDraft = isSamePost && pagePreview?.status === 'draft'
  const id = isDraft ? pagePreview.id : slug

  if(isDraft) idType = 'DATABASE_ID'

  const data = await fetchAPI(
    `
    {
      ${postType}(id: "${id}", idType: ${idType}) {
        id
        promoPopup {
          promoEnable
          promoPopupTimeout
          promoImage {
            mediaDetails {
              sizes(include: LARGE) {
                width
                height
              }
              width
              height
            }
            sourceUrl(size: LARGE)
          }
          promoImageLink
          promoImageTarget
          promoContent
          promoButton {
            label: promoButtonLabel
            link: promoButtonLink
            target: promoButtonTarget
          }
        }
      }
    }
  `
  )
  data[postType].promoPopup["postID"] = data[postType].id
  return data[postType].promoPopup
}

/**
 * CPT: get SEO
*/
export async function getCptSeo( $postType ) {
  const data = await fetchAPI(`
  {
    seo {
      contentTypes {
        ${$postType} {
          schema {
            raw
          }
          archive {
            title
            archiveLink
            metaDesc
            metaRobotsFollow
            metaRobotsIndex
          }
        }
      }
      schema {
        siteName
        siteUrl
      }
    }
  }
  `)
  return data.seo
}

export async function getBlogPosts(postsPerPage, slug=null) {
  const data = await fetchAPI(`
  fragment PostFields on Post {
    title
    slug
    date
    featuredImage {
      node {
        sourceUrl(size: MEDIUM_LARGE)
        mediaDetails {
          sizes(include: MEDIUM_LARGE) {
            width
            height
          }
          width
          height
        }
      }
    }
    singlePost {
      expiryDate
      terms
    }
  }
  query BlogPosts{
    news: posts(first: ${postsPerPage+1}, where: { orderby: { field: DATE, order: DESC }, categoryName: "announcements" }) {
      edges {
        node {
          ...PostFields
        }
      }
    }
    offers: posts(first: ${postsPerPage+1}, where: { orderby: { field: DATE, order: DESC }, categoryName: "offers" }) {
      edges {
        node {
          ...PostFields
        }
      }
    }
    events: posts(first: ${postsPerPage+1}, where: { orderby: { field: DATE, order: DESC }, categoryName: "events" }) {
      edges {
        node {
          ...PostFields
        }
      }
    }
    recipes: posts(first: ${postsPerPage+1}, where: { orderby: { field: DATE, order: DESC }, categoryName: "recipes" }) {
      edges {
        node {
          ...PostFields
        }
      }
    }
    bedrooms: posts(first: ${postsPerPage+1}, where: { orderby: { field: DATE, order: DESC }, categoryName: "bedrooms" }) {
      edges {
        node {
          ...PostFields
        }
      }
    }
  }
  `)

  // Filter out the main post
  if( slug ) {
    data.news.edges = data.news.edges.filter(({ node }) => node.slug !== slug)
    data.offers.edges = data.offers.edges.filter(({ node }) => node.slug !== slug)
    data.events.edges = data.events.edges.filter(({ node }) => node.slug !== slug)
    data.recipes.edges = data.recipes.edges.filter(({ node }) => node.slug !== slug)
    data.bedrooms.edges = data.bedrooms.edges.filter(({ node }) => node.slug !== slug)
  }

  // If there are still more posts, remove the last one
  if (data.news.edges.length > postsPerPage) data.news.edges.pop()
  if (data.offers.edges.length > postsPerPage) data.offers.edges.pop()
  if (data.events.edges.length > postsPerPage) data.events.edges.pop()
  if (data.recipes.edges.length > postsPerPage) data.recipes.edges.pop()
  if (data.bedrooms.edges.length > postsPerPage) data.bedrooms.edges.pop()

  return {
    news: data.news.edges,
    offers: data.offers.edges,
    events: data.events.edges,
    recipes: data.recipes.edges,
    bedrooms: data.bedrooms.edges
  }
}

export async function getMaintenancePlaceholder(slug) {
  const data = await fetchAPI(
    `
    {
      page(id: "${slug}", idType: URI) {
        status
        id
        title
        uri
        content
      }
    }
    `
  )

  return data.page
}
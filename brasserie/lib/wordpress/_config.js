// Define valid WP post types (singular and plural GraphQL names).
export const postTypes = {
    page: {
      pluralName: 'pages',
      route: '',
      qlType: 'PageIdType'
    },
    post: {
      pluralName: 'posts',
      route: 'news',
      qlType: 'PostIdType'
    }
  }

  // Define hierarchical post types.
  export const hierarchicalPostTypes = ['page']

  export function isHierarchicalPostType(postType) {
    return hierarchicalPostTypes.includes(postType)
  }
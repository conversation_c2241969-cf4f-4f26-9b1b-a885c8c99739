body {
  #CybotCookiebotDialog {
    border-radius: unset;
    letter-spacing: 0;
    box-shadow: none;
    padding: 15px;
    overflow: initial;


    .CybotCookiebotDialogContentWrapper {
      &::after {
        content: '';
        display: block;
        background: $celadon;
        width: 100vw;
        position: absolute;
        top: -15px;
        bottom: -15px;
        left: calc(50% - 50vw);
        z-index: -1;

        @include media-breakpoint-up(xl) {
          top: -24px;
          bottom: -24px;
        }
      }
    }

    #CybotCookiebotDialogBodyLevelButtonLevelOptinAllowallSelection {
      background: $warm;
    }

    #CybotCookiebotDialogBodyContent {
      overflow: initial;
    }

    #CybotCookiebotDialogBodyContentText {
      font-family: $headings-font-family;
      font-size: 1.25rem;
    }

    .CybotCookiebotDialogHeaderLogosWrapper,
    > div:not(.CybotCookiebotDialogContentWrapper) {
      display: none !important;
    }

    .Cy<PERSON><PERSON>ookiebotScrollContainer {
      margin-bottom: $spacer;

      .CybotCookiebotScrollbarContainer {
        display: none !important; // unnecessary scrollbar
      }
    }

    #CybotCookiebotDialogFooter {
      position: absolute !important;
      bottom: 0 !important;
      right: 0 !important;
      width: 60% !important;
      padding-left: 0 !important;
    }

    #CybotCookiebotDialogBodyEdgeMoreDetails {
      width: 40%;
      text-align: right !important;
      padding-right: $spacer/2 !important;
      line-height: 1;

      @include media-breakpoint-up(xl) {
        line-height: 1.5;
      }
    }

    //-----------------------------------------
    // OPEN COOKIE DIALOG - configuration view
    //-----------------------------------------

    &.CybotCookiebotDialogActive:not(.CybotEdge) {
      #CybotCookiebotDialogBodyButtonDecline {
        display: block !important;
      }

      #CybotCookiebotDialogFooter {
        position: static !important;
        width: 100% !important;
        padding: 0 !important;

        #CybotCookiebotDialogBodyButtonsWrapper {
          align-items: center;
          justify-content: center;
        }
      }
    }
  }

  #CybotCookiebotDialog.CybotEdge {
    max-width: 340px;
    box-shadow: none;

    @include media-breakpoint-up(md) {
      max-width: 450px;
    }
  }
  #CybotCookiebotDialogHeader,
  #CybotCookiebotDialogBodyContentTitle,
  #CybotCookiebotDialogBodyLevelWrapper,
  #CybotCookiebotDialogPoweredByText,
  #CybotCookiebotDialogBodyLevelButtonCustomize,
  #CybotCookiebotDialog.CybotEdge #CybotCookiebotDialogBodyLevelButtonLevelOptinAllowallSelection {
    display: none !important;
  }
  #CybotCookiebotDialogBodyContent {
    text-align: center;
    font-size: 1rem;
  }
  .CybotCookiebotDialogBodyBottomWrapper,
  #CybotCookiebotDialog .CybotCookiebotScrollContainer {
    border: none !important;
    min-height: unset;
    padding: 0;
  }
  #CybotCookiebotDialogBodyButtons .CybotCookiebotDialogBodyButton,
  #CybotCookiebotDialogFooter .CybotCookiebotDialogBodyButton {
    margin: 0.5rem 0;
  }
  #CybotCookiebotDialogBodyEdgeMoreDetails,
  #CybotCookiebotDialog.CybotEdge #CybotCookiebotDialogBodyEdgeMoreDetails {
    justify-content: right;
    margin: 0;
    padding: 0;

    a {
      padding: $spacer ($spacer * 2);
      line-height: 1.625;
      width: auto;
      max-width: unset;
      white-space: nowrap;
      font-size: 1rem;
      font-weight: 400;
      color: $celadon;
      padding: 0;
      display: inline-block;
      padding: 0.3125rem 0.625rem;
      background-color: $warm;
      border-radius: $btn-border-radius;

      &:after {
        display: none !important;
      }
    }
  }
  #CybotCookiebotDialogFooter {
    #CybotCookiebotDialogBodyButtons {
      align-items: center;
    }
    #CybotCookiebotDialogBodyButtonsWrapper {
      align-items: start;
      justify-content: start;
      flex-direction: row-reverse !important;
    }
    .CybotCookiebotDialogBodyButton {
      display: inline-block;
      line-height: 1.625;
      max-width: unset !important;
      font-weight: normal;
      width: auto !important;
      padding: 0.3125rem 0.625rem;
      white-space: nowrap;
      font-size: 1rem;
      color: $celadon !important;
      background-color: $warm;
      border-radius: $btn-border-radius;
      border: none !important;
    }
  }

  #CybotCookiebotDialog .CybotCookiebotFader.CybotCookiebotDialogActive {
    display: none !important;
  }

  @include media-breakpoint-up(md) {
    #CybotCookiebotDialog.CybotEdge,
    #CybotCookiebotDialog[data-template=slideup].CybotCookiebotDialogActive {
      // top: auto;
      // bottom: $spacer * 5;
    }
    #CybotCookiebotDialogBodyEdgeMoreDetails,
    #CybotCookiebotDialog.CybotEdge #CybotCookiebotDialogBodyEdgeMoreDetails {
      display: block;
      height: auto;
      text-align: center;
    }
    #CybotCookiebotDialog.CybotEdge.CybotMultilevel #CybotCookiebotDialogFooter {
      width: 100%;
    }
    #CybotCookiebotDialogFooter #CybotCookiebotDialogBodyButtons {
      max-width: none !important;
    }
  }

  @include media-breakpoint-up(xl) {
    #CybotCookiebotDialog.CybotEdge {
      width: 400px;
      min-width: auto;
      top: 50% !important;
      transform: translate(-50%,-50%);
    }
    #CybotCookiebotDialog.CybotEdge .CybotCookiebotDialogContentWrapper {
      flex-direction: column;
    }
    #CybotCookiebotDialog.CybotEdge.CybotMultilevel #CybotCookiebotDialogFooter {
      position: relative;
      right: auto;
      top: auto;
    }
    #CybotCookiebotDialog.CybotEdge #CybotCookiebotDialogTabContent {
      margin: 0;
    }
    #CybotCookiebotDialog #CybotCookiebotDialogBody .CybotCookiebotScrollContainer {
      max-height: none;
      width: 100%;
    }
  }

  #CybotCookiebotDialog.CybotEdge:not([lang="ta"]):not([lang="bg"]):not([lang="is"]):not([lang="el"]) #CybotCookiebotDialogTabContent {
    margin: 0;
    width: auto;
  }

  #CybotCookiebotDialog.CybotEdge #CybotCookiebotDialogBodyContent {
    padding: 0;
  }

  #CybotCookiebotDialog.CybotEdge .CybotCookiebotDialogBodyBottomWrapper {
    padding: 0;
  }

  #CybotCookiebotDialog.CybotEdge #CybotCookiebotDialogFooter .CybotCookiebotDialogBodyButton:not(:first-of-type),
  #CybotCookiebotDialog.CybotEdge #CybotCookiebotDialogFooter .CybotCookiebotDialogBodyButton:not(:last-of-type) {
    margin: 0 $spacer/2;
  }

  #CybotCookiebotDialog.CybotEdge .CybotCookiebotDialogBodyBottomWrapper {
    margin: 0;
  }
}

.cookiebot-declaration-shortcode {
  > div > div:not(.CookieDeclaration) {
    display: none !important;
  }
}
// === Hack: hide "TEST" watermark, to save some pessos
#CookieBanner {
  > div:first-child:not(#CookieBannerOverlay) {
    display: none !important;
  }
}

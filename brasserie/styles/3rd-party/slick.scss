.slick-dots {
    position: static;

    li {
        width: (9 / 16) * 1rem;
        height: (9 / 16) * 1rem;

        button {
            color: currentColor;
            width: (9 / 16) * 1rem;
            height: (9 / 16) * 1rem;
            padding: 0;

            &::before {
                content: "";
                color: currentColor;
                width: (9 / 16) * 1rem;
                height: (9 / 16) * 1rem;
                border-radius: 100%;
                border: 1px solid;
                position: static;
                display: block;
                opacity: 1;
            }
        }

        &.slick-active {
            button::before {
                color: currentColor;
                background-color: currentColor;
                opacity: 1;
            }
        }
    }
}


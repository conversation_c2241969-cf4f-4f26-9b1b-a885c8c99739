.k10-html-container {
  .fade {
    &.in {
      opacity: 1;
    }
  }
}

body {

  @media (max-width: 766px) {
    .k10-menus .k10-course {
      padding: 0;
    }
  }

  &.single-menu {
      @media (max-width: 575.99px) {
        .container {
          width: 100%;
          max-width: 100%;
        }
      }

  }
}

// tripple bars

.bars,
.bars::before,
.bars::after {
  display: block;
  height: 1px;
  width: 100%;
  position: absolute;
  background-color: #000;
  animation-name: bars-entrance;
  animation-duration: 1s;
}

.bars {
        animation-delay: 1s;

  top: calc(100% + 0.5rem);
  left: 0;

  &::before,
  &::after {
    content: '';
    top: -0.25rem;
    animation-delay: 0s;
  }

  &::after {
    top: 0.25rem;
    animation-delay: 2s;
  }
}

@keyframes bars-entrance {
  0%   {scale: 0.8; opacity: 0;}
  100% {scale: 1; opacity: 1;}
}

body {
  .k10-html-container {
    $border-color: $green;

      max-width: 744px;
      margin: 0 auto;
      border: 1px solid $border-color;
      padding: 2px;

      > .k10-menus {
        border: 1px solid $border-color;
        padding: 2px;

        > .k10-body {
          border: 1px solid $border-color;
        }
      }
  }

  .k10-w-menu {
    .k10-course__header {
      @media (min-width: 1345px) {
        // position: absolute;
        // right: calc(100% + $spacer * 4 + 7px);
        // width: calc(50%);
        // text-align: right;
        // top: 6px;

        text-align: right;
        position: relative;
        right: auto;
        top: auto;
        width: 50%;
        transform: translateX(calc(-100% - $spacer * 4 - 7px));
        margin-bottom: -1.5em;

        .k10-course__name {
          // height: 1em;
          // margin-bottom: -1em;
        }

      }
    }

    .k10-w-course__name-text {
      font-family: $font-family-heading;
      font-size: 1rem;
      letter-spacing: 1px;
      line-height: 1;
      display: block;
    }

    .k10-recipe {
      margin-top: 0;
      padding: 0;
    }

    .k10-recipe__col {
      padding: 0 2rem 0 0;
    }

    .k10-w-recipe__name {
      font-family: $font-family-subheading;
      font-weight: 500;
      font-size: 1rem;
      letter-spacing: 1px;
    }

    .k10-w-recipe__desc {
      font-family: $font-family-sans-serif;
      padding-right: 2rem;

      span {
        background: $warm;
        position: relative;
        z-index: 1;
        padding-right: 2px;
      }
    }

    .k10-w-labels-filter2__header {
      background-color: transparent;
    }

    .k10-w-byo-sub-item__price {
      font-family: $font-family-subheading;
      font-weight: 400;
      font-size: 12px;
    }

    .k10-w-recipe__price {
      font-family: $font-family-subheading;
      font-weight: 400;
      font-size: 12px;
    }

    .k10-w-info-row__name {
      font-family: $font-family-sans-serif;
    }

     .k10-w-byo-sub__name {
      font-family: $font-family-subheading;
      text-transform: uppercase;
      font-weight: 500;
     }

    .k10-w-byo-sub-item__label {
      font-family: $font-family-sans-serif;
      font-style: italic;
      font-weight: 400;
    }

    .k10-w-recipe__label {
      font-family: $font-family-sans-serif;
      font-style: italic;
      padding-right: $spacer;
    }

    .k10-w-recipe-modal__recipe-name {
      font-family: $font-family-subheading;
      font-size: 1rem;
      letter-spacing: 1px;
      font-weight: 500;
    }

    .k10-w-recipe-modal__td {
      display: inline;
      font-family: $font-family-sans-serif;
      font-size: 1rem;

      + td {
        padding-left: 0.5rem;
      }
    }

    .k10-w-recipe-modal__section-header,
    .k10-w-recipe-modal__section-values,
    .k10-w-recipe-modal__recipe-desc {
      font-family: $font-family-sans-serif;
      font-size: 1rem;
    }

    .k10-w-recipe-modal__component_labels {
      font-family: inherit;
    }

    .k10-w-toolbar__disclaimer {
      font-family: $font-family-btn;
      text-transform: uppercase;
      letter-spacing: 1px;
      padding-left: 0.625rem;
      padding-right: 0.625rem;
      border: 2px solid $red;
      text-transform: uppercase;
      line-height: 1;
      border-radius: 0;
      font-size: 12px;
      font-weight: 400;
      color: $warm;
      transition: all 0.3s ease;

      &:focus {
        border-color: $dark;
        background-color: $red;
        color: $warm;
        box-shadow: inset 0 0 0 1px $warm;
      }

      &:hover {
        color: $red !important;
        border-color: $red;
        background-color: $warm;
      }

      &:visited {
        color: $warm;
      }
    }
  }

  .k10-menus {
    .k10-wrapper {
      width: 100%;
      padding: 0 calc($spacer * 2);
    }

    .k10-course__name  {
      padding: 0;
    }

    .k10-recipe__info-img {
      box-shadow: 0 0 0 4px $warm;
    }

    .k10-course {
      margin-bottom: 115px;
      position: relative;
    }

    .k10-recipe__row {
      align-items: baseline;
      z-index: 2;
    }

    .k10-recipe__row_2 {
      position: relative;
      z-index: 1;

      &::after {
        content: '';
        display: block;
        position: absolute;
        top: calc(100% - 11px);
        right: 0;
        left: 0;
        z-index: 0;
        border-bottom: 1px dotted $green;
      }
    }

    .k10-recipe__row_3 {
      position: relative;

      &:after {
        content: "";
        display: block;
        position: absolute;
        top: calc(100% - 8px);
        right: 0;
        left: 0;
        z-index: 0;
        border-bottom: 1px dotted $green;
      }

      .k10-recipe__price {
        position: relative;
        bottom: -2px;
        background-color: $warm;
        z-index: 1;
      }
    }

    .k10-byo-item__row {
      align-items: baseline;
    }

    .k10-byo-item__col {
      padding: 0;
    }

    .k10-byo-item__price {
      position: relative;
      top: 1px;
      margin-left: 4px;
    }

    .k10-info-row {
        padding: 0;
    }

    .k10-recipe__text-wrapper {
      position: relative;
      display: flex;
      flex-direction: column;
    }

    .k10-recipe__col_price {
      position: absolute;
      right: 0;
      bottom: 2px;
      background-color: $warm;
      padding: 0;
    }

    .k10-recipe__price {
      padding-left: 4px;
    }

    .k10-recipe-modal__body {
      text-align: center;
    }

    .k10-recipe-modal__nutrients-table-wrapper {
      max-width: 300px;
      margin: 0 auto;
    }

    .k10-course_level_2 {
      margin: $spacer*5 0;
    }

    .k10-course__name_level_2,
    .k10-course__name_level_3 {
      font-size: 1rem;
      display: block;

      @media (min-width: 1345px) {
        text-align: right;
      }

      &:after {
        display: none;
      }
    }



    .k10-course__desc_level_2 {
      margin: $spacer*2 0;
      font-size: 1rem;
      padding: 0;

      @media (min-width: 1345px) {
        text-align: right;
      }
    }
  }

  .k10-w-menu .k10-w-recipe__size-name {
    font-family: var(--font-libre-franklin), Arial, sans-serif;
    font-weight: 400;
    font-size: 14px;
    background-color: $warm;
    position: relative;
    z-index: 1;
    padding-right: $spacer;
  }

  .k10-menus .k10-recipe__info-wrapper {
    background-color: $warm;
    position: relative;
    z-index: 1;
    left: -6px;
  }

  .k10-byo__sub {
    .k10-byo-item__row {
      position: relative;
      z-index: 0;

      &::after {
        content: '';
        display: block;
        width: 100%;
        border-bottom: 1px dotted;
        position: absolute;
        top: calc(100% - 11px);
        z-index: -1;
      }
    }

    .k10-byo-item__col {
      background-color: $warm;
      position: relative;
      z-index: 1;
    }

    .k10-byo-item__col_main {
      padding-right: 0.5rem;
    }
  }
}

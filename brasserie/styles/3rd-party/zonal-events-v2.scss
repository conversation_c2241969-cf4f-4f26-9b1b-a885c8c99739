#zonalevents-booking-form {
  input,
  select,
  textarea {
    text-align: left;
  }
}

.zonalevents-widget.v2 {
  max-width: 377px;
  margin: 0 auto;
  // padding: 2rem;
  position: relative;
  // border: 20px solid #efe8de;

  [disabled] {
    opacity: 0.65;
  }

  .hidden {
    display: none !important;
  }

  .widget-header {}

  .widget-content {
    position: relative;
  }

  .widget-page {
    position: relative;
    top: 0;
    padding-bottom: 8rem;
  }

  .inner {
    margin-bottom: 2rem;
  }

  .form-group {}

  .form-group {
    position: relative;
    margin-bottom: 1rem;

    &.input-error {
      .error-tip {
        display: block;
      }
    }
  }

  label {
    display: block;
  }

  .form-inline {
    position: relative;

    input[type="checkbox"],
    input[type="radio"] {
      position: absolute;
      border: 1px solid $dark;
      padding: 0;
      margin: 4px 0 0;
      -webkit-appearance: none !important;
      -moz-appearance: none !important;
      appearance: none !important;
      width: 1em !important;
      height: 1em !important;
      -webkit-transition: all .25s ease;
      transition: all .25s ease;

      &:checked {
        background: $dark;
      }

      +label {
        display: inline-block;
        padding-left: 25px;
        text-align: left;
        width: 100%;
      }
    }
  }

  .form-control[readonly] {
    cursor: default;
    background-color: #fff;
  }

  .form-check {}

  .form-check-inline {
    display: inline-block;
    margin-right: 1rem;

    label {
      display: inline-block;
    }

    +.form-check-inline {
      // margin-left: 1rem;
    }
  }

  .btn {
    &[disabled] {
      cursor: not-allowed;
    }

    &.btn--wide {
      padding-left: 2.5rem;
      padding-right: 2.5rem;
    }
  }

  .time-dropdown {
    .dropdown-toggle {
      display: block;
      width: 100%;
      max-width: none;
      height: 52px;
      font-style: italic;
      letter-spacing: 1px;
      text-align: center;
      padding: 0 12px;
      border: 1px solid $dark;
      margin: 0;
    }

    .dropdown-menu {
      border: 1px solid $dark;
      background: $warm;
      color: $dark;
      border-top: none;
      border-radius: 0;
      margin-top: 0;
      // position: relative;
      padding-bottom: 55px;
      overflow: hidden;
      // display: none;

      &.show {
        display: block;
      }

      &.no-legend {
        padding-bottom: 0;
      }
    }

    .dropdown-menu-inner {
      max-height: 580px;
      overflow-y: auto;
    }

    .time-session-label {
      padding: 0 10px;
      text-align: center;
      margin-top: 1rem;
      margin-bottom: 0.5rem;
    }

    .time-list {
      list-style: none !important;
      margin: 0;
      padding: 0 !important;
    }

    .time-item {
      display: inline-block;
      flex-grow: 1;
      margin: 0;
      margin-bottom: 0 !important;
      text-align: center;

      + .time-item {
        margin-left: $spacer/2;
      }

      &:before {
        display: none !important;
      }

      input {
        display: none !important;
      }
    }

    label,
    span {
      padding: 8px 0;
      margin: 0;
      background: $green;
      border: 2px solid;
      border-color: $green;
      color: $warm;
      width: 100%;
      text-align: center;
      cursor: pointer;
      display: block;
      font-family: $font-family-btn;
      letter-spacing: 1px;
      text-transform: uppercase;
      font-size: 10px;
      line-height: 1;
      flex-grow: 1;

      &.active,
      &:hover {
        background-color: $warm;
        color: $green;
      }

      &.limited {
        $limited-color: $celadon;

        background: $limited-color;
        border-color: $limited-color;
        color: $dark;

        &:hover {
          background-color: transparent;
        }
      }

      &[disabled] {
        $disabled-color: lighten($celadon, 20%);

        background: $disabled-color !important;
        border-color: $disabled-color !important;
        cursor: default;
        color: $dark;

        &:hover {
          background: $disabled-color !important;
        }
      }
    }

    .time-legend {
      text-align: center;
      border-top: 1px solid $green;
      margin: 10px 0 0;
      padding-top: 10px;
      padding-bottom: 5px;
      // position: absolute;
      // bottom: 0;
      // left: 0;
      // right: 0;

      span {
        cursor: default;
        padding-left: 4px;
        padding-right: 4px;

        + span {
          @include media-breakpoint-up(sm) {
            margin-left: 4px;
          }
        }

      &.active,
      &:hover {
        background-color: $green;
        color: $warm;
      }

      &.limited {
        &:hover {
          $limited-color: $celadon;

          background: $limited-color;
          border-color: $limited-color;
          color: $dark;
        }
      }

      &[disabled] {
        &:hover {
          $disabled-color: lighten($celadon, 20%);
          background: $disabled-color !important;
          border-color: $disabled-color !important;
          color: $dark;
          }
        }
      }
    }
  }

  .zonalevents-upsell-name {
    span {
      &:before {
        content: ", ";
      }

      &:first-of-type {
        &:before {
          display: none;
        }
      }
    }
  }

  .zonalevents-loader {
    overflow: hidden;
    position: absolute;
    left: -1rem;
    top: 0;
    right: -1rem;
    bottom: 0;
    background: transparentize($warm, 0.25);
    text-align: center;
    z-index: 99;
  }

  .zonalevents-message {
    position: absolute;
    left: 50%;
    top: 50%;
    background: #fff;
    padding: 1rem;
    width: 90%;
    max-width: 280px;
    text-align: center;
    border: 1px solid $dark;
    transform: translate(-50%, -50%);
  }

  .zonalevents-message-close {
    display: block;
    cursor: pointer;
    font-size: 3rem;
  }


  @media screen and (max-width: 767px) {
    border: none;
    padding: 1rem 0;

    .btn {
      white-space: normal;
      height: auto;
    }

    .time-dropdown {
      .dropdown-menu-inner {
        max-height: calc(100vh - 140px);
      }
    }
  }

  // ===== Layout: Short =====
  &.layout-short {
    position: relative;
    max-width: unset;

    .zonalevents-calendar-notes {
      display: none !important; // temporary, please remove once fixed
    }

    .widget-header {
      display: none;
    }

    .widget-page {
      padding-bottom: 0;
    }

    .zonalevents-loader {
      display: none !important;
    }

    .inner {
      margin-bottom: 0;
    }

    // --- make it inline for larger screens
    @include media-breakpoint-up(md) {
      max-width: none;

      .form-group,
      .widget-content {
        display: inline-block;
        vertical-align: bottom;
        margin: 0 $spacer/2;
      }

      .widget-content {
        margin: 0;
        width: unset;
      }

      #zonalevents-booking-form {
        text-align: center !important;
      }
    }
  }
}

.booking-details .single-line {
  display: flex;
  flex-direction: row;
  margin-bottom: 0.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid;

  > * {
    padding: 0 .25em;
    text-align: left;
  }

  >strong {
    white-space: nowrap;
  }
}

// ===== Zonal Events - bookings->enquiry
body.zonal-booking-enquiry {
  .zonalevents-widget {
    background: #efe8de;
  }
}

// === IE message ===
.zonalevents-ie-message {
  display: none;
}

/*Internet Explorer 10 & 11 : Create a media query using -ms-high-contrast, in which you place your IE 10 and 11-specific CSS styles. Because -ms-high-contrast is Microsoft-specific (and only available in IE 10+), it will only be parsed in Internet Explorer 10 and greater.*/
@media all and (-ms-high-contrast: none),
(-ms-high-contrast: active) {
  /* IE10+ CSS styles go here */
  // .zonalevents-ie-message {
  //     display: block !important;
  // }
}

/* Internet Explorer 9 and below */
html.ie8,
html.ie9 {
  .zonalevents-widget {
    display: none !important;
  }

  .zonalevents-ie-message {
    display: block !important;
  }
}
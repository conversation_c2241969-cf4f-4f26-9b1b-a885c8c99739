html {
    position: relative;
    overflow-x: hidden;
    // DO NOT set above on body element,
    // this will destroy horizontal scrolling hijacking we use for gallery-scroll-2 component
}

body {
    min-height: 100vh;
    padding-top: 2rem !important;
    font-family: $font-family-sans-serif;

    @include media-breakpoint-up(lg) {
        padding-top: 4rem !important;
    }
}

.container {
    padding-left: $spacer * 2;
    padding-right: $spacer * 2;

    @include media-breakpoint-up(md) {
        padding-left: $spacer * 5;
        padding-right: $spacer * 5;
    }

     @include media-breakpoint-up(md) {
        padding-right: calc(var(--bs-gutter-x) * .5);
        padding-left: calc(var(--bs-gutter-x) * .5);
    }
}

.wp-block-cover {
    .container {
        padding-right: calc(var(--bs-gutter-x) * .5);
        padding-left: calc(var(--bs-gutter-x) * .5);
    }
}


.container-fluid {
    padding-left: $spacer * 2;
    padding-right: $spacer * 2;
}

.object-fit-cover {
    width: 100% !important;
    max-width: unset !important;
    height: 100% !important;
    max-height: unset !important;
    object-fit: cover !important;
}

.force-mwidth {
    max-width: 216px;
    margin: 0 auto;
}

.edge-2-edge {
    // force an element to be displayed edge to edge of the viewport
    width: 100vw !important;
    transform: translateX(-50%) !important;
    margin-left: 50% !important;
    max-width: unset !important;
}

@each $breakpoint in map-keys($grid-breakpoints) {
    @include media-breakpoint-up($breakpoint) {
        $infix: breakpoint-infix($breakpoint, $grid-breakpoints);
        .edge-2-edge#{$infix}-up {
            // force an element to be displayed edge to edge of the viewport
            width: 100vw !important;
            transform: translateX(-50%) !important;
            margin-left: 50% !important;
        }
    }
}

@each $breakpoint in map-keys($grid-breakpoints) {
    @include media-breakpoint-down($breakpoint) {
        $infix: breakpoint-infix($breakpoint, $grid-breakpoints);
        .edge-2-edge#{$infix}-down {
            // force an element to be displayed edge to edge of the viewport
            width: 100vw !important;
            transform: translateX(-50%) !important;
            margin-left: 50% !important;
        }
    }
}

.container,
.row {
    @include media-breakpoint-down(md) {
        // --bs-gutter-x: (30 / 16) * 2 * 1rem;
      }
}
.row.g-0 {
    --bs-gutter-x: 0;
}

.wp-block-image {
    align-items: start;
}

// center something horizontally
%center-x {
    display: block !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
}

// center something vertically
%center-y {
    display: block !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
}

// center something vertically
%center-xy {
    display: block !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
}

// Responsive ratios
// up ...
@each $breakpoint in map-keys($grid-breakpoints) {
    @include media-breakpoint-up($breakpoint) {
        @each $key, $ratio in $aspect-ratios {
            .ratio-#{$breakpoint}-#{$key} {
                --#{$prefix}aspect-ratio: #{$ratio};
            }
        }
    }
}
// ... down
@each $breakpoint in map-keys($grid-breakpoints) {
    @include media-breakpoint-down($breakpoint) {
        @each $key, $ratio in $aspect-ratios {
            .ratio-#{$breakpoint}-#{$key} {
                --#{$prefix}aspect-ratio: #{$ratio};
            }
        }
    }
}

.ratio {
    overflow: hidden;
}

.ratio > img:not(.image-cover) {
    // TODO: ratios stretching needs looking at on mobile
    object-fit: cover;

    display: block !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    max-width: none;
    min-height: 100%;
    min-width: 100%;
}

// sleeping woody
.video-dog {
    margin-left: auto;
    max-width: 375px;
    width: 100%;
}

.overflowing-wrap {
    overflow-x: auto;
    white-space: nowrap;
    cursor: grab;

    figure {
        width: 85%;
        display: inline-block;

        @include media-breakpoint-up(sm) {
            width: 37%;
        }

        @include media-breakpoint-up(md) {
            width: 38%;
        }

        @include media-breakpoint-up(xl) {
            width: 21%;
        }
    }
}

.inset-0 {
    inset: 0 0 0 0;
}

// ===== Clean Layout (no header, no footer)
body.layout-clean {
    .banner,
    .master-footer,
    .mobile-nav {
        display: none !important;
    }
    main {
        padding-top: 0 !important;
    }
}

// slick carousel, remove extra spacing
.slick-slide {
    line-height: 1;
}

.maxw-375 {
    max-width: 375px !important;
    margin-left: auto;
    margin-right: auto;
}

// patterns

// TODO: get better assets and use repetition

.pattern {
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
}

.inview .pattern__dashes-green {
    background-image: url("/images/pattern-test.png");
    background-size: cover;
}

.tripple-border {
    position: relative;
    border: 1px solid $green;

    &::before,
    &::after {
        content: '';
        display: block;
        position: absolute;
        inset: 2px;
        border: 1px solid $green;
        z-index: -1;
    }

    &::after {
        inset: 5px;
        border: 1px solid $green;
        z-index: -2;
    }

    &.rounded-circle {
        padding: $spacer*4;

        &::before,
        &::after {
            border-radius: 50%;
        }

        text-align: center;

        * {
            text-align: inherit;
        }
    }

    &.wp-block-image {
        padding: 6px;
    }
}

.tripple-border__round {
    position: relative;

    &::after {
        content: '';
        display: block;
        position: absolute;
        z-index: -1;
        inset: $spacer;
        border: 1px solid $green;
        border-radius: 100%;
        box-shadow: inset 0 0 0 2px $warm, inset 0 0 0 3px $green, inset 0 0 0 5px $warm, inset 0 0 0 6px $green;
    }
}

input:not([type="button"]):not([type="submit"]):not([type="checkbox"]),
textarea,
select,
option {
    background-color: $warm;
}

button {
  color: $green;
}
select {
  color: $green;
  text-align: center;

  option {
    text-align: center;
  }
}

.wpcf7-form {
    // generic contact form 7

    > p {
        // wpcf7 adds paragraphs, we might as well use them
        margin: 0 0 ($spacer * 2);
        justify-content: center;

        @include media-breakpoint-up(lg) {
            justify-content: start;
        }
    }

    .js-select-replace {
        margin-bottom: $spacer * 2;
    }

    br {
        // wpcf7 adds breaks, we don't want them
        display: none !important;
    }
}

.form-label {
    padding: 0 $spacer/2;
    font-family: $font-family-sans-serif;
    width: 100%;
    font-size: 1rem;
}

.form-control {
    border-radius: 0;
    padding: 7px calc($spacer/2);
    line-height: 1;
    text-align: center;
    font-family: $font-family-btn;
    letter-spacing: 1px;
    font-size: 10px;
    text-transform: uppercase;

    &:focus {
        box-shadow: none;
        outline: $focus-outline;
    }
}

.icon-dropdown-chevron {
    width: 20px;
    height: auto;
}

.form-check-input {
    margin-top: 0;

  &:focus {
    outline: $focus-outline;
  }
}

.checkbox-input-magic {
    // checkboxes from wpcf7 are not cool

    p {
        // once again
        display: flex;
        flex-direction: row;

        label {
            margin-left: $spacer * 1;
            font-size: $font-size-sm;
        }
    }
}

.error-tip {
    color: $red;
    font-size: 10px;
    letter-spacing: 1px;
    display: none;
    text-align: center;
    padding-top: $spacer/2;
    text-transform: uppercase;
    font-family: $font-family-subheading;
    font-weight: 400;
}

.form-row.error .error-tip {
display: block;
}

.wpcf7-not-valid-tip {
    // validation error
    color: $danger;
    margin: ($spacer * 1.5) 0 0 ($spacer * 1.5);
    display: block;
}

// ===== React Select =====
.js-select-replace {
    p {
        margin-bottom: 0;
    }
    select {
        @extend .visually-hidden;
    }
}
body {
    .react-select-container {
        font-family: $font-family-btn;
        font-size: 10px;
        text-transform: uppercase;
        letter-spacing: 1px;

        &.react-select--is-disabled {
            opacity: 0.65;
        }
    }

    .react-select__input-container {
        padding: 0;
        margin: 0;
        line-height: 1;
    }

    .react-select__control {
        background-color: $warm;
        border-color: $dark;
        padding: 4px;
        border-radius: 0;
        min-height: unset;

        &:focus,
        &.react-select__control--is-focused {
            border-color: $input-focus-border-color;
            outline: 0;
            box-shadow: $input-focus-box-shadow;
        }
    }
    .react-select__value-container {
        padding: 0 4px 0 20px;
        text-align: center;
    }

    .react-select__single-value {
        margin: 0;
        color: $green;
    }

    .react-select__indicators {

    }
    .react-select__menu {
        background-color: lighten($warm, 2%);
        color: $green !important;
        box-shadow: none;
        border-radius: 0;
        border: 1px solid;
    }
    .react-select__menu-list {
        padding: 0;
    }

    .react-select__option {
        text-align: center;
        padding-left: $spacer/2;

        &:active,
        &.react-select__option--is-selected {
            background-color: $red;
            color: $warm
        }
    }



    .react-select__indicator {
        padding: 0;
    }

    .react-select__indicator-separator {
        display: none;
    }
}

.form-row {
    margin-bottom: $spacer*2;

    .row {
    --bs-gutter-x: 1rem;

        .react-select__control {
            padding-left: 0.4375rem;
        }
    }
}

// --- Custom select madness
.dropdown-indicator {
    color: $red;
    position: relative;
}

.react-select-container {
    // inline styles are strong hence why we wrap it

    .react-select__indicator-separator {
        background-color: $dark;
    }

    .react-select__control:hover {
        border-color: $dark;
    }

    .react-select__control:focus {
        border-color: transparent;
    }
}

.react-select__option.react-select__option--is-selected {
    background-color: $green;
}

.react-select__option.react-select__option--is-focused {
    background-color: darken($warm, 5%);
    color: $green;
}

.react-select__option.react-select__option--is-selected {
    color: linen;
}

.react-select__indicator.react-select__dropdown-indicator {
    color: $dark;
}

body {
    // React Calendar UI
    .react-calendar {
        width: auto;
        font-family: $font-family-btn;
        font-size: 10px;
        letter-spacing: 1px;
        text-transform: uppercase !important;

        button {
            font-family: $font-family-btn;
            font-size: 10px;
            letter-spacing: 1px;
            text-transform: uppercase !important;
        }

        abbr {
            text-decoration: none;
        }

        .react-calendar__navigation {
            height: auto;
            flex-wrap: wrap;
            border-bottom: 1px solid;
            border-bottom-left-radius: 0;
            border-bottom-right-radius: 0;
            margin-bottom: 0.5rem;
            position: relative;

            &::before {
                content: "";
                position: absolute;
                inset: 0 0 auto;
                z-index: 1;
                height: 35px;
            }

            > *:last-child {
                border-right: none;
            }
        }

        .react-calendar__navigation__label {
            width: 100%;
            display: block;
            order: 0;
            border-bottom: 1px solid;
            line-height: 2rem;
            font-weight: 600;
        }

        .react-calendar__navigation__arrow {
            width: 25%;
            white-space: nowrap;
            line-height: 2;
            font-size: 10px;
            line-height: 20px;
        }

        .react-calendar__navigation__next-button,
        .react-calendar__navigation__prev-button,
        .react-calendar__navigation__next2-button,
        .react-calendar__navigation__prev2-button {
            order: 1;
            border-right: 1px solid $green;
        }

        .react-calendar__navigation__arrow {
            flex-grow: 1;
        }
    }

    // hack for forcing dropdown menus to be the width of ther dropdown triggers

    .dropdown-menu-full {
        left: 0 !important;
        right: 0 !important;

        @include media-breakpoint-up(md) {
            right: auto !important;
        }
    }
}
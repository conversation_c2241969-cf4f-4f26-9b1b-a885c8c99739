.dropdown-menu {
    line-height: 1.625;
    margin-bottom: $spacer * 2;

    &.show {
        visibility: visible;
        opacity: 1;
    }

    li {
        padding: var(--bs-dropdown-item-padding-y) var(--bs-dropdown-item-padding-x);
        margin-bottom: 0 !important;

        &:before {
            display: none;
        }
    }

    .dropdown-item {
        width: auto;
        padding: 0;
        display: block;
        color: $celadon;
        transition: color 0.3s ease-in-out;
    }
}

.dropdown-toggle {
    position: relative;
    justify-content: center;

    &::after {
        display: none;
    }

    .icon-arrow-down {
        position: absolute;
        right: 0.5rem;
        top: 50%;
        transform:translateY(-50%);
      }

    &.btn-redlight + .dropdown-menu {
        background-color: $red;

        .dropdown-item {
            &:hover {
                color: $dark;
            }
        }
    }

    &.btn-redmid + .dropdown-menu {
        background-color: $red;

        .dropdown-item {
            &:hover {
                color: $dark;
            }
        }
    }


}

.dropdown-header {
    color: inherit;
    font-style: italic;
}

.dropdown-divider {
    width: 100%;
}

.guestline-dropdown,
.menus-dropdown {
    .dropdown-toggle {
        position: relative;
        max-width: none;

        [class^="icon-"] {
            position: absolute !important;
            top: 50%;
            right: 0.5rem !important;
        }
    }
}

.offcanvas {
    .guestline-dropdown {
        width: 100%;

        .dropdown-menu {
            text-align: center;
        }
    }
}

.banner {
    .dropdown-menu:not(.guestline-dropdown-menu) {
        text-align: center;

        &.show {
            top: 100%;
            opacity: 1;
            transition: background-color 0.3s ease-in-out, top 0.3s ease-in-out, opacity 0.3s ease-in-out, visibility 0s ease-in-out 0.3s;

            @include media-breakpoint-up(lg) {
                top: -250px;
                opacity: 1;
            }

            &.show {
                top: 100%;
                opacity: 1;
                transition: background-color 0.3s ease-in-out, top 0.3s ease-in-out, opacity 0.3s ease-in-out 0.1s, visibility 0s ease-in-out;
            }
        }
    }
    .guestline-dropdown {

        @include media-breakpoint-up(lg) {

            .dropdown-toggle {
                [class^=icon-] {
                    right: 0.25rem !important;
                    width: 1rem;
                    height: 1rem;
                }
            }
        }
    }

    .guestline-dropdown-menu {
        margin-top: $spacer;
        min-width: unset;
    }
}

.has-desc {
    padding: var(--bs-dropdown-item-padding-y) var(--bs-dropdown-item-padding-x);

    .dropdown-item {
        white-space: initial;
    }
}

.nav-item-desc {
    white-space: initial;
}

.menus-dropdown-menu {
    inset: 0 auto auto auto !important;
}

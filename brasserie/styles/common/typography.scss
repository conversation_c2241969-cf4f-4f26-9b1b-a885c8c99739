* {
  // fix font rendering issues
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
}

p,
ul,
ol,
input,
textarea,
select {
  //
}

dl {
  margin-top: 0;
  margin-bottom: 20px;
}

dt {
  font-weight: normal;
  font-style: italic;
  font-size: 0.85rem;
  margin-bottom: 10px;
  padding-left: 10px;
  line-height: 1.5;

  &:before {
    content: "- ";
    position: relative;
    margin-left: -10px;
  }
}

h1,
.h1,
h2,
.h2,
h3,
.h3,
h4,
.h4,
h5,
.h5,
h6,
.h6 {
  margin-bottom: $spacer * 2;
  margin-top: $spacer * 5;
  font-family: $font-family-heading;
  line-height: 1.25;
  text-align: center;

  strong {
    color: $green;
  }
}

h1,
.h1 {
  font-family: $font-family-heading;
  text-transform: uppercase;
  font-size: 19px;

  strong {
    font-size: inherit;
    font-family: $font-family-subheading;
    font-weight: 400;
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  @include media-breakpoint-up(sm) {
    font-size: 21px;
  }

  @include media-breakpoint-up(md) {
    font-size: 23px;
  }

  @include media-breakpoint-up(lg) {
    font-size: 25px;
  }

  @include media-breakpoint-up(xl) {
    font-size: 27px;
  }
}

h2,
.h2 {
    font-family: $font-family-heading;
    text-transform: uppercase;
    font-size: 16px;

  strong {
    font-size: inherit;
    font-family: $font-family-subheading;
    font-weight: 400;
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  @include media-breakpoint-up(sm) {
    font-size: 18px;
  }

  @include media-breakpoint-up(md) {
    font-size: 20px;
  }

  @include media-breakpoint-up(lg) {
    font-size: 22px;
  }

  @include media-breakpoint-up(xl) {
    font-size: 24px;
  }
}

h3,
.h3 {
  font-size: 20px;
  font-family: $font-family-subheading;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 1px;

  strong {
    font-size: 0.75em;
  }

  @include media-breakpoint-up(sm) {
    font-size: 21px;
  }

  @include media-breakpoint-up(md) {
    font-size: 25px;
  }

  @include media-breakpoint-up(lg) {
    font-size: 27px;
  }

  @include media-breakpoint-up(xl) {
    font-size: 29px;
  }
}

h4,
.h4 {
  font-size: 18px;
  font-family: $font-family-subheading;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 1px;

  strong {
    font-size: 0.75em;
  }

    @include media-breakpoint-up(sm) {
    font-size: 20px;
  }

  @include media-breakpoint-up(md) {
    font-size: 22px;
  }

  @include media-breakpoint-up(lg) {
    font-size: 24px;
  }

  @include media-breakpoint-up(xl) {
    font-size: 26px;
  }
}

h5,
.h5 {
  font-family: $font-family-sans-serif;
  font-size: 20px;
  line-height: 1.2;
  text-transform: none;
  font-weight: 700;

  strong {
    font-size: 0.8em;
    font-weight: 600;
  }

  @include media-breakpoint-up(sm) {
    font-size: 22px;
  }

  @include media-breakpoint-up(md) {
    font-size: 24px;
  }

  @include media-breakpoint-up(lg) {
    font-size: 26px;
  }

  @include media-breakpoint-up(xl) {
    font-size: 28px;
  }
}

h6,
.h6 {
  font-family: $font-family-sans-serif;
  font-size: 18px;
  line-height: 1.2;
  text-transform: none;
  font-weight: 700;

  strong {
    font-size: 0.8em;
    font-weight: 600;
  }

  @include media-breakpoint-up(sm) {
    font-size: 20px;
  }

  @include media-breakpoint-up(md) {
    font-size: 22px;
  }

  @include media-breakpoint-up(lg) {
    font-size: 24px;
  }

  @include media-breakpoint-up(xl) {
    font-size: 26px;
  }
}

p {
  text-align: justify;
}

small,
.small {
  font-size: $font-size-sm;
}

.text-justify {
    text-align: justify !important;
}

b,
strong,
.lead {
  font-weight: $font-weight-bold;
}

.lead {
  font-size: $lead-font-size;
}

mark,
.mark {
  padding: 0;
}

main {
  font-size: 17px;

  ul {
    text-align: justify;
  }

  ol {
    list-style: decimal;
    text-align: justify;
  }
}

%hr,
hr,
.wp-block-separator {
    position: relative;
    opacity: 1;
    margin: 25px 0;
    border: none;
    border-top: 1px solid $green;
    border-bottom: 1px solid $green;
    padding: 0 !important;

  &::before {
    content: "";
    display: block;
    height: 1px;
    background-color: $green;
    margin: 2px auto;
  }

  &.show {
    width: 100%;
  }

  &.white {
    border-color: $warm;
    &::before {
      background-color: $warm;
    }
  }

  &.wide,
  &.is-style-wide {
    width: 100vw;
    left: 50%;
    transform: translateX(-50%);
  }

  &.mobile-wide {
    @include media-breakpoint-down(md) {
      width: 100vw;
      left: 50%;
      transform: translateX(-50%);
    }
  }
}

hr,
.wp-block-separator {
  + h1, + .h1,
  + h2, + .h2,
  + h3, + .h3,
  + h4, + .h4,
  + h5, + .h5,
  + h6, + .h6 {
    margin-top: 0;
  }
}

// .animated {
//   hr {
//     width: 0
//   }
// }

// .inview hr {
//   animation-name: draw-hr;
//   animation-duration: 0.8s;
//   animation-iteration-count: 1;
//   animation-delay: 0.5s;
//   animation-fill-mode: forwards;
//   animation-timing-function: cubic-bezier(0.32, 0, 0.67, 0);
// }

@keyframes draw-hr {
  from {
    width: 0;
  }

  to {
    width: 100%;
  }
}

.blockquote {
  position: relative;
  font-family: $headings-font-family;
  font-size: (25 / 16) * 1rem;
  line-height: (30 / 16) * 1rem;
  margin: 0;

  &::before,
  &::after {
    display: block;
    font-size: (35 / 16) * 1rem;
    line-height: (40 / 16) * 1rem;
  }

  &::before {

    content: "\201C";
  }

  &::after {
    content: "\201D";
    margin-top: $spacer * 1.5;

    @include media-breakpoint-up(lg) {
      text-align: right;
    }
  }

  >*:first-child {
    margin-top: 0 !important;
    // remove top margin from the first child of the blockquote
  }

  >*:last-child {
    margin-bottom: 0 !important;
    // remove bottom margin from the first child of the blockquote
  }
}

.blockquote-footer {
  margin: 0;
  font-size: 1rem;
  color: currentColor;

  &::before {
    display: none;
  }
}

figure {
  margin: unset;
}

.entry-content {
  a {
    text-decoration-style: solid;
    text-decoration-thickness: 1px;
    text-underline-offset: 2px;
  }
}

.loader {
  border-left-color: $green !important;
  border-bottom-color: transparentize($green, 0.8) !important;
  border-right-color: transparentize($green, 0.8) !important;
  border-top-color: transparentize($green, 0.8) !important;
}

$burger-easing: cubic-bezier(0.65, 0, 0.35, 1);

.burger {
    position: relative;
    cursor: pointer;
    overflow: hidden;
    border: none;
    color: $warm;

    .icon {
        position: relative;
        width: 15px;
        height: 15px;

        .line {
            position: absolute;
            left: 0;
            background-color: currentColor;
            height: 2px;
            width: 100%;
            pointer-events: none;
        }

        .line--1 {
            top: 0;
            transition: all 0.3s ease-in-out;
            transform-origin: left center;
        }

        .line--2 {
            top: 0;
            bottom: 0;
            margin: auto;
            right: 0;
            transition: right 0.3s ease-in-out, opacity 0.3s ease-in-out, transform 0s ease-in-out;
        }

        .line--3 {
            bottom: 0;
            transition: all 0.3s ease-in-out;
            transform-origin: left center;
        }

        @include media-breakpoint-up(lg) {
            height: 15px;
        }
    }

    &.open {
        .line {
            box-shadow: 0 0 0 1px $warm;
            width: calc(100% + 4px);

        }

        .line--1 {
            transform: rotate(45deg);
                        left: 1px;
        }

        .line--2 {
            opacity: 0;
            right: $spacer * -3;
            transform: translate3d(($spacer * -3), 0, 0);
            transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out, right 0s ease-in-out 0.2s;
        }

        .line--3 {
            transform: rotate(-45deg);
                        left: 1px;
        }
    }
}

.banner {
    z-index: $zindex-banner;
    transition: all 0.3s ease-in-out;

    background-color: $red;

    &.nav-transparent {
        background-color: transparent;
    }

    @include media-breakpoint-up(md) {
        padding: 13px 0;
    }

    .nav-link {
        font-family: $font-family-heading;
        text-transform: uppercase;
        font-size: 13px;
        line-height: 1;
        letter-spacing: 1px;
    }

    .btn {
        font-family: $font-family-heading;
        text-transform: uppercase;
        font-size: 13px;
    }
}

.navbar-brand {
    // used for changing the color of the SVG logo on hover
    transition: all .25s cubic-bezier(.17,.67,.51,1.67);
	// it needs to be kicked up to work with our desktop dropdown backdrop
	z-index: $zindex-brand;

    &:hover {
        transform: scale(1.05);
    }

    @media screen and (min-width: 1090px) {
        margin-bottom: 0 !important;
    }
}

.nav-item {
    z-index: 1000; // necessary for the banner, possibly other places
}

.navlink {
    // dropdown icons size
    [class^="icon-"] {
        width: $spacer * 2;
        height: $spacer * 2;
    }
}

.navbar-toggler {
    left: 0;
    top: calc(46 / 16 * 1rem);
    border: none;

    &:focus {
        box-shadow: none;
    }
}

.nav-actions {
    position: absolute;
    right: $spacer;
    top: $spacer;

    @include media-breakpoint-up(md) {
        position: initial;
    }

    .btn {
        max-width: unset;
        width: auto;
        padding: $spacer/2 $spacer;
    }
}

// offcanvas styles
.offcanvas {
    --bs-offcanvas-width: 50%;

    @include media-breakpoint-up(sm) {
        --bs-offcanvas-width: auto;
        left: auto;
    }

    .container {
        padding-left: 0;
        padding-right: 0;
    }

    .footer-brand {
        margin-left: auto;
        width: 100%;

        .wordmark {
            width: 100%;
            height: auto;
        }
    }

    .dropdown-menu {
        display: none !important;
    }

    .st-socials {
        margin-left: 194px !important;
    }

    &:not(.show) {
        .offcanvas-submenu {
            transform: none;
        }
    }
}

.offcanvas-submenu {
    position: fixed;
    z-index: -1;
    top: 0;
    left: 50%;
    margin: 0;
    height: 100vh;
    border: none;
    border-radius: 0;
    width: 50%;
    padding: 55px 20px 5rem;
    overflow-y: auto;
    background-color: $warm;
    transform: none;
    will-change: transform;
    transition: $transition-base;

    @include media-breakpoint-up(sm) {
        width: auto;
        left: auto;
        padding-top: 59px;
        padding-left: $spacer*3;
        padding-right: $spacer*3;
    }

    @include media-breakpoint-up(lg) {
        padding-top: 59px;
        padding-left: $spacer*5;
        padding-right: $spacer*5;
    }

    &.show {
        transform: translateX(-100%);
    }

    ul {
        margin: 0;
    }

    li {
        font-size: 16px;
        color: $green;
        padding: 0.1rem 0;
        font-family: $font-family-sans-serif;
        margin-left: 1rem;

        @include media-breakpoint-up(sm) {
            font-size: 19px;
        }


        a {
            font-family: $font-family-sans-serif;
            color: inherit;

            &:hover,
            &:focus,
            &:active,
            &.current-menu-item {
                text-decoration: underline;
            }
        }

        &.btn {
            margin-left: 0;
            padding: 0;
            width: min-content;

            @include media-breakpoint-up(sm) {
                font-size: 23px;
            }

            a {
                font-family: $font-family-heading !important;
                white-space: normal;
            }

            &.btn-secondary {
                white-space: wrap;
                font-size: 12px;
                border: none;
                background-color: transparent;
                text-align: left;

                a {
                    letter-spacing: 1px;
                    color: $warm;
                    background-color: $red;
                    border: 2px solid $red;
                    padding: 4px;

                &:hover {
                    text-decoration: unset;
                    color: $red;
                    background-color: $warm;
                    }
                }
            }
        }

        &.separator {
            margin-top: 1rem;
            letter-spacing: 1px;
            margin-left: 0;
            font-size: 14px;

            a {
                display: none;
            }

            span {
                font-weight: 500;
                text-transform: uppercase;
                font-family: $font-family-subheading;
            }
        }
    }
}

.offcanvas-body {
    padding-top: 110px;
    padding-left: 20px;
    padding-right: 20px;
    background-color: $green;
    // header height + gap

    @include media-breakpoint-up(sm) {
        padding-left: $spacer*3;
        padding-right: $spacer*3;
    }

    @include media-breakpoint-up(lg) {
        padding-left: $spacer*5;
        padding-right: $spacer*5;
    }


    .navbar-toggler {
        position: absolute;
        top: 80px;
        left: 12px;

        @include media-breakpoint-up(sm) {
            left: 22px;
        }

        @include media-breakpoint-up(lg) {
            left: 43px;
        }
    }

    .btn-secondary {
        font-family: $font-family-heading;
        font-size: 10px;
        text-align: center;

        @include media-breakpoint-up(sm) {
            font-size: 23px;
        }
    }

    .navbar-nav {
        font-family: $font-family-heading;
        text-transform: uppercase;

        .nav-item:not(.btn) {
            > .nav-link {
                font-size: 12px;
                letter-spacing: 1px;
                text-align: left;
                align-items: start !important;

                    @include media-breakpoint-up(sm) {
                        font-size: 20px;
                    }

                    @include media-breakpoint-up(md) {
                        font-size: 23px;
                    }

                span {
                    display: inline-block;
                    position: relative;

                    &::after {
                        content: '';
                        position: absolute;
                        bottom: -6px;
                        left: 0;
                        width: 50%;
                        height: 1px;
                        background-color: $celadon;
                        box-shadow: 0 3px 3px 0 $celadon, 0 -3px 3px 0 $celadon;
                        opacity: 0;
                        visibility: hidden;
                        transition: $transition-base;
                    }
                }

                svg {
                    color: currentColor;
                    position: absolute;
                    right: 0;
                }

                &:hover,
                &:focus,
                &:active,
                &.current-menu-item {
                    color: inherit;

                    span {
                        &::after {
                            opacity: 1;
                            visibility: visible;
                            width: 100%;
                            box-shadow: 0 3px 0 0 $celadon, 0 -3px 0 0 $celadon;
                        }
                    }
                }
            }
        }

        > .nav-item {
            &.btn {
                a {
                    padding: 0 !important;
                    font-size: 25px;
                }
            }
        }
    }

    .footer-details {
        [class*="col-"] {
            width: 100%;
            text-align: center;
        }

        [class*="offset-"] {
            margin-left: 0;
        }
    }

    .copyright {
        text-align: center !important;
        margin-top: 3.125rem !important;
    }

    .saint {
        display: block;
    }
}

// === burger animation
.offcanvas {
    transition: transform 0.6s $burger-easing;
    height: calc(100vh - $mobile-nav-height);

    .dropdown-toggle {
        justify-content: start;
    }

    // default, critical for transitions
    .nav-item {
        opacity: 0;
        transition: all 0.3s $burger-easing 0.3s;
        transform: translateY($spacer * -1);

         @for $i from 1 through 12 {
            &:nth-child(#{$i}) {
                transition-delay: 0.3s + ($i * 0.05s);
            }
        }
    }

    @include media-breakpoint-up(lg) {
        height: 100vh;
    }
}

.offcanvas.show:not(.hiding) {
    // when the menu is visible
    .nav-item {
        opacity: 1;
        transform: translateY(0);
    }
}

// ===== Mobile nav
.mobile-nav {
    position: fixed;
    z-index: 1060;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    // border-top: 1px solid $warm;
    height: $mobile-nav-height;

    .nav-list {
        margin: 0;
        display: flex;
        flex-direction: row;
        justify-content: space-around;
    }

    .nav-item {
        // border-right: 1px solid $warm;
        text-align: center;
        color: $warm;
        font-size: 12px;
        width: auto;
        flex-grow: 1;
        text-transform: uppercase;
        display: flex;
        align-items: center;
        justify-content: center;

        &:last-of-type {
            border: none;
        }
    }

    .nav-link {
        padding: 0.3rem 0.5rem;
        width: 100%;
        font-size: inherit;
        line-height: inherit;
        border-radius: 0 !important;
        max-width: none;

        &:hover,
        &:focus,
        &:active,
        &.current-menu-item {
            background: $warm;
            color: $dark;
        }
    }

    .guestline-dropdown-menu {
        margin-bottom: $spacer/2;
        text-align: center;
    }
}

.header-buttons {
    width: 100%;
    order: -1;

    @include media-breakpoint-up(lg) {
        width: 120px;
        order: initial;

        &.has-dropdown {
            min-width: 140px;
        }
    }
}
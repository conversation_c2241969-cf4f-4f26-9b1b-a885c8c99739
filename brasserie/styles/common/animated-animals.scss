.animated-thing {
  display: block;
  width: 100%;
  position: relative;
  margin-left: auto;
  margin-right: auto;
  background-repeat: no-repeat;
  background-position: right center;

  @include media-breakpoint-up(lg) {
    margin-right: 0;
  }

  svg {
    position: absolute;
    width: 600px;

    text {
      font-size: 2.5rem;
      font-family: $headings-font-family;
    }

    // border: 1px solid red;
    // path {
    //   stroke-width: 1px;
    //   stroke: black;
    // }
  }

  &.bird {
    display: block;
    height: 100px;
    position: relative;
    margin-bottom: $spacer * 5;
    margin-right: -1.5rem;
    width: calc(100% + 1.5rem);
    background-image: url("/images/placeholder-bird-clean.png");
    background-repeat: no-repeat;
    background-position: right center;
    background-size: auto 100px;

    svg {
      right: 90px;
      top: 0;
    }
  }

  &.dog {
    height: 250px;
    width: calc(100% + 170px);
    margin-right: -170px;
    max-width: none;
    background-image: url("/images/placeholder-dog-clean.png");
    background-size: auto 250px;

    svg {
      right: 250px;
      top: 35px;
    }
  }

  &.dog-reverted {
    height: 250px;
    width: calc(100% + 170px);
    margin-left: -170px;
    margin-right: auto;
    max-width: none;
    background-image: url("/images/placeholder-dog-reverted-clean.png");
    background-position: left center;
    background-size: auto 250px;

    svg {
      left: 250px;
      top: 35px;

      path {
        transform: scale(1, -1) translate(0, -100%);
      }
    }

  }

  &.bee {
    display: none;
    height: 80px;
    width: 100%;
    margin-right: auto;
    max-width: none;
    background-image: url("/images/bee.png");
    background-position: left center;
    background-size: auto 80px;
    padding: 80px 0;

    svg {
      left: 70px;
      top: 35px;

      text {
        fill: $white;
      }

      path {
        transform: scale(1, -1) translate(0, -100%);
      }
    }

  }

  @include media-breakpoint-up(md) {
    svg {
      width: 900px;
      text{
        font-size: 1.25rem;
      }
    }
    &.bird {
      margin-right: 0;
      svg {
        top: -30px;
      }
    }
    &.dog {
      svg {
        top: 15px;
      }
    }
    &.dog-reverted {
      svg {
        top: 15px;
      }
    }
    &.bee {
      svg {
        top: 15px;
      }
    }
  }

  @include media-breakpoint-up(lg) {
    svg {
      position: absolute;
      width: 1200px;
      text {
        font-size: 1rem;
      }
    }
    &.bird {
      svg {
        top: -4rem;
      }
    }
    &.dog {
      svg {
        top: 0;
      }
    }
    &.dog-reverted {
      svg {
        top: 0;
      }
    }
    &.bee {
      display: block;
      svg {
        top: 0;
      }
    }
    &.half-width {
      overflow: hidden;
    }
  }

  // === Extra class to make the path half width (when placed inside columns)
  &.half-width {
    @include media-breakpoint-up(lg) {
      svg {
        width: 900px;
        text {
          font-size: 1.25rem;
        }
      }
      &.bird {
        margin-bottom: 0;
        svg {
          top: -2rem;
        }
      }
      &.dog {
        svg {
          top: 25px;
        }
      }
      &.dog-reverted {
        svg {
          top: 25px;
        }
      }
      &.bee {
        svg {
          top: -5px;
        }
      }
    }
    @include media-breakpoint-up(xl) {
      svg {
        width: 600px;
        text {
          font-size: 2rem;
        }
      }
      &.bird {
        svg {
          top: 0;
        }
      }
      &.bee {
        svg {
          top: 25px;
        }
      }
    }
  }

}

// === Inside Featured Block
.block-featured.has-anim {
  @include media-breakpoint-up(lg) {
    &.has-bird {
      .featured-content {
        padding-bottom: 6rem;
      }
    }

    &.has-dog {
      .featured-content {
        padding-bottom: 12rem;
      }
    }

    .animated-thing {
      &.bird {
        position: absolute;
        right: 0;
        bottom: 20px;
        margin-right: 0;
        width: 50%;
      }

      &.dog {
        position: absolute;
        right: -250px;
        bottom: 20px;
        width: calc(50% + 250px);
        margin-right: 0;
      }
    }
  }
}

// video animals - scaling - we want the same line weight

.ratio-bee,
.ratio-bird {
  max-width: 375px;
}

.ratio-seed {
  max-width: 170px;
}

.ratio-squirrel {
  max-width: 300px;
}

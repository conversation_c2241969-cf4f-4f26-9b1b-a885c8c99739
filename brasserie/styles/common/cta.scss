.block-loyalty {
  hr {
    &.is-style-wide {
        @include media-breakpoint-up(md) {
          width: 100% !important;
          transform: none !important;
          margin: 0 !important;
        }
    }
  }
}

.cta-background,
.cta-img {
  max-width: 300px;
  margin: 0 auto;
  width: 100%;

  @include media-breakpoint-up(md) {
    max-width: 475px;
  }
}

.cta-background {
  position: relative;
  z-index: 1;
  background-color: $warm;
  padding: $spacer*2;

  &.footer-brand {
    position: static !important;
    transform: translatey(-50%);
    margin-bottom: $spacer*-7;
  }
}

.cta-img {
  border: $spacer solid $warm;
}

.cta-content {
  position: relative;
  z-index: 1;
  padding-bottom: $spacer*3;

  h2 {
    margin-top: $spacer*3;
  }

  figure img:only-child {
    margin: $spacer auto;
    max-width: 120px;
    display: block;
  }
}


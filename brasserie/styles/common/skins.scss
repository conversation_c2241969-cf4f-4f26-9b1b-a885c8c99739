// Plaster - default for all inns
body {
    --body-backround:            #{$warm};   // default body background colour
    --base-backround:            #{$warm};   // default navbar background colour
    --active-background:         #{$celadon};       // open navbar background colour
    --base-text:                 #{$dark};      // base text colour for the navbar
    --inverse-text:              #{$warm};     // inverted text colour for navbar in open state
    --link-text:                 #{$warm};   // dropdown links colour
    --current-text:              #{$dark};      // current nav item
    --hover-focus-active-text:   #{$green};      // link hover focus active colour
    --hover-focus-active-nav:    #{$warm};     // navbar hover focus active colour
}

// Summer
body.bg-summer {
    --body-backround:            #{$summer};    // default body background colour
    --base-backround:            #{$summer};    // default navbar background colour
    --active-background:         #{$celadon};       // open navbar background colour
    --base-text:                 #{$dark};      // base text colour for the navbar
    --inverse-text:              #{$warm};     // inverted text colour for navbar in open state
    --link-text:                 #{$warm};   // dropdown links colour
    --current-text:              #{$dark};      // current nav item
    --hover-focus-active-text:   #{$green};      // link hover focus active colour
    --hover-focus-active-nav:    #{$warm};     // navbar hover focus active colour
}

// Autumn
body.bg-autumn {
    --body-backround:            #{$autumn};    // default body background colour
    --base-backround:            #{$autumn};    // default navbar background colour
    --active-background:         #{$celadon};       // open navbar background colour
    --base-text:                 #{$dark};      // base text colour for the navbar
    --inverse-text:              #{$warm};     // inverted text colour for navbar in open state
    --link-text:                 #{$warm};   // dropdown links colour
    --current-text:              #{$dark};      // current nav item
    --hover-focus-active-text:   #{$green};      // link hover focus active colour
    --hover-focus-active-nav:    #{$warm};     // navbar hover focus active colour
}

// Winter
body.bg-winter {
    --body-backround:            #{$winter};    // default body background colour
    --base-backround:            #{$winter};    // default navbar background colour
    --active-background:         #{$celadon};       // open navbar background colour
    --base-text:                 #{$dark};      // base text colour for the navbar
    --inverse-text:              #{$warm};     // inverted text colour for navbar in open state
    --link-text:                 #{$warm};   // dropdown links colour
    --current-text:              #{$dark};      // current nav item
    --hover-focus-active-text:   #{$green};      // link hover focus active colour
    --hover-focus-active-nav:    #{$warm};     // navbar hover focus active colour
}

// Spring
body.bg-spring {
    --body-backround:            #{$spring};    // default body background colour
    --base-backround:            #{$spring};    // default navbar background colour
    --active-background:         #{$celadon};       // open navbar background colour
    --base-text:                 #{$dark};      // base text colour for the navbar
    --inverse-text:              #{$warm};     // inverted text colour for navbar in open state
    --link-text:                 #{$warm};   // dropdown links colour
    --current-text:              #{$dark};      // current nav item
    --hover-focus-active-text:   #{$green};      // link hover focus active colour
    --hover-focus-active-nav:    #{$warm};     // navbar hover focus active colour
}

// transparent
body.nav-transparent {
    --body-backround:            #{$warm};   // default navbar background colour
    --base-backround:            #{transparent};   // default navbar background colour
    --active-background:         #{transparent};       // open navbar background colour
    --base-text:                 #{$warm};      // base text colour for the navbar
    --inverse-text:              #{$warm};     // inverted text colour for navbar in open state
    --link-text:                 #{$warm};   // dropdown links colour
    --current-text:              #{$dark};      // current nav item
    --hover-focus-active-text:   #{$green};      // link hover focus active colour
    --hover-focus-active-nav:    #{$warm};     // navbar hover focus active colour
}

// All colour schemes
body {
    background-color:  var(--body-backround);

    @include media-breakpoint-up(lg) {
        &.nav-shrink {
        .nav-link {
                // color: var(--base-text);
            }
        }
    }

    .banner {
        // background-color:  var(--base-backround);

        &.state-open{
            background-color: var(--active-background);
            color: var(--inverse-text);

            .nav-link {
                &:hover,
                &:focus {
                    color: var(--link-text);
                }
            }
        }

        &.state-closed{
            background-color:  var(--base-backround);
            color: var(--base-text);

            .nav-link {
                &:hover,
                &:focus {
                    color: var(--hover-focus-active-nav);
                }
            }
        }
    }
    .navbar-brand,
    .header-info {
        a  {
            color: var(--base-text);
        }
    }
    .navbar-brand:focus,
    .navbar-brand:hover {
        color: var(--hover-focus-active-nav);
    }

    .nav-link {
        // all links
        color: var(--body-backround);

        &:hover,
        &:focus {
            color: var(--link-text);
        }

        &.show {
            color: var(--base-text) !important;
        }

    }

    .navbar-nav .show > .nav-link {
        // first level links
        color: var(--link-text);

        &:hover,
        &:focus {
            color: var(--hover-focus-active-nav);
        }
    }

    .dropdown-item {
        // second level links
        color: var(--link-text);

        &:hover,
        &:focus {
            color: var(--hover-focus-active-text);
            background-color: transparent;
        }
    }

    .current-menu-item {
        color: var(--current-text);
    }

    .offcanvas {
        // mobile menu
        color: var(--inverse-text);
    }

    .form-check-input,
    .react-select__control,
    .form-control {
    }

    .react-calendar {
        border-radius: 0;
        border-color: currentColor;
    }

    .react-calendar__navigation {
        border-radius: 0.375rem;
        overflow: hidden;
    }

    .react-calendar__navigation button:enabled:hover,
    .react-calendar__navigation button:enabled:focus {
        background-color: darken($warm, 5%);
    }

    .react-calendar__tile {
        padding: 10px 0;
    }

    .react-calendar__tile:disabled,
    .react-calendar__navigation button:disabled {
        background-color: transparent;
        opacity: 33%;
    }

    .react-calendar__tile--now {
        background-color: lighten($red, 40%);
        color: $red;

        &:hover,
        &:focus {
            color: darken($red, 20%);
        }
    }

    .react-calendar__tile--active {
        background-color: $red;
        color: $warm;

        &:hover,
        &:focus {
            background-color: darken($red, 20%) !important;
        }
    }

    .react-calendar__tile:enabled {
        &:hover,
        &:focus {
            background-color: darken($warm, 5%) !important;
            color: $green;
        }
    }
}

// Transparent nav variant
body.nav-transparent {
    .banner {
        background: linear-gradient(180deg, rgba(33,37,41,1) 0%, rgba(33,37,41,0) 100%);
    }
}
.btn {
    // dangerous but true to design
    padding-left: $spacer;
    padding-right: $spacer;
    flex-grow: 0;
    flex-shrink: 0;
    border-width: 2px;
    text-transform: uppercase;
    line-height: 1;
    letter-spacing: 1px;
    font-family: $font-family-btn;
    text-align: center;
}

.btn[disabled] {
    background-color: $warm !important;
    border-color: $celadon !important;
    color: $celadon !important;
}

.btn-primary {
    color: $warm;

    &:hover {
        background-color: $warm;
        border-color: $green;
        color: $green;
    }

    &:focus,
    &.active {
        box-shadow: inset 0 0 0 1px $warm;
    }
}

.btn-red {
    color: $warm;
}

.btn-outline-primary {
    &:hover,
    &:focus,
    &.active {
        background-color: $green;
        color: $warm;
    }

    &:focus,
    &.active {
        box-shadow: inset 0 0 0 1px $warm;
    }
}

.btn-secondary {
    color: $warm;

    &:hover {
        background-color: $warm;
        color: $red;
    }

    &:focus,
    &.active {
        box-shadow: inset 0 0 0 1px $warm;
    }
}

.btn-outline-secondary {
    &:hover,
    &:focus,
    &.active {
        background-color: $red;
        color: $warm;
    }

    &:focus {
        box-shadow: inset 0 0 0 1px $warm;
    }
}

.btn-outline-warm {
    border-color: $warm;
    color: $warm;

    &:hover,
    &:focus,
    &.active {
        background-color: $red;
        color: $warm;
    }

    &.active {
        text-decoration: underline;
    }
}

.btn-narrow {
    max-width: none;
    width: auto;
    padding-left: 1rem;
    padding-right: 1rem;
}

.blog-filters {
    .btn {
        // max-width: 120px;
    }
}

// === Core\button
.wp-block-button__link,
body .editor-styles-wrapper .wp-block-button__link{
    @extend .btn;

    margin: 0.5rem 0;
}

body .editor-styles-wrapper .wp-block-button.is-style-fill,
body .wp-block-button.is-style-fill {
    > .wp-block-button__link{
        @extend .btn-primary;
    }
}

body .editor-styles-wrapper .wp-block-button.is-style-outline,
body .wp-block-button.is-style-outline,
body .wp-block-button.is-style-outline:not(.has-text-color) {
    > .wp-block-button__link {
        @extend .btn-outline-primary;

        color: $primary;

        &:hover,
        &:focus,
        &.active {
            color: $warm;
            border-color: $primary;
            background-color: $primary;
        }
    }
}

body .editor-styles-wrapper [data-style="outline-primary"] .wp-block-wp-bootstrap-blocks-button {
    border: 1px solid $primary;
}

body .editor-styles-wrapper .wp-block-button.is-style-outline-secondary,
body .wp-block-button.is-style-outline-secondary{
    > .wp-block-button__link {
        @extend .btn-outline-secondary;
    }
}

body .editor-styles-wrapper [data-style="outline-secondary"] .wp-block-wp-bootstrap-blocks-button {
    border: 1px solid $dark;
}

// === Bootstrap Blocks \ button
.wp-bootstrap-blocks-button {
    flex-grow: 1;

    a {
        margin: 0 0 0.5rem;
        // width: 230px;
    }

    @include media-breakpoint-up(lg) {
        margin-right: 0.46875rem !important;
      }
}

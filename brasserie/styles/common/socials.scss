// ===== Socials =====
.st-socials {
  margin-bottom: 1rem;

  .st-socials-label {
    color: inherit;
    display: inline-block;
    margin: 0 1rem 0 0;
    line-height: 2rem;
  }

  .st-socials-list {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .st-socials-item {
    display: inline-block;
    margin: 0 0.1rem;
    text-align: center;
  }

  .st-socials-link {
    color: inherit;
    display: block;

    i {
      font-size: $h3-font-size;
      background: $white;
      color: inherit;
      display: block;
      line-height: 1.9rem;
      width: 29px;
      height: 29px;
    }

    &:hover,
    &:focus {
      text-decoration: none;
    }
  }

  // === Layout: Icons
  &.layout-icons {
    margin: 0;

    .st-socials-list {
      line-height: 1;
    }

    [class^="icon-"] {
      width: 22px;
      height: 22px;
    }

    .st-socials-link {
      font-size: 0;
    }
  }
}
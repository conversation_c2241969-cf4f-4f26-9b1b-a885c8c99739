.copyright {
    a {
        color: inherit;
        text-decoration: none;
    }

    span {
        display: block;
    }

    @include media-breakpoint-up(lg) {
        span {
            display: inline-block;
            margin: 0 1rem;
        }
    }
}

.saint {
    color: rgba($celadon, 0.5);

    a {
        color: inherit;
        text-decoration: none;
        transition: $transition-base;

        &:hover,
        &:focus,
        &:active {
            color: $white;
        }
    }
}

.master-footer {
    .nav-item {
        //
    }

    .nav-link {
        padding: 0.25rem 0;

        &.current-menu-item,
        &:hover,
        &:focus,
        &:active {
            color: $dark;
        }
    }

    .st-socials.layout-icons [class^="icon-"] {
        width: 28px;
        height: 28px;
    }

    @include media-breakpoint-up(sm) {
        h4, .h3 {
            min-height: 3rem;
        }
    
        .col > ul > .nav-item {
            width: 33%;
        }
    }

    @include media-breakpoint-up(md) {
        h4, .h3 {
            text-align: left;
            min-height: 3rem;
        }
    
        .col > ul > .nav-item {
            width: 20%;
        }
    }

    @include media-breakpoint-up(lg) {
        h4, .h3 {
            min-height: unset;
        }

        .col > ul > .nav-item {
            width: auto;
        }
    }
}

.footer-brand {
    position: absolute !important;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    transform: translateY(-50%);

    @include media-breakpoint-up(lg) {
        right: auto;
    }
}

.footer-slogan {
    position: absolute;
    right: 0;
    transform: translateY(-130%);
}

.swing-sign {
    display: block;
    width: 320px;
    height: auto;
    max-width: 100%;
}

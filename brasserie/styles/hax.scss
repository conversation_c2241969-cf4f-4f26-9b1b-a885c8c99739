body.modal-open,
body.yarl__no_scroll {
    padding-right: 0 !important;
    overflow: visible !important;

    .banner,
    .navbar-toggler,
    .mobile-nav {
        padding-right: 0 !important;
    }

    .navbar-toggler {
        margin-right: 0 !important;
    }
}

@each $breakpoint in map-keys($grid-breakpoints) {

    @each $color,
    $value in $theme-colors {
        @include media-breakpoint-up($breakpoint) {
            .bg-#{$breakpoint}-#{$color} {
                background-color: $value !important;
            }

            .text-#{$breakpoint}-#{$color} {
                color: $value !important;
            }
        }
    }
}

.birdy-scale {
    img {
        height: 100px;
        width: auto;
    }
}

.home-hero-illy-stretch {
    position: relative;

    @include media-breakpoint-down(md) {
        margin: 0px -34%;
        position: relative;
        right: -20%;
    }

    @include media-breakpoint-up(lg) {
        width: calc(50vw - 30px);
        margin-left: calc((100vw - 932px) / -2 - 8px);
    }

    @include media-breakpoint-up(xl) {
        width: calc(1280px / 2 + 100px);
        margin-left: calc((1280px - 200%) / -2 - 100px);
    }
}

.video-home-hero {
    position: relative;

    @include media-breakpoint-down(md) {
        margin: 0px -10%;
        position: relative;
    }

    @include media-breakpoint-up(lg) {
        width: calc(50vw - 30px);
        margin-left: calc((100vw - 932px) / -2 - 8px);
    }

    @include media-breakpoint-up(xl) {
        width: calc(1280px / 2 + 100px);
        margin-left: calc((1280px - 200%) / -2 - 100px);
    }
}

// placeholder illustrations
.placeholder-dog {
    &::after {
        content: "";
        display: block;
        height: 250px;
        width: 100%;
        max-width: 530px;
        background-image: url("/images/placeholder-dog.png");
        background-repeat: no-repeat;
        background-position: right center;
        background-size: auto 250px;

        @include media-breakpoint-up(lg) {
            position: absolute;
            right: -250px;
            bottom: 30px;
        }
    }
}

// === Hide reCaptcha badge
.grecaptcha-badge {
    display: none !important;
}

// temp hide booking buttons ----- nav update -- safe to delete post deployment

.banner,
.offcanvas {
    li.btn.btn-redmid.px-lg-10.mx-auto.mx-lg-0 {
        display: none;
    }
}

// disabled styles for links such us pagination

.disabled-link {
    opacity: 0.25;
}

// BB short booking form

.short-booking-form {
    .h2 {
        font-size: 1rem;
    }

    p,
    .js-select-replace {
        margin: 0;
    }
}

//delete after we fix download badges

.fill-unset {
    fill: unset !important;
}

.calendar-dropdown,
.date-dropdown,
.time-dropdown {
  .dropdown-toggle {
    color: $green;
    display: flex;
    padding: 9px 29px;
    font-family: $font-family-btn;
    font-size: 10px;
    text-transform: uppercase;
    text-align: center;
    align-items: center;
    justify-content: center;
    position: relative;

    .dropdown-indicator {
      position: absolute;
      right: 4px;
    }
  }

  .dropdown-menu {
    background-color: lighten($warm, 2%);
    padding: 0;
    border: none;
    color: $dark;
  }

  .react-calendar .react-calendar__navigation::before {
    content: none;
  }

  .react-calendar__navigation button:disabled {
    opacity: 1;
  }
}

//
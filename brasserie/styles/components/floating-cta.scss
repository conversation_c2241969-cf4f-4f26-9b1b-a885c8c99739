.cta-buttons {
    > *:last-child {
        margin-bottom: 0 !important;
    }
}

.cta-floating {
    right: $spacer * 1.5;
    top: 100vh;
    z-index: $zindex-fixed;
    // background-color: var(--base-backround);
    transition: all 3s ease-out;

    .rene {
        width: 100px;
        height: auto;
    }

    &.show {
        top: 180px;
    }

    &.hide {
        top: -100vh;
    }
}

.cta-trigger {
    cursor: pointer;
}

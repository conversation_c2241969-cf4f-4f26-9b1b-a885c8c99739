/*===== Shortcode: [newsletter_form] =====*/
.shortcode-newsletter {
  position: relative;

  .post--cta--newsletter {
    margin: 0 auto;
    min-height: 0;
    max-width: 100%;
  }

  .form-row {
    &.error {
      label,
      input,
      .react-select__control,
      .react-select__single-value,
      .react-select__indicator.react-select__dropdown-indicator {
        color: $red;
      }
      input,
      .react-select__control{
        border-color: $red;
      }
      .react-select-container .react-select__indicator-separator {
        background-color: $red;
      }
    }
  }

  .message.error,
  .message.dob-error {
    color: $red;
    padding: 1rem;
    border: 1px solid $red;
    margin: 1rem 0;
  }

  .message.success {
    color: $celadon;
    padding: 1rem;
    border: 1px solid $celadon;
    margin: 1rem 0;
  }
}
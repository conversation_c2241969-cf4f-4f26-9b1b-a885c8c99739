.menus-nav {
    position: relative;
    font-family: $font-family-subheading;

    .menu-item {
        margin-bottom: 0.5rem;

        a {
            text-decoration: none;
        }

        &.active a,
        a:hover,
        a:focus {

            &::after {
                content: '';
                display: block;
                width: 100%;
                height: 7px;
                position: absolute;
                top: 100%;
                background-image: url("/images/tripple-border-horizontal.png");
                background-repeat: repeat-x;
                background-size: auto 100%;
            }
        }
    }
}
// stylelint-disable

// --- Various stuff ---
$enable-negative-margins: true;
$mobile-nav-height: 30px;

// BB palette
$dark: #28170F !default;
$green: #415143 !default;
$celadon:#6EA394 !default;
$red: #EC472E !default;
$warm: #FFF9E6 !default;
$black: $dark !default;
$white: $warm !default;

$summer:    #a2a2a2;
$autumn:    #a2a2a2;
$winter:    #a2a2a2;
$spring:    #a2a2a2;

$body-bg: $warm !default;
$body-color: $green !default;

// scss-docs-end color-variables

// scss-docs-start theme-color-variables
$primary:       $green !default;
$secondary:     $red !default;
// scss-docs-end theme-color-variables

// scss-docs-start theme-colors-map
$theme-colors: (
  "primary":    $primary,
  "secondary":  $secondary,
  "success":    $green,
  "info":       $celadon,
  "warning":    $celadon,
  "danger":     $red,

  // HWC palette
  "dark":       $dark,
  "green":      $green,
  "celadon":    $celadon,
  "red":        $red,
  "warm":       $warm,

  // HWI palette
  "summer":     $summer,
  "autumn":     $autumn,
  "winter":     $winter,
  "spring":     $spring,
) !default;
// scss-docs-end theme-colors-map

// Prefix for :root CSS variables

$variable-prefix:             bs- !default; // Deprecated in v5.2.0 for the shorter `$prefix`
$prefix:                      $variable-prefix !default;

// Spacing
//
// Control the default styling of most Bootstrap elements by modifying these
// variables. Mostly focused on spacing.
// You can add more entries to the $spacers map, should you need more variation.

// scss-docs-start spacer-variables-maps
$spacer: (10 / 16) * 1rem !default;
$spacers: (
  0: 0,
  5: $spacer * .5,
  10: $spacer,
  15: $spacer * 1.5,
  20: $spacer * 2,
  25: $spacer * 2.5,
  30: $spacer * 3,
  40: $spacer * 4,
  50: $spacer * 5,
  70: $spacer * 7,
  80: $spacer * 8,
  100: $spacer * 10,
  135: $spacer * 13.5,
  150: $spacer * 15
) !default;
// scss-docs-end spacer-variables-maps

// Links
//
// Style anchor elements.

$link-color:                              $dark !default;
$link-decoration:                         underline !default;
$link-shade-percentage:                   20% !default;
$link-hover-color:                        shift-color($link-color, $link-shade-percentage) !default;
$link-hover-decoration:                   null !default;

// Paragraphs
//
// Style p element.

$paragraph-margin-bottom:   (24 / 16) * 1rem !default;


// Grid breakpoints
//
// Define the minimum dimensions at which your layout will change,
// adapting to different screen sizes, for use in media queries.

// scss-docs-start grid-breakpoints
$grid-breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1400px
) !default;
// scss-docs-end grid-breakpoints

@include _assert-ascending($grid-breakpoints, "$grid-breakpoints");
@include _assert-starts-at-zero($grid-breakpoints, "$grid-breakpoints");


// Grid containers
//
// Define the maximum width of `.container` for different screen sizes.

// scss-docs-start container-max-widths
$container-max-widths: (
  xs: 100%,
  sm: 500px,
  md: 738px,
  lg: 932px,
  xl: 1140px,
  xxl: 1320px
) !default;
// scss-docs-end container-max-widths

// Grid columns
//
// Set the number of columns and specify the width of the gutters.

$grid-columns:                12 !default;
$grid-gutter-width:           (20 / 16) * 1rem !default;
$grid-row-columns:            6 !default;

// Container padding

$container-padding-x: $grid-gutter-width !default;


// Components
//
// Define common padding and border radius sizes and more.

$transition-base:             all .3s ease-in-out !default;
$transition-fade:             opacity .35s linear !default;
// scss-docs-start collapse-transition
$transition-collapse:         height .35s ease !default;
$transition-collapse-width:   width .35s ease !default;
// scss-docs-end collapse-transition

// stylelint-disable function-disallowed-list
// scss-docs-start aspect-ratios
$aspect-ratios: (
  "1x1": 100%,
  "2x1": calc(1 / 2 * 100%),
  "1x2": calc(2 / 1 * 100%),
  "3x2": calc(2 / 3 * 100%),
  "2x3": calc(3 / 2 * 100%),
  "4x3": calc(3 / 4 * 100%),
  "3x4": calc(4 / 3 * 100%),
  "9x16": calc(16 / 9 * 100%),
  "9x13": calc(13 / 9 * 100%),
  "16x9": calc(9 / 16 * 100%),
  "21x9": calc(9 / 21 * 100%),
) !default;
// scss-docs-end aspect-ratios
// stylelint-enable function-disallowed-list

// scss-docs-start zindex-stack
$zindex-dropdown:                   1000 !default;
$zindex-brand:                      1010 !default;
$zindex-sticky:                     1020 !default;
$zindex-fixed:                      1030 !default;
$zindex-offcanvas-backdrop:         1049 !default;
$zindex-offcanvas:                  1050 !default;
$zindex-banner:                     1046 !default;
$zindex-modal-backdrop:             1050 !default;
$zindex-modal:                      1055 !default;
$zindex-popover:                    1070 !default;
$zindex-tooltip:                    1080 !default;
$zindex-toast:                      1090 !default;
// scss-docs-end zindex-stack


// Typography
//
// Font, line-height, and color for body text, headings, and more.

// scss-docs-start font-variables
// stylelint-disable value-keyword-case
$font-family-sans-serif:      var(--font-cormorant, "Arial"), Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji" !default;
$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace !default;
$font-family-heading:         var(--font-palonuevo, "Arial"), Arial, sans-serif !default;
$font-family-subheading:      var(--font-libre-franklin), Arial, sans-serif !default;
$font-family-btn:             var(--font-palonuevo-bold, "Arial"), Arial, sans-serif !default;
// stylelint-enable value-keyword-case
$font-family-base:            var(--#{$prefix}font-sans-serif) !default;
$font-family-code:            var(--#{$prefix}font-monospace) !default;

// $font-size-root affects the value of `rem`, which is used for as well font sizes, paddings, and margins
// $font-size-base affects the font size of the body text
$font-size-root:              null !default;
$font-size-base:              1rem !default; // Assumes the browser default, typically `16px`
$font-size-sm:                $font-size-base * .75 !default; // 12px
$font-size-lg:                $font-size-base * 1.25 !default; // 20px

$font-weight-normal:          400 !default;
$font-weight-medium:          500 !default;
$font-weight-semibold:        600 !default;
$font-weight-bold:            700 !default;
$font-weight-black:           800 !default;

$font-weight-base:            $font-weight-medium !default;

$line-height-base:            (26 / 16) !default;
$line-height-sm:              1.25 !default;
$line-height-lg:              2 !default;

$h1-font-size:                (36 / 16) * 1rem !default;
$h2-font-size:                (21 / 16) * 1rem !default;
$h3-font-size:                (17 / 16) * 1rem !default;
$h4-font-size:                (17 / 16) * 1rem !default;
$h5-font-size:                (17 / 16) * 1rem !default;

$h2-font-size-lg:             (24 / 16) * 1rem !default;
$h3-font-size-lg:             (24 / 16) * 1rem !default;

// scss-docs-end font-variables

// scss-docs-start headings-variables
$headings-margin-bottom:      $spacer !default;
$headings-font-style:         null !default;
$headings-font-weight:        400 !default;
$headings-line-height:        1.2 !default;
$headings-color:              null !default;
// scss-docs-end headings-variables

// scss-docs-start type-variables
$lead-font-size:              (17 / 16) * 1rem !default;
$lead-font-weight:            $font-weight-semibold !default;
$small-font-size:             $font-size-sm !default;
// scss-docs-end type-variables

// Buttons + Forms
//
// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.

// scss-docs-start input-btn-variables
$input-btn-padding-y:         (7 / 16) * 1rem !default;
$input-btn-padding-x:         (30 / 16) * 1rem !default;
$input-btn-font-family:       null !default;
$input-btn-font-size:         $font-size-base !default;
$input-btn-line-height:       $line-height-base !default;

$input-btn-focus-width:         .25rem !default;
$input-btn-focus-color-opacity: .25 !default;
$input-btn-focus-blur:          0 !default;

$input-btn-padding-y-sm:      .25rem !default;
$input-btn-padding-x-sm:      .5rem !default;
$input-btn-font-size-sm:      $font-size-sm !default;

$input-btn-padding-y-lg:      .5rem !default;
$input-btn-padding-x-lg:      1rem !default;
$input-btn-font-size-lg:      $font-size-lg !default;
// scss-docs-end input-btn-variables


// Buttons
//
// For each of Bootstrap's buttons, define text, background, and border color.

// scss-docs-start btn-variables
$btn-font-size:               $small-font-size;
$btn-font-weight:             $font-weight-normal !default;
$btn-box-shadow:              inset 0 1px 0 rgba($white, .15), 0 1px 1px rgba($black, .075) !default;
$btn-disabled-opacity:        .65 !default;
$btn-active-box-shadow:       inset 0 3px 5px rgba($black, .125) !default;
$btn-border-radius:           0 !default;
$btn-border-radius-lg:        $btn-border-radius !default;

$btn-link-color:              var(--#{$prefix}link-color) !default;
$btn-link-hover-color:        var(--#{$prefix}link-hover-color) !default;

// Allows for customizing button radius independently from global border radius
$btn-border-radius:           0;
$btn-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;
// scss-docs-end btn-variables


// Forms

// scss-docs-start form-text-variables
$form-text-margin-top:                  .25rem !default;
// scss-docs-end form-text-variables

// scss-docs-start form-label-variables
$form-label-margin-bottom:              0 !default;
$form-label-font-size:                  1rem !default;
// scss-docs-end form-label-variables

// scss-docs-start form-input-variables
$input-padding-y:                       0 !default;
$input-padding-x:                       $spacer/2 !default;

$input-bg:                              $body-bg !default;
$input-color:                           $body-color !default;
$input-border-color:                    $dark !default;
$input-plaintext-color:                 $body-color !default;

$input-transition:                      border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;

$form-color-width:                      3rem !default;

$form-check-input-border:               1px solid;
$form-check-input-border-radius:        0;
$form-check-input-focus-box-shadow:     none;

$focus-outline:                         1px solid;

// scss-docs-end form-input-variables

// scss-docs-start zindex-levels-map
$zindex-levels: (
  n1: -1,
  0: 0,
  1: 1,
  2: 2,
  3: 3
) !default;
// scss-docs-end zindex-levels-map

// Navs

// scss-docs-start nav-variables
$nav-link-padding-y:                0 !default;
$nav-link-padding-x:                0 !default;
$nav-link-font-size:                (17 / 16) * 1rem !default;
$nav-link-font-weight:              400 !default;
$nav-link-color:                    currentColor !default;
$nav-link-hover-color:              $celadon !default;
$nav-link-transition:               color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out !default;
// scss-docs-end nav-variables


// Navbar

// scss-docs-start navbar-variables
$navbar-padding-y:                  0 !default;
$navbar-padding-x:                  null !default;

$navbar-nav-link-padding-x:         (29 / 16) * .5rem !default;

$navbar-brand-font-size:            $font-size-lg !default;
// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link
$nav-link-height:                   $font-size-base * $line-height-base + $nav-link-padding-y * 2 !default;
$navbar-brand-height:               (175 / 16) * 1rem !default;
$navbar-brand-padding-y:            null !default;
$navbar-brand-margin-end:           0 !default;

$navbar-toggler-padding-y:          0 !default;
$navbar-toggler-padding-x:          0 !default;
$navbar-toggler-font-size:          $font-size-lg !default;
$navbar-toggler-border-radius:      null !default;
$navbar-toggler-transition:         box-shadow .15s ease-in-out !default;

// scss-docs-end navbar-variables

// Dropdowns
//
// Dropdown menu container and contents.

// scss-docs-start dropdown-variables
$dropdown-min-width:                auto !default;
$dropdown-padding-x:                0 !default;
$dropdown-padding-y:                .5rem !default;
$dropdown-spacer:                   .125rem !default;
$dropdown-font-size:                $font-size-base !default;
$dropdown-color:                    $celadon !default;
$dropdown-bg:                       $warm !default;
// fusv-enable
// scss-docs-end dropdown-variables

// Offcanvas

// scss-docs-start offcanvas-variables
$offcanvas-padding-y:               1rem !default;
$offcanvas-padding-x:               10px !default;
$offcanvas-transition-duration:     .3s !default;
$offcanvas-bg-color:                $green !default;
$offcanvas-border-width:            0 !default;
// scss-docs-end offcanvas-variables

// Accordion

// scss-docs-start accordion-variables
$accordion-padding-y:                     1rem !default;
$accordion-padding-x:                     0 !default;
$accordion-color:                         var(--#{$prefix}body-color) !default;
$accordion-bg:                            none !default;
$accordion-border-width:                  0 !default;
$accordion-border-color:                  var(--#{$prefix}border-color) !default;
$accordion-border-radius:                 var(--#{$prefix}border-radius) !default;
$accordion-inner-border-radius:           subtract($accordion-border-radius, $accordion-border-width) !default;

$accordion-body-padding-y:                $accordion-padding-y !default;
$accordion-body-padding-x:                $accordion-padding-x !default;

$accordion-button-padding-y:              $accordion-padding-y !default;
$accordion-button-padding-x:              $accordion-padding-x !default;
$accordion-button-color:                  var(--#{$prefix}body-color) !default;
$accordion-button-bg:                     var(--#{$prefix}accordion-bg) !default;
$accordion-transition:                    $btn-transition, border-radius .15s ease !default;
$accordion-button-active-bg:              var(--#{$prefix}primary-bg-subtle) !default;
$accordion-button-active-color:           var(--#{$prefix}primary-text-emphasis) !default;

// accordions
$accordion-btn-focus-box-shadow: none;
$accordion-body-padding-y: $spacer * 2;
$accordion-body-padding-x: 0;

// Modal

$modal-content-border-radius:       0 !default;
$modal-content-inner-border-radius: 0 !default;

// stylelint-enable

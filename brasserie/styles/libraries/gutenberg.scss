@import "~@wordpress/base-styles/colors";
@import "~@wordpress/base-styles/variables";
@import "~@wordpress/base-styles/mixins";
@import "~@wordpress/base-styles/breakpoints";
@import "~@wordpress/base-styles/animations";
@import "~@wordpress/base-styles/z-index";


// It is important to include these styles in all built stylesheets.
// This allows to CSS variables post CSS plugin to generate fallbacks.
// It also provides default CSS variables for npm package consumers.
@import '~@wordpress/base-styles/default-custom-properties';

@import '~@wordpress/block-library/src/style.scss';
@import '~@wordpress/block-library/src/theme.scss';


// ===== Blocks =====

// --- Image block
.wp-block-image {
  &.alignleft,
  &.alignright,
  &.aligncenter {
    display: block;
  }

  figcaption {
    text-align: right;
    font-weight: $lead-font-weight;
    margin-top: 0;
    font-size: inherit;
    color: inherit;
  }
}

// --- Cover block
.wp-block-cover {
  overflow: visible;
  place-items: flex-end;
  padding: 4*$spacer 0;
  min-height: 0;

  &__background,
  &__image-background  {
    @extend .edge-2-edge;
  }

  &__image-background {
    //
  }
}

// --- Separator block
.wp-block-separator:not(.is-style-wide):not(.is-style-dots) {
  width: auto;
}

// ===== Utilities & hax =====
.is-layout-flex {
    @extend .d-flex, .flex-wrap;
}

.is-vertical {
    @extend .flex-column;
}

.is-nowrap {
  flex-wrap: nowrap !important;
}

.is-content-justification-center {
  justify-content: center !important;
}

.is-content-justification-space-between {
  justify-content: space-between !important;
}

:where(.wp-block-cover:not(.has-text-color)), :where(.wp-block-cover-image:not(.has-text-color)) {
  color: $warm;
}

.has-text-align-justify {
  text-align: justify !important;
}

// Colors: text
@each $key, $color in $theme-colors {
  .has-#{$key}-color {
    color: $color;
  }
}

// Colors: background
@each $key, $color in $theme-colors {
  .has-#{$key}-background-color {
    background-color: $color;
  }
}
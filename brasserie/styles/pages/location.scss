.single-location {
  @include media-breakpoint-up(lg) {
    padding-top: 6rem !important;

    .banner {
      padding-bottom: 0;
    }

    .navbar {
      padding-bottom: 1rem;
    }
  };
}

.location-subnav {
  padding: 1rem 0;

  .dropdown-indicator {
    position: absolute;
    right: 0;
    top: -4px;
  }

  .nav-item {
    margin: 0 0.5rem;

    &:last-child {
      margin-right: 0;
    }

    .nav-link {
      font-family: $font-family-btn;
      font-size: 12px
    }

    &.location-name {
      .nav-link {
        font-family: $font-family-heading;
        font-size: 14px;
      }
    }
  }

  @include media-breakpoint-down(lg) {
    position: fixed;
    bottom: $mobile-nav-height;
    left: 0;
    right: 0;
    z-index: 100;
    padding: 0;
    transform: translateY( calc(100% - 2.5rem) );
    transition: $transition-base;
    will-change: transform;

    nav {
      max-height: calc(100vh - $mobile-nav-height - 40px);
      overflow-y: auto;
      padding: 0.25rem 0 1rem;
    }

    .nav-item {
      margin: 0.5rem 0;
      text-align: center;

      .nav-link {
        padding: 0.25rem 0;
      }

      &.location-name {
        position: relative;
      }
    }

    &.subnav-open {
      transform: none;

      .dropdown-indicator {
        transform: rotate(180deg);
      }
    }
  }
}
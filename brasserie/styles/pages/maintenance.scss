.maintenance-page {
  padding-top: 0 !important; // overriding layout.scss TODO
}

.maintenance-page-inner {
  background-color: $warm;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  width: 100vw;

  article {
    // max-width: 400px;
  }

  .body-copy {
    > * {
      margin-top: 0;
    }
  }

  .logo {
    width: 290px;
    height: auto;
  }

  .rene {
    width: 130px;
    height: auto;
  }

  .maintenance-video {
    margin: 0 auto;
    margin-top: -2rem;

    @include media-breakpoint-up(md) {
      margin-top: -4rem;
    }

    @include media-breakpoint-up(xl) {
      margin-top: -10rem;
    }
  }

  .splashboxes {
    @include media-breakpoint-down(md) {
      --bs-gutter-x: (30 / 16) * 1rem;
    }

    .col-12 {
      margin-bottom: calc(var(--bs-gutter-x));
    }

    .box{
      min-height: 100%;
    }
  }

  // === Custom Maintenance view (proxy of 'coming-soon/ page')
  &.custom {
    display: block;

    .shortcode-newsletter {
      .message.success {
        .btn {
          display: none !important;
        }
      }
    }
  }
}
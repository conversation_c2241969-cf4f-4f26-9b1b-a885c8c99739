.tenkites-menu {
    position: relative;


    &.has-badge {
        --badge-padding: 40px;
        --badge-height: 126px;

        margin-top: calc((2 * var(--badge-padding) + var(--badge-height)) / 2 + 4 * $spacer);
        min-height: 300px;

        .k10-body {
            padding-top: calc((2 * var(--badge-padding) + var(--badge-height)) / 2);
        }

        .menu-badge {
            height: var(--badge-height);
            padding: var(--badge-padding);
            box-sizing: content-box;
            background-color: $warm;
            position: absolute;
            left: 50%;
            top: 0;
            transform: translate(-50%, -50%);
            z-index: 2;

            img {
                height: 100%;
                width: auto;
            }
        }
    }
}
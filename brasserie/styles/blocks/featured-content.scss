// stylelint-disable
// ===== block: Featured content
.block-featured {
  position: relative;

  .innerblocks {
    p {
      font-weight: $lead-font-weight;
    }
  }

  .featured-content {
    position: relative;
    // z-index: 1;
  }

  .featured-image-wrap {
    position: relative;
    // z-index: 2;

    .image-cover {
      position: absolute;
      left: 0%;
      top: 0%;
      right: auto;
      bottom: 0%;
      width: 100%;
      height: 100%;
      background-color: var(--base-backround);
    }
  }

  .buttons {
    > *:last-child {
      margin-bottom: 0 !important;
    }

    &.columns-2 {
      @include media-breakpoint-up(lg) {
      flex-direction: row !important;
      flex-wrap: wrap;
      }
    }

    margin-bottom: $spacer * 5;

    @include media-breakpoint-up(md) { 
      margin-bottom: 0;
    }
  }
}

// ===== Skins (this can be used globally) =====
// === Skin: Green
.skin-green {
    background: $green;
    color: $warm;
}

// === Skin: Red
.skin-red {
    background: $red;
    color: $warm;

    hr {
      border-color: $warm;

      &::before {
        background-color: $warm;
      }
    }
}

// === Skin: Celadon
.skin-celadon {
    background: $celadon;
    color: $green;
}

// stylelint-enable

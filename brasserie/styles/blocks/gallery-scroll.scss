.gallery-wrap-scroll {
  position: relative;
  // height: 200vh;
  margin: 0 -2*$spacer;
  height: 1000px;
  overflow: hidden;

  .scroll-container {
    overflow: auto;
    display: flex;
    justify-content: flex-start;
    // position: sticky;
    top: 20vh;
  }

  .wp-block-image {
    width: 90%;
    display: flex;
    justify-content: center;
    padding: 0 $spacer*1.5;
    margin-bottom: 0;
  }

  @include media-breakpoint-up(md) {
    .wp-block-image {
      width: 45%;
    }
  }

  @include media-breakpoint-up(lg) {
    .scroll-container {
      overflow: hidden;
      display: flex;
      // position: sticky;
      // top: 33%;
      // bottom: 33%;
    }

    .wp-block-image {
      width: 30%;
    }
  }

  @include media-breakpoint-up(lg) {
    margin: 0 calc((100vw - 100%) / -2);
  }

  @include media-breakpoint-up(xl) {
    margin: 0 calc((1326px - 100%) / -2);
  }

  @include media-breakpoint-down(lg) {
    height: auto !important;
  }
}

.gallery-wrap-framer {
    .img-group-container {
        height: calc( var(--items-count) * 100vh );
        position: relative;
    }

    .img-group-container > div {
        position: sticky;
        top: 0;
        overflow: hidden;
        height: 100vh;
    }

    .img-group {
        display: flex;
    }

    .block-gallery-scroll-item {
        display: flex;
        width: 100vw;
        height: 100vh;
        flex: 0 0 auto;
        align-items: center;
        justify-content: center;
        // flex-direction: column;

        // > * {
        //     width: 100%;
        //     height: 100%;
        //     display: flex;
        //     align-items: center;
        // }
    }

    .progress {
        position: fixed;
        left: 0;
        right: 0;
        height: 5px;
        background: $dark;
        bottom: 50px;
        transform: scaleX(0);
    }
}
.block-accordions {
  ul,
  ol {
    padding: 0;
  }

  ul {
    list-style: none;

    &::after {
      content: "\2767";
      display: block;
      width: 100%;
    }

    li::before {
      content: "\2767";
      display: block;
      width: 100%;
    }
  }

  ol {
    counter-reset: cupcake;
    list-style: none;

    li {
      counter-increment: increment;
      margin-bottom: 1rem;

      &::before {
        content: counters(increment, "");
        display: block;
        font-weight: bold;
      }
    }
  }
}

.accordion-item {
  border: none;
  border-bottom: 1px solid $red;

  &:first-child,
  &:last-child {
    border-radius: 0;
  }
}

.accordion-body {
  text-align: justify;

  .card-body {
    > *:last-child {
      margin-bottom: 0;
    }
  }
}

.accordion-button {
  font-size: inherit;
  align-items: end;
  justify-content: space-between;
  position: relative;
  font-family: $font-family-subheading;
  text-transform: uppercase;

  @include media-breakpoint-up(lg) {
    position: static;
    justify-content: space-between;
    padding-left: $spacer * 0;
    padding-right: $spacer * 0;
  }

  &::after {
    display: none;
  }

  .label-wrap {
    max-width: calc(100% - 20px - $spacer);

    &::after {
      content: '';
      display: block;
      position: absolute;
      border-bottom: 1px dotted;
      inset: 0 0 calc($spacer*2) 0;
      z-index: 0;
      max-width: calc(100% - 20px - $spacer);
    }

    span {
      background-color: $warm;
      display: inline;
      position: relative;
      z-index: 1;
    }
  }

  svg {
    transition: transform 0.3s ease-in-out;
    background-color: $warm;
    width: 20px;
    box-sizing: content-box;
    height: auto;
    position: relative;
    z-index: 1;
    bottom: 3px;
  }

  &[aria-expanded="true"] {
    //open accordion
    svg {
      transform: rotate(-180deg);
    }
  }

  &:focus {
    box-shadow: none;
  }

  &:not(.collapsed) {
    background-color: unset;
    outline: none;
  }
}
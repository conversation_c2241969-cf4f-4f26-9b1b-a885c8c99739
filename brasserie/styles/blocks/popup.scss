.btn-close {
  background-image: none;
  opacity: 1;
  border-radius: 0;
  padding: $spacer;
  top: $spacer;
  right: $spacer;
  transition: all 0.3s ease-in-out;
  background-color: $warm;
  width: auto;

  svg {
    color: $green;
    width: $spacer * 2;
    height: auto;
    stroke: $green;
    stroke-width: 2px;
    stroke-linejoin: round;
    transition: all 0.3 ease;
  }

  &:hover {
    opacity: 1;
    background-color: $red;
    
    svg {
      color: $warm;
      stroke: $warm;
    }
  }
}

.modal-content {
  overflow: unset !important;
}

.promo-content {
  > *:first-child {
    margin-top: 0;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    &:first-child {
      margin-bottom: $spacer * 1.5;
    }
  }
}

.promo-btns {
  li {
    margin-bottom: $spacer * 1.5;

    + li {
      margin-left: $spacer/2;
    }
  }
}

.modal.solo-img {
  .modal-dialog {

    @include media-breakpoint-up(md) {
      --bs-modal-width: 500px;
    }

    @include media-breakpoint-up(lg) {
      --bs-modal-width: 768px;
    }
  }
}

.promo-img {
  object-fit: cover;
  width: 100%;
  height: 100%;
}
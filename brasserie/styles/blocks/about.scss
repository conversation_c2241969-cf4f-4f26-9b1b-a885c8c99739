.block-about {
  --bg-image-height: 160px;
  padding-top: calc(var(--bg-image-height) / 2);

  @include media-breakpoint-up(xl) {
    --bg-image-height: 190px;
  }

  .badge {
    position: absolute;
    top: calc(var(--bg-image-height) / -2);
    left: 0;
    width: 100%;
    height: var(--bg-image-height);
    z-index: 2;
    background-repeat: no-repeat;
    background-position: center top ;
    background-size: auto 100%;
  }

  .wrap {
    position: relative;
    border: 1px solid $green;
    padding: $spacer;
    padding-bottom: $spacer * 12;
    padding-top: calc(var(--bg-image-height) / 2 + 2 * $spacer);
    margin-bottom: $spacer*2;
    display: flex;
    flex-direction: column;
    justify-content: center;

    @include media-breakpoint-up(md) {
      min-height: 100%;
      padding: $spacer * 3;
      padding-top: calc(var(--bg-image-height) / 2 + 2 * $spacer);
      padding-bottom: $spacer * 12;
    }

    &::after {
      content: "EST 1996";
      position: absolute;
      bottom: 20px;
      left: 0;
      width: 100%;
      font-family: $font-family-subheading;
      padding-top: 60px;
      text-align: center;
      line-height: 1;
      background: url("/images/monogram-red.svg") no-repeat;
      background-position: center top;
      background-size: auto 40px;
    }
  }
}
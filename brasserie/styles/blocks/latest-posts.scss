.block-latest {
    position: relative;
    color: $warm;

    &:after {
        content: '';
        display: block;
        position: absolute;
        left: 50%;
        top: 0;
        height: 100%;
        background: $green;
        right: 0;
        width: 100vw;
        transform: translate(-50%);
        z-index: -1;
    }

    > *:last-child {
        hr {
            display: none;
        }
    }

    .post-excerpt {
        font-weight: $lead-font-weight;
    }
}

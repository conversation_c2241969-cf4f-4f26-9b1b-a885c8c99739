.opening-times {
  margin: 0 auto;
  text-align: center !important;

   @include media-breakpoint-up(md) {
    text-align: left !important;
   }

  @include media-breakpoint-up(lg) {
    margin: 0;
  }

  h1, .h1,
  h2, .h2,
  h3, .h3,
  h4, .h4,
  h5, .h5,
  h6, .h6 {
    text-align: inherit;
  }

  p {
    text-align: inherit;
  }

  ul {
    font-family: $font-family-subheading;

    @include media-breakpoint-up(md) {
      margin-left: 100px;
    }
  }

  li {
    text-align: center !important;

    @include media-breakpoint-up(md) {
      text-align: left !important;
    }
  }

  .day,
  .time {
    display: inline-block;
  }

  .day {
    font-weight: 600;
  }

  .time {
  }
}

.opening-times-group {

  ul {}

  li {}

  .time {
    padding-left: 0;
  }
}

.master-footer {
  .opening-times {
    margin: 0 auto;

    @include media-breakpoint-up(lg) {
      margin: 0;
    }

    li {}

    .day,
    .time {
      display: inline-block;
      width: 50%;
    }

    .day {
      text-align: right;
      padding-right: $spacer / 2;

      @include media-breakpoint-up(lg) {
        text-align: right;
      }
    }

    .time {
      text-align: left;
      padding-left: $spacer / 2;
    }
  }
}
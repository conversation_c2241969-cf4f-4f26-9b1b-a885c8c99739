// === General Gallery styles
.wp-block-gallery,
.wp-block-gallery .innerblocks {
  .wp-block-image,
  .wp-block-video {
    margin-bottom: $grid-gutter-width;

    > * {
      height: 100% !important;

    }

    figcaption {
      display: none;
    }

    &:not([class*="col-"]) {
      &:nth-child(5n+1) {
        @extend .col-12, .col-md-6;
      }

      &:nth-child(5n+2),
      &:nth-child(5n+3){
        @extend .col-6, .col-md-3;
      }

      &:nth-child(5n+4){
        @extend .col-12, .col-md-4;
      }

      &:nth-child(5n){
        @extend .col-12, .col-md-8;
      }
    }
  }

  .wp-block-image {
    cursor: pointer;
  }

  img,
  video {
    height: 100%;
    width: 100%;
    object-fit: cover;
    object-position: center center;
  }
}

// --- ACF: Gallery block - specific styles
.block-gallery {

}
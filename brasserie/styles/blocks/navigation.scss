.wp-block-navigation {

    .wp-block-navigation__container {
        align-items: center;
        flex-direction: column;
        align-items: stretch;
    }

    .wp-block-navigation-item {
        flex-direction: column;
        align-items: center;
        margin-bottom: $spacer * 3;
        padding-bottom: $spacer * 3;
        position: relative;
        
        @include media-breakpoint-up(lg) {
            align-items: start;
        }
    
        &::after {
            content:"";
            display: block;
            width: 100%;
            position: absolute;
            bottom: 0;
    
            @extend %hr;
        }
    }
}

.wp-block-navigation-item__content {
}

.wp-block-navigation-item__label {
    font-family: $headings-font-family;
    font-size: $h3-font-size;
    line-height: 1.2;

    &::after {
        content: "";
        display: block;
        background-image: url("/images/icon.svg#arrow-down");
        background-repeat: no-repeat;
        width: 1rem;
        height: 1rem;
        border: none;
        opacity: 1;
        background-position: center;
        background-size: auto 100%;
        margin: 0;
        position: absolute;
        right: 0;
        top: 0.25em;
    }
}

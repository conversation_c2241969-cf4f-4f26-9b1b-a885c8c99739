@import url('//fonts.cdnfonts.com/css/avenir-lt-std');

* {
  border-radius: 0 !important;
}

body,
input,
button,
textarea {
  font-family: 'Avenir LT Std', sans-serif;
}

.shadow-sm {
  box-shadow: none !important;
}

.card,
.btn {
  border-radius: 0;
}

.btn-primary {
  background-color: #688585 !important;
  border: 2px solid #fff !important;
}

.btn-secondary {
  background-color: #aebfc1 !important;
  border: 2px solid #fff;
}

.card {
  border: 1px solid rgba(0, 0, 0, .125);
}

body {
  color: #282828 !important;
  letter-spacing: 1px;
  background: #fff;
  font-family: "KeplerStd-Regular", Georgia, Times, serif;
  font-size: 1rem;
  line-height: 1.33;
  letter-spacing: 1px;
  padding-top: 3rem;
}

.btn {
  background: #688585;
  border: 2px solid #fff;
  font-family: "Avenir Medium", Georgia, Times, serif;
  font-size: 1rem;
  padding: .66rem 1.5rem;
  color: #fff;
  text-transform: uppercase;
  border-radius: 0;
}

.occasion-name .card {
  border: none;
}

.attendees-list-panel .card-header {
  background: none;
  border: none;
  font-family: "Avenir Medium", Georgia, Times, serif;
  letter-spacing: .12em;
  color: #466565;
  font-weight: 400;
  text-transform: uppercase;

}

h1,
.h1,
h2,
.h2 {
  font-family: "Avenir Medium", Georgia, Times, serif;
  letter-spacing: .12em;
  color: #466565;
  font-weight: 400;
  text-transform: uppercase;
  margin-bottom: 1rem;

}

.user-message {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1;
  padding: 0;
}

.user-message .message-title .message {
  display: inline-block;
  margin-right: 1rem;
  font-size: 1rem;
}

.h3,
h3 {
  font-size: 1rem;
}

.location-panel .card-footer {
  background-color: transparent;
  border: 0;
}

.site-details .location-panel {
  background: #aebfc1;
  border: none;
}

.svg-inline--fa.fa-3x {
  color: #688585;
  margin-bottom: 1rem;
}

.h4,
h4 {
  font-size: 1.125rem;
}

.footer .location-panel {
  border: none;
  padding: 1rem 0;
}

.footer .location-panel .card-body {
  padding: 0;
}

.attendees-list-panel .card-footer {
  background: #aebfc1;
  border-top: none;
}

.card-footer {
  border-radius: 0 !important;
  background-color: #e9f4f5;
  border-top: none;
}

.attendees-list .event-overview-btn.active {
  background: #688585 !important;
  border: 2px solid #fff !important;
  font-family: "Avenir Medium", Georgia, Times, serif;
  font-size: 1rem;
  padding: .66rem 1.5rem;
  color: #fff !important;
  text-transform: uppercase;
  border-radius: 0;
}

.form-add-attendee .btn-secondary,
.location-panel .btn-secondary,
.attendees-list .btn-secondary {
  background: #688585 !important;
  border: 2px solid #fff !important;
}

.form-add-attendee {
  max-width: 702px;
  margin: 0 auto;
  padding: 30px;
  background: #aebfc1;
}

.form-control {
  border-radius: 0;
  border: 1px solid rgba(0, 0, 0, .125);
  color: #282828;
}

.form-control:focus {

  box-shadow: 0 0 0 .2rem rgba(0, 0, 0, .125);
  border-color: rgba(0, 0, 0, .125);
}

.footer .location-panel ul>li:last-child {
  display: none;
}

.footer .location-panel ul {
  align-content: flex-start;
  justify-content: center;
}

.footer .location-panel ul>li {
  margin: 1rem;
}

.total-required-amount,
.total-remaining-amount {
  font-size: 1.5rem;
}

.total-required,
.total-remaining {
  font-family: "Avenir Medium", Georgia, Times, serif;
  letter-spacing: .12em;
  color: #466565;
  font-weight: 400;
  text-transform: uppercase;
  margin-bottom: 1rem;
  font-size: 1rem;
}

.event-manage-form {
  max-width: 440px;
  margin: 0 auto 4rem;
}

h2 {
  text-align: center;
}

.event-manage-form .row {
  width: auto;
}

.col-1.form-control {}

.cover-picker.input-group {}

body .event-manage-form .cover-picker input {
  width: 80px;
  max-width: 80px;
  min-width: 80px;
}

.data-consent-wrapper div div label {
  font-weight: 400;
  display: inline-block;
  width: calc(100% - 2rem);
  margin-left: 1rem;
  vertical-align: top;
  /*! margin-bottom: 1rem; */
}

.data-consent-wrapper {
  margin-top: 4rem;
}

.data-consent-wrapper label {
  margin-bottom: 1rem;
}

.guest-menu-header-link>h4 {
  background: none;
  border: none;
  font-family: "Avenir Medium", Georgia, Times, serif;
  letter-spacing: .12em;
  color: #466565;
  font-weight: 400;
  text-transform: uppercase;
}

.guest-menu .occasion-name {
  background: none;
  border: none;
  font-family: "Avenir Medium", Georgia, Times, serif;
  letter-spacing: .12em;
  color: #466565;
  font-weight: 400;
  text-transform: uppercase;
  padding: 0.5rem 0;
}

.rounded {
  border-radius: 0 !important;
}

.footer .location-panel ul>li {
  margin: 1rem;
}

.authenticate-form {
  text-align: center;
}

.authenticate-form>.row:nth-child(n+2) {
  max-width: 560px;
  margin: 0 auto;
}

.label-input[for="surnameForAuthentication"] {
  font-weight: 400;
  margin: 2rem 0;
}



.form-add-attendee .btn.add-send-invitation+.btn {
  margin-right: 0;
}

.guest-summary-menu-choices .text-right.col-4.col-md-3 {
  position: absolute;
  bottom: 3px;
  right: 0;
}

.guest-summary-menu-choices .col-7 {
  -ms-flex: 0 0 83.333333%;
  flex: 0 0 83.333333%;
  max-width: 83.333333%;
}

.guest-summary-menu-choices .col-1 {
  width: 30px !important;
  flex-basis: unset;
}

.guest-summary-menu-choices .col-7 p.pl-1.pr-1.mb-1 {
  padding-left: 1rem !important;
}

.btn.addComment {

  background: #688585;
  border: 2px solid #fff;
font-family: "Avenir Medium", Georgia, Times, serif;
font-size: 1rem;
padding: 0 .5rem !important;
color: #fff;
text-transform: uppercase;
  margin-top: 0.5rem;
}

.btn.addComment:hover {
  text-decoration: none;
  border-color: #545b62;
}

span[title="Price"],
.guest-summary-price-due,
.guest-summary section .container>.row:last-child {
  /*! display: none; */
}

.product-border.mx-3 {
  margin: 1.5rem 0 0 !important;
}

.guest-summary-menu-choices>.no-gutters {
  margin: 0 0 1.5rem 0 !important;
  position: relative;
}

.modal .portion-item .text-right span {
  /*! display: none; */
}

.modal .modal-body .equal-columns.list-group .choice-item-quantity-control.input-group.input-group-sm {
  display: none;
}

.choice-group-title.row {
  align-items: center;
  margin-bottom: 1.5rem;
}

.choice-group-title.row h5 {
  background: none;
  border: none;
  font-family: "Avenir Medium", Georgia, Times, serif;
  letter-spacing: .12em;
  color: #466565;
  font-weight: 400;
  text-transform: uppercase;
  font-size: 1rem;
}

.choice-group:not(:last-child) {
  margin-bottom: 1.5rem;
}

.modal-title {
  background: none;
  border: none;
  font-family: "Avenir Medium", Georgia, Times, serif;
  letter-spacing: .12em;
  color: #466565;
  font-weight: 400;
  text-transform: uppercase;
}

.modal-title small {
  letter-spacing: 1px;
  font-family: "KeplerStd-Regular", Georgia, Times, serif;
  font-size: 1rem;
  line-height: 1.33;
  letter-spacing: 1px;
  text-transform: none;
  color: #282828;
}

.modal-header,
.modal-footer {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  padding: 1.5rem;
  justify-content: space-between;
  border: none;
}

.modal-footer {
  background-color: #e9f4f5;
}

.modal-content {
  border-radius: 0;
}

input[type="checkbox"] {
  border-radius: 0;
}

.guest-summary .border-top.border-bottom.p-2.row {
  border-bottom: none !important;
  background-color: #e9f4f5;
}

a, i {
  color: #688585 !important;
}

.btn-tertiary {
  color: #688585 !important;
  border: 2px solid #fff !important;
}

p {
  line-height: 1.33 !important;
}

.choice-group-title.row .pl-0.pl-md-2.col-4.col-md-3 {
  right: 45px;
}

.alert {
  border-radius: 0;
}

.border {
  border: 1px solid rgba(0,0,0,.125) !important;
}

.guest-menu-collapse > .mb-4.row {
  margin-bottom: 0 !important;
}

.guest-menu-collapse > .mb-4.row::before {
  content: '';
  display: block;
  height: 1px;
  background: rgba(0,0,0,.125);
  width: calc(100% - 32px);
  line-height: 1;
  position: relative;
  margin: 1rem;
}

.guest-menu-collapse .product-border.mx-3 {
  display: none;
}

.guest-menu.col-sm-12.col-lg-5 .rounded.border.menu.mb-3.p-2 .align-items-center.row .col-2.col-sm-2.col-lg-1 {
  position: relative;
  right: 15px;
}

.guest-menu-collapse .alert {
  margin-top: 1.5rem;
}

.modal-body {
  padding: 1.5rem;
}

.guest-menu .rounded.border.menu.mb-3.p-2 {
  padding: 1.5rem !important;
}

.guest-summary .border-top.border-bottom.p-2.row {
  padding: 1.5rem 0 !important;
}

.remove-attendee-modal {
  max-width: 500px;
}

.remove-attendee-modal .modal-footer {
  -ms-justify-content: space-between !important;
  justify-content: space-between !important;
  border-top: none;
}

.form-check .form-check-label {
  font-weight: 400
}

#surnameForAuthentication,
#cancellationReason {
  display: block;
  width: 100%;
  padding: .375rem .75rem;
  font-size: 1rem;
  line-height: 1.5;
  background-color: #fff;
  background-clip: padding-box;
  transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
  border-radius: 0;
  border: 1px solid rgba(0, 0, 0, .125);
  color: #282828;
}

#surnameForAuthentication:focus,
#cancellationReason:focus {
  box-shadow: 0 0 0 .2rem rgba(0, 0, 0, .125);
  border-color: rgba(0, 0, 0, .125);
  outline: none;
}

#surnameForAuthentication {
  width: 300px;
  margin-left: auto;
  margin-right: auto;
}

p.pl-1.pr-1.mb-1 {
  padding-left: 1rem !important;
}

label[for="notes"] {
  display: none;
}

label[for="notes"] + .input-group {
  margin-top: -2rem;
}

.guest-summary-menu-choices:after {
  content: "A discretionary service charge will be added to your bill & fairly distributed among the team who prepared & served your food today. Thank you!";
  font-size: 0.8em;
  font-style: italic;
}

/*
Przemek request from 14.11.2022
- hide 'Unable to make it?' on the main page of the Guest Portal.
- After selecting an attendee and and clicking 'Remove Attendee', the main guest is presented with a choice of two radio buttons:
Could you please hide the second radio button:
'Remove attendee and reduce party size by 1'
*/
/* .event-details .location-panel .cancel-button, */
.remove-attendee-modal .modal-body form .form-check:last-of-type{
  display: none !important;
}

/*
Przemek request from 28.11.2022
Manage Event -> Update Event Detaisl
- hide "Update Party size" group
*/
.event-manage-form .form-group.email + .form-group,
.event-manage-form .form-group.email + .form-group + .form-group {
  display: none;
}
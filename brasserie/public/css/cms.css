@charset "UTF-8";
/*
* This stylesheet is loaded into G<PERSON>nberg editor to mimic app styling for all blocks
* open this folder in terminal and use:
* sass --source-map cms.scss cms.css
*/
@use "sass:math";
:root,
[data-bs-theme=light] {
  --bs-blue: #0d6efd;
  --bs-indigo: #6610f2;
  --bs-purple: #6f42c1;
  --bs-pink: #d63384;
  --bs-red: #EC472E;
  --bs-orange: #fd7e14;
  --bs-yellow: #ffc107;
  --bs-green: #415143;
  --bs-teal: #20c997;
  --bs-cyan: #0dcaf0;
  --bs-black: #28170F;
  --bs-white: #FFF9E6;
  --bs-gray: #6c757d;
  --bs-gray-dark: #343a40;
  --bs-gray-100: #f8f9fa;
  --bs-gray-200: #e9ecef;
  --bs-gray-300: #dee2e6;
  --bs-gray-400: #ced4da;
  --bs-gray-500: #adb5bd;
  --bs-gray-600: #6c757d;
  --bs-gray-700: #495057;
  --bs-gray-800: #343a40;
  --bs-gray-900: #212529;
  --bs-primary: #415143;
  --bs-secondary: #EC472E;
  --bs-success: #415143;
  --bs-info: #6EA394;
  --bs-warning: #6EA394;
  --bs-danger: #EC472E;
  --bs-dark: #28170F;
  --bs-green: #415143;
  --bs-celadon: #6EA394;
  --bs-red: #EC472E;
  --bs-warm: #FFF9E6;
  --bs-summer: #a2a2a2;
  --bs-autumn: #a2a2a2;
  --bs-winter: #a2a2a2;
  --bs-spring: #a2a2a2;
  --bs-primary-rgb: 65, 81, 67;
  --bs-secondary-rgb: 236, 71, 46;
  --bs-success-rgb: 65, 81, 67;
  --bs-info-rgb: 110, 163, 148;
  --bs-warning-rgb: 110, 163, 148;
  --bs-danger-rgb: 236, 71, 46;
  --bs-dark-rgb: 40, 23, 15;
  --bs-green-rgb: 65, 81, 67;
  --bs-celadon-rgb: 110, 163, 148;
  --bs-red-rgb: 236, 71, 46;
  --bs-warm-rgb: 255, 249, 230;
  --bs-summer-rgb: 162, 162, 162;
  --bs-autumn-rgb: 162, 162, 162;
  --bs-winter-rgb: 162, 162, 162;
  --bs-spring-rgb: 162, 162, 162;
  --bs-primary-text-emphasis: #1a201b;
  --bs-secondary-text-emphasis: #5e1c12;
  --bs-success-text-emphasis: #1a201b;
  --bs-info-text-emphasis: #055160;
  --bs-warning-text-emphasis: #664d03;
  --bs-danger-text-emphasis: #5e1c12;
  --bs-light-text-emphasis: #495057;
  --bs-dark-text-emphasis: #495057;
  --bs-primary-bg-subtle: #d9dcd9;
  --bs-secondary-bg-subtle: #fbdad5;
  --bs-success-bg-subtle: #d9dcd9;
  --bs-info-bg-subtle: #cff4fc;
  --bs-warning-bg-subtle: #fff3cd;
  --bs-danger-bg-subtle: #fbdad5;
  --bs-light-bg-subtle: #fcf9f0;
  --bs-dark-bg-subtle: #ced4da;
  --bs-primary-border-subtle: #b3b9b4;
  --bs-secondary-border-subtle: #f7b5ab;
  --bs-success-border-subtle: #b3b9b4;
  --bs-info-border-subtle: #9eeaf9;
  --bs-warning-border-subtle: #ffe69c;
  --bs-danger-border-subtle: #f7b5ab;
  --bs-light-border-subtle: #e9ecef;
  --bs-dark-border-subtle: #adb5bd;
  --bs-white-rgb: 255, 249, 230;
  --bs-black-rgb: 40, 23, 15;
  --bs-font-sans-serif: var(--font-cormorant, "Arial"), Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --bs-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --bs-gradient: linear-gradient(180deg, rgba(255, 249, 230, 0.15), rgba(255, 249, 230, 0));
  --bs-body-font-family: var(--bs-font-sans-serif);
  --bs-body-font-size:1rem;
  --bs-body-font-weight: 500;
  --bs-body-line-height: 1.625;
  --bs-body-color: #415143;
  --bs-body-color-rgb: 65, 81, 67;
  --bs-body-bg: #FFF9E6;
  --bs-body-bg-rgb: 255, 249, 230;
  --bs-emphasis-color: #28170F;
  --bs-emphasis-color-rgb: 40, 23, 15;
  --bs-secondary-color: rgba(65, 81, 67, 0.75);
  --bs-secondary-color-rgb: 65, 81, 67;
  --bs-secondary-bg: #e9ecef;
  --bs-secondary-bg-rgb: 233, 236, 239;
  --bs-tertiary-color: rgba(65, 81, 67, 0.5);
  --bs-tertiary-color-rgb: 65, 81, 67;
  --bs-tertiary-bg: #f8f9fa;
  --bs-tertiary-bg-rgb: 248, 249, 250;
  --bs-heading-color: inherit;
  --bs-link-color: #28170F;
  --bs-link-color-rgb: 40, 23, 15;
  --bs-link-decoration: underline;
  --bs-link-hover-color: #20120c;
  --bs-link-hover-color-rgb: 32, 18, 12;
  --bs-code-color: #d63384;
  --bs-highlight-color: #415143;
  --bs-highlight-bg: #fff3cd;
  --bs-border-width: 1px;
  --bs-border-style: solid;
  --bs-border-color: #dee2e6;
  --bs-border-color-translucent: rgba(40, 23, 15, 0.175);
  --bs-border-radius: 0.375rem;
  --bs-border-radius-sm: 0.25rem;
  --bs-border-radius-lg: 0.5rem;
  --bs-border-radius-xl: 1rem;
  --bs-border-radius-xxl: 2rem;
  --bs-border-radius-2xl: var(--bs-border-radius-xxl);
  --bs-border-radius-pill: 50rem;
  --bs-box-shadow: 0 0.5rem 1rem rgba(40, 23, 15, 0.15);
  --bs-box-shadow-sm: 0 0.125rem 0.25rem rgba(40, 23, 15, 0.075);
  --bs-box-shadow-lg: 0 1rem 3rem rgba(40, 23, 15, 0.175);
  --bs-box-shadow-inset: inset 0 1px 2px rgba(40, 23, 15, 0.075);
  --bs-focus-ring-width: 0.25rem;
  --bs-focus-ring-opacity: 0.25;
  --bs-focus-ring-color: rgba(65, 81, 67, 0.25);
  --bs-form-valid-color: #415143;
  --bs-form-valid-border-color: #415143;
  --bs-form-invalid-color: #EC472E;
  --bs-form-invalid-border-color: #EC472E;
}

[data-bs-theme=dark] {
  color-scheme: dark;
  --bs-body-color: #dee2e6;
  --bs-body-color-rgb: 222, 226, 230;
  --bs-body-bg: #212529;
  --bs-body-bg-rgb: 33, 37, 41;
  --bs-emphasis-color: #FFF9E6;
  --bs-emphasis-color-rgb: 255, 249, 230;
  --bs-secondary-color: rgba(222, 226, 230, 0.75);
  --bs-secondary-color-rgb: 222, 226, 230;
  --bs-secondary-bg: #343a40;
  --bs-secondary-bg-rgb: 52, 58, 64;
  --bs-tertiary-color: rgba(222, 226, 230, 0.5);
  --bs-tertiary-color-rgb: 222, 226, 230;
  --bs-tertiary-bg: #2b3035;
  --bs-tertiary-bg-rgb: 43, 48, 53;
  --bs-primary-text-emphasis: #8d978e;
  --bs-secondary-text-emphasis: #f49182;
  --bs-success-text-emphasis: #8d978e;
  --bs-info-text-emphasis: #6edff6;
  --bs-warning-text-emphasis: #ffda6a;
  --bs-danger-text-emphasis: #f49182;
  --bs-light-text-emphasis: #f8f9fa;
  --bs-dark-text-emphasis: #dee2e6;
  --bs-primary-bg-subtle: #0d100d;
  --bs-secondary-bg-subtle: #2f0e09;
  --bs-success-bg-subtle: #0d100d;
  --bs-info-bg-subtle: #032830;
  --bs-warning-bg-subtle: #332701;
  --bs-danger-bg-subtle: #2f0e09;
  --bs-light-bg-subtle: #343a40;
  --bs-dark-bg-subtle: #2e2928;
  --bs-primary-border-subtle: #273128;
  --bs-secondary-border-subtle: #8e2b1c;
  --bs-success-border-subtle: #273128;
  --bs-info-border-subtle: #087990;
  --bs-warning-border-subtle: #997404;
  --bs-danger-border-subtle: #8e2b1c;
  --bs-light-border-subtle: #495057;
  --bs-dark-border-subtle: #343a40;
  --bs-heading-color: inherit;
  --bs-link-color: #8d978e;
  --bs-link-hover-color: #a4aca5;
  --bs-link-color-rgb: 141, 151, 142;
  --bs-link-hover-color-rgb: 164, 172, 165;
  --bs-code-color: #e685b5;
  --bs-highlight-color: #dee2e6;
  --bs-highlight-bg: #664d03;
  --bs-border-color: #495057;
  --bs-border-color-translucent: rgba(255, 249, 230, 0.15);
  --bs-form-valid-color: #8d978e;
  --bs-form-valid-border-color: #8d978e;
  --bs-form-invalid-color: #f49182;
  --bs-form-invalid-border-color: #f49182;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

@media (prefers-reduced-motion: no-preference) {
  :root {
    scroll-behavior: smooth;
  }
}

body {
  margin: 0;
  font-family: var(--bs-body-font-family);
  font-size: var(--bs-body-font-size);
  font-weight: var(--bs-body-font-weight);
  line-height: var(--bs-body-line-height);
  color: var(--bs-body-color);
  text-align: var(--bs-body-text-align);
  background-color: var(--bs-body-bg);
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(40, 23, 15, 0);
}

hr {
  margin: 0.625rem 0;
  color: inherit;
  border: 0;
  border-top: var(--bs-border-width) solid;
  opacity: 0.25;
}

h6, .h6, h5, .h5, h4, .h4, h3, .h3, h2, .h2, h1, .h1 {
  margin-top: 0;
  margin-bottom: 0.625rem;
  font-weight: 400;
  line-height: 1.2;
  color: var(--bs-heading-color);
}

h1, .h1 {
  font-size: calc(1.35rem + 1.2vw);
}
@media (min-width: 1200px) {
  h1, .h1 {
    font-size: 2.25rem;
  }
}

h2, .h2 {
  font-size: calc(1.25625rem + 0.075vw);
}
@media (min-width: 1200px) {
  h2, .h2 {
    font-size: 1.3125rem;
  }
}

h3, .h3 {
  font-size: 1.0625rem;
}

h4, .h4 {
  font-size: 1.0625rem;
}

h5, .h5 {
  font-size: 1.0625rem;
}

h6, .h6 {
  font-size: 1rem;
}

p {
  margin-top: 0;
  margin-bottom: 1.5rem;
}

abbr[title] {
  text-decoration: underline dotted;
  cursor: help;
  text-decoration-skip-ink: none;
}

address {
  margin-bottom: 1rem;
  font-style: normal;
  line-height: inherit;
}

ol,
ul {
  padding-left: 2rem;
}

ol,
ul,
dl {
  margin-top: 0;
  margin-bottom: 1rem;
}

ol ol,
ul ul,
ol ul,
ul ol {
  margin-bottom: 0;
}

dt {
  font-weight: 700;
}

dd {
  margin-bottom: 0.5rem;
  margin-left: 0;
}

blockquote {
  margin: 0 0 1rem;
}

b,
strong {
  font-weight: bolder;
}

small, .small {
  font-size: 0.75rem;
}

mark, .mark {
  padding: 0.1875em;
  color: var(--bs-highlight-color);
  background-color: var(--bs-highlight-bg);
}

sub,
sup {
  position: relative;
  font-size: 0.75em;
  line-height: 0;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

a {
  color: rgba(var(--bs-link-color-rgb), var(--bs-link-opacity, 1));
  text-decoration: underline;
}
a:hover {
  --bs-link-color-rgb: var(--bs-link-hover-color-rgb);
}

a:not([href]):not([class]), a:not([href]):not([class]):hover {
  color: inherit;
  text-decoration: none;
}

pre,
code,
kbd,
samp {
  font-family: var(--bs-font-monospace);
  font-size: 1em;
}

pre {
  display: block;
  margin-top: 0;
  margin-bottom: 1rem;
  overflow: auto;
  font-size: 0.75rem;
}
pre code {
  font-size: inherit;
  color: inherit;
  word-break: normal;
}

code {
  font-size: 0.75rem;
  color: var(--bs-code-color);
  word-wrap: break-word;
}
a > code {
  color: inherit;
}

kbd {
  padding: 0.1875rem 0.375rem;
  font-size: 0.75rem;
  color: var(--bs-body-bg);
  background-color: var(--bs-body-color);
  border-radius: 0.25rem;
}
kbd kbd {
  padding: 0;
  font-size: 1em;
}

figure {
  margin: 0 0 1rem;
}

img,
svg {
  vertical-align: middle;
}

table {
  caption-side: bottom;
  border-collapse: collapse;
}

caption {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  color: var(--bs-secondary-color);
  text-align: left;
}

th {
  text-align: inherit;
  text-align: -webkit-match-parent;
}

thead,
tbody,
tfoot,
tr,
td,
th {
  border-color: inherit;
  border-style: solid;
  border-width: 0;
}

label {
  display: inline-block;
}

button {
  border-radius: 0;
}

button:focus:not(:focus-visible) {
  outline: 0;
}

input,
button,
select,
optgroup,
textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

button,
select {
  text-transform: none;
}

[role=button] {
  cursor: pointer;
}

select {
  word-wrap: normal;
}
select:disabled {
  opacity: 1;
}

[list]:not([type=date]):not([type=datetime-local]):not([type=month]):not([type=week]):not([type=time])::-webkit-calendar-picker-indicator {
  display: none !important;
}

button,
[type=button],
[type=reset],
[type=submit] {
  -webkit-appearance: button;
}
button:not(:disabled),
[type=button]:not(:disabled),
[type=reset]:not(:disabled),
[type=submit]:not(:disabled) {
  cursor: pointer;
}

::-moz-focus-inner {
  padding: 0;
  border-style: none;
}

textarea {
  resize: vertical;
}

fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
}

legend {
  float: left;
  width: 100%;
  padding: 0;
  margin-bottom: 0.5rem;
  line-height: inherit;
  font-size: calc(1.275rem + 0.3vw);
}
@media (min-width: 1200px) {
  legend {
    font-size: 1.5rem;
  }
}
legend + * {
  clear: left;
}

::-webkit-datetime-edit-fields-wrapper,
::-webkit-datetime-edit-text,
::-webkit-datetime-edit-minute,
::-webkit-datetime-edit-hour-field,
::-webkit-datetime-edit-day-field,
::-webkit-datetime-edit-month-field,
::-webkit-datetime-edit-year-field {
  padding: 0;
}

::-webkit-inner-spin-button {
  height: auto;
}

[type=search] {
  -webkit-appearance: textfield;
  outline-offset: -2px;
}
[type=search]::-webkit-search-cancel-button {
  cursor: pointer;
  filter: grayscale(1);
}

/* rtl:raw:
[type="tel"],
[type="url"],
[type="email"],
[type="number"] {
  direction: ltr;
}
*/
::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-color-swatch-wrapper {
  padding: 0;
}

::file-selector-button {
  font: inherit;
  -webkit-appearance: button;
}

output {
  display: inline-block;
}

iframe {
  border: 0;
}

summary {
  display: list-item;
  cursor: pointer;
}

progress {
  vertical-align: baseline;
}

[hidden] {
  display: none !important;
}

.lead {
  font-size: 1.0625rem;
  font-weight: 600;
}

.display-1 {
  font-weight: 300;
  line-height: 1.2;
  font-size: calc(1.625rem + 4.5vw);
}
@media (min-width: 1200px) {
  .display-1 {
    font-size: 5rem;
  }
}

.display-2 {
  font-weight: 300;
  line-height: 1.2;
  font-size: calc(1.575rem + 3.9vw);
}
@media (min-width: 1200px) {
  .display-2 {
    font-size: 4.5rem;
  }
}

.display-3 {
  font-weight: 300;
  line-height: 1.2;
  font-size: calc(1.525rem + 3.3vw);
}
@media (min-width: 1200px) {
  .display-3 {
    font-size: 4rem;
  }
}

.display-4 {
  font-weight: 300;
  line-height: 1.2;
  font-size: calc(1.475rem + 2.7vw);
}
@media (min-width: 1200px) {
  .display-4 {
    font-size: 3.5rem;
  }
}

.display-5 {
  font-weight: 300;
  line-height: 1.2;
  font-size: calc(1.425rem + 2.1vw);
}
@media (min-width: 1200px) {
  .display-5 {
    font-size: 3rem;
  }
}

.display-6 {
  font-weight: 300;
  line-height: 1.2;
  font-size: calc(1.375rem + 1.5vw);
}
@media (min-width: 1200px) {
  .display-6 {
    font-size: 2.5rem;
  }
}

.list-unstyled {
  padding-left: 0;
  list-style: none;
}

.list-inline {
  padding-left: 0;
  list-style: none;
}

.list-inline-item {
  display: inline-block;
}
.list-inline-item:not(:last-child) {
  margin-right: 0.5rem;
}

.initialism {
  font-size: 0.75rem;
  text-transform: uppercase;
}

.blockquote {
  margin-bottom: 0.625rem;
  font-size: 1.25rem;
}
.blockquote > :last-child {
  margin-bottom: 0;
}

.blockquote-footer {
  margin-top: -0.625rem;
  margin-bottom: 0.625rem;
  font-size: 0.75rem;
  color: #6c757d;
}
.blockquote-footer::before {
  content: "— ";
}

.img-fluid {
  max-width: 100%;
  height: auto;
}

.img-thumbnail {
  padding: 0.25rem;
  background-color: var(--bs-body-bg);
  border: var(--bs-border-width) solid var(--bs-border-color);
  border-radius: var(--bs-border-radius);
  max-width: 100%;
  height: auto;
}

.figure {
  display: inline-block;
}

.figure-img {
  margin-bottom: 0.3125rem;
  line-height: 1;
}

.figure-caption {
  font-size: 0.75rem;
  color: var(--bs-secondary-color);
}

.container,
.container-fluid,
.container-xxl,
.container-xl,
.container-lg,
.container-md,
.container-sm,
.container-xs {
  --bs-gutter-x: 1.25rem;
  --bs-gutter-y: 0;
  width: 100%;
  padding-right: calc(var(--bs-gutter-x) * .5);
  padding-left: calc(var(--bs-gutter-x) * .5);
  margin-right: auto;
  margin-left: auto;
}

.container {
  max-width: 100%;
}

@media (min-width: 576px) {
  .container-sm, .container {
    max-width: 500px;
  }
}
@media (min-width: 768px) {
  .container-md, .container-sm, .container {
    max-width: 738px;
  }
}
@media (min-width: 992px) {
  .container-lg, .container-md, .container-sm, .container {
    max-width: 932px;
  }
}
@media (min-width: 1200px) {
  .container-xl, .container-lg, .container-md, .container-sm, .container {
    max-width: 1140px;
  }
}
@media (min-width: 1400px) {
  .container-xxl, .container-xl, .container-lg, .container-md, .container-sm, .container {
    max-width: 1320px;
  }
}
:root {
  --bs-breakpoint-xs: 0;
  --bs-breakpoint-sm: 576px;
  --bs-breakpoint-md: 768px;
  --bs-breakpoint-lg: 992px;
  --bs-breakpoint-xl: 1200px;
  --bs-breakpoint-xxl: 1400px;
}

.row {
  --bs-gutter-x: 1.25rem;
  --bs-gutter-y: 0;
  display: flex;
  flex-wrap: wrap;
  margin-top: calc(-1 * var(--bs-gutter-y));
  margin-right: calc(-.5 * var(--bs-gutter-x));
  margin-left: calc(-.5 * var(--bs-gutter-x));
}
.row > * {
  flex-shrink: 0;
  width: 100%;
  max-width: 100%;
  padding-right: calc(var(--bs-gutter-x) * .5);
  padding-left: calc(var(--bs-gutter-x) * .5);
  margin-top: var(--bs-gutter-y);
}

.col {
  flex: 1 0 0;
}

.row-cols-auto > * {
  flex: 0 0 auto;
  width: auto;
}

.row-cols-1 > * {
  flex: 0 0 auto;
  width: 100%;
}

.row-cols-2 > * {
  flex: 0 0 auto;
  width: 50%;
}

.row-cols-3 > * {
  flex: 0 0 auto;
  width: 33.33333333%;
}

.row-cols-4 > * {
  flex: 0 0 auto;
  width: 25%;
}

.row-cols-5 > * {
  flex: 0 0 auto;
  width: 20%;
}

.row-cols-6 > * {
  flex: 0 0 auto;
  width: 16.66666667%;
}

.col-auto {
  flex: 0 0 auto;
  width: auto;
}

.col-1 {
  flex: 0 0 auto;
  width: 8.33333333%;
}

.col-2 {
  flex: 0 0 auto;
  width: 16.66666667%;
}

.col-3 {
  flex: 0 0 auto;
  width: 25%;
}

.col-4 {
  flex: 0 0 auto;
  width: 33.33333333%;
}

.col-5 {
  flex: 0 0 auto;
  width: 41.66666667%;
}

.col-6, .wp-block-gallery .wp-block-image:not([class*=col-]):nth-child(5n+2), .wp-block-gallery .wp-block-image:not([class*=col-]):nth-child(5n+3),
.wp-block-gallery .wp-block-video:not([class*=col-]):nth-child(5n+2),
.wp-block-gallery .wp-block-video:not([class*=col-]):nth-child(5n+3),
.wp-block-gallery .innerblocks .wp-block-image:not([class*=col-]):nth-child(5n+2),
.wp-block-gallery .innerblocks .wp-block-image:not([class*=col-]):nth-child(5n+3),
.wp-block-gallery .innerblocks .wp-block-video:not([class*=col-]):nth-child(5n+2),
.wp-block-gallery .innerblocks .wp-block-video:not([class*=col-]):nth-child(5n+3) {
  flex: 0 0 auto;
  width: 50%;
}

.col-7 {
  flex: 0 0 auto;
  width: 58.33333333%;
}

.col-8 {
  flex: 0 0 auto;
  width: 66.66666667%;
}

.col-9 {
  flex: 0 0 auto;
  width: 75%;
}

.col-10 {
  flex: 0 0 auto;
  width: 83.33333333%;
}

.col-11 {
  flex: 0 0 auto;
  width: 91.66666667%;
}

.col-12, .wp-block-gallery .wp-block-image:not([class*=col-]):nth-child(5n),
.wp-block-gallery .wp-block-video:not([class*=col-]):nth-child(5n),
.wp-block-gallery .innerblocks .wp-block-image:not([class*=col-]):nth-child(5n),
.wp-block-gallery .innerblocks .wp-block-video:not([class*=col-]):nth-child(5n), .wp-block-gallery .wp-block-image:not([class*=col-]):nth-child(5n+4),
.wp-block-gallery .wp-block-video:not([class*=col-]):nth-child(5n+4),
.wp-block-gallery .innerblocks .wp-block-image:not([class*=col-]):nth-child(5n+4),
.wp-block-gallery .innerblocks .wp-block-video:not([class*=col-]):nth-child(5n+4), .wp-block-gallery .wp-block-image:not([class*=col-]):nth-child(5n+1),
.wp-block-gallery .wp-block-video:not([class*=col-]):nth-child(5n+1),
.wp-block-gallery .innerblocks .wp-block-image:not([class*=col-]):nth-child(5n+1),
.wp-block-gallery .innerblocks .wp-block-video:not([class*=col-]):nth-child(5n+1) {
  flex: 0 0 auto;
  width: 100%;
}

.offset-1 {
  margin-left: 8.33333333%;
}

.offset-2 {
  margin-left: 16.66666667%;
}

.offset-3 {
  margin-left: 25%;
}

.offset-4 {
  margin-left: 33.33333333%;
}

.offset-5 {
  margin-left: 41.66666667%;
}

.offset-6 {
  margin-left: 50%;
}

.offset-7 {
  margin-left: 58.33333333%;
}

.offset-8 {
  margin-left: 66.66666667%;
}

.offset-9 {
  margin-left: 75%;
}

.offset-10 {
  margin-left: 83.33333333%;
}

.offset-11 {
  margin-left: 91.66666667%;
}

.g-0,
.gx-0 {
  --bs-gutter-x: 0;
}

.g-0,
.gy-0 {
  --bs-gutter-y: 0;
}

.g-5,
.gx-5 {
  --bs-gutter-x: 0.3125rem;
}

.g-5,
.gy-5 {
  --bs-gutter-y: 0.3125rem;
}

.g-10,
.gx-10 {
  --bs-gutter-x: 0.625rem;
}

.g-10,
.gy-10 {
  --bs-gutter-y: 0.625rem;
}

.g-15,
.gx-15 {
  --bs-gutter-x: 0.9375rem;
}

.g-15,
.gy-15 {
  --bs-gutter-y: 0.9375rem;
}

.g-20,
.gx-20 {
  --bs-gutter-x: 1.25rem;
}

.g-20,
.gy-20 {
  --bs-gutter-y: 1.25rem;
}

.g-25,
.gx-25 {
  --bs-gutter-x: 1.5625rem;
}

.g-25,
.gy-25 {
  --bs-gutter-y: 1.5625rem;
}

.g-30,
.gx-30 {
  --bs-gutter-x: 1.875rem;
}

.g-30,
.gy-30 {
  --bs-gutter-y: 1.875rem;
}

.g-40,
.gx-40 {
  --bs-gutter-x: 2.5rem;
}

.g-40,
.gy-40 {
  --bs-gutter-y: 2.5rem;
}

.g-50,
.gx-50 {
  --bs-gutter-x: 3.125rem;
}

.g-50,
.gy-50 {
  --bs-gutter-y: 3.125rem;
}

.g-70,
.gx-70 {
  --bs-gutter-x: 4.375rem;
}

.g-70,
.gy-70 {
  --bs-gutter-y: 4.375rem;
}

.g-80,
.gx-80 {
  --bs-gutter-x: 5rem;
}

.g-80,
.gy-80 {
  --bs-gutter-y: 5rem;
}

.g-100,
.gx-100 {
  --bs-gutter-x: 6.25rem;
}

.g-100,
.gy-100 {
  --bs-gutter-y: 6.25rem;
}

.g-135,
.gx-135 {
  --bs-gutter-x: 8.4375rem;
}

.g-135,
.gy-135 {
  --bs-gutter-y: 8.4375rem;
}

.g-150,
.gx-150 {
  --bs-gutter-x: 9.375rem;
}

.g-150,
.gy-150 {
  --bs-gutter-y: 9.375rem;
}

@media (min-width: 576px) {
  .col-sm {
    flex: 1 0 0;
  }

  .row-cols-sm-auto > * {
    flex: 0 0 auto;
    width: auto;
  }

  .row-cols-sm-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }

  .row-cols-sm-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }

  .row-cols-sm-3 > * {
    flex: 0 0 auto;
    width: 33.33333333%;
  }

  .row-cols-sm-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }

  .row-cols-sm-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }

  .row-cols-sm-6 > * {
    flex: 0 0 auto;
    width: 16.66666667%;
  }

  .col-sm-auto {
    flex: 0 0 auto;
    width: auto;
  }

  .col-sm-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }

  .col-sm-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }

  .col-sm-3 {
    flex: 0 0 auto;
    width: 25%;
  }

  .col-sm-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }

  .col-sm-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }

  .col-sm-6 {
    flex: 0 0 auto;
    width: 50%;
  }

  .col-sm-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }

  .col-sm-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }

  .col-sm-9 {
    flex: 0 0 auto;
    width: 75%;
  }

  .col-sm-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }

  .col-sm-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }

  .col-sm-12 {
    flex: 0 0 auto;
    width: 100%;
  }

  .offset-sm-0 {
    margin-left: 0;
  }

  .offset-sm-1 {
    margin-left: 8.33333333%;
  }

  .offset-sm-2 {
    margin-left: 16.66666667%;
  }

  .offset-sm-3 {
    margin-left: 25%;
  }

  .offset-sm-4 {
    margin-left: 33.33333333%;
  }

  .offset-sm-5 {
    margin-left: 41.66666667%;
  }

  .offset-sm-6 {
    margin-left: 50%;
  }

  .offset-sm-7 {
    margin-left: 58.33333333%;
  }

  .offset-sm-8 {
    margin-left: 66.66666667%;
  }

  .offset-sm-9 {
    margin-left: 75%;
  }

  .offset-sm-10 {
    margin-left: 83.33333333%;
  }

  .offset-sm-11 {
    margin-left: 91.66666667%;
  }

  .g-sm-0,
.gx-sm-0 {
    --bs-gutter-x: 0;
  }

  .g-sm-0,
.gy-sm-0 {
    --bs-gutter-y: 0;
  }

  .g-sm-5,
.gx-sm-5 {
    --bs-gutter-x: 0.3125rem;
  }

  .g-sm-5,
.gy-sm-5 {
    --bs-gutter-y: 0.3125rem;
  }

  .g-sm-10,
.gx-sm-10 {
    --bs-gutter-x: 0.625rem;
  }

  .g-sm-10,
.gy-sm-10 {
    --bs-gutter-y: 0.625rem;
  }

  .g-sm-15,
.gx-sm-15 {
    --bs-gutter-x: 0.9375rem;
  }

  .g-sm-15,
.gy-sm-15 {
    --bs-gutter-y: 0.9375rem;
  }

  .g-sm-20,
.gx-sm-20 {
    --bs-gutter-x: 1.25rem;
  }

  .g-sm-20,
.gy-sm-20 {
    --bs-gutter-y: 1.25rem;
  }

  .g-sm-25,
.gx-sm-25 {
    --bs-gutter-x: 1.5625rem;
  }

  .g-sm-25,
.gy-sm-25 {
    --bs-gutter-y: 1.5625rem;
  }

  .g-sm-30,
.gx-sm-30 {
    --bs-gutter-x: 1.875rem;
  }

  .g-sm-30,
.gy-sm-30 {
    --bs-gutter-y: 1.875rem;
  }

  .g-sm-40,
.gx-sm-40 {
    --bs-gutter-x: 2.5rem;
  }

  .g-sm-40,
.gy-sm-40 {
    --bs-gutter-y: 2.5rem;
  }

  .g-sm-50,
.gx-sm-50 {
    --bs-gutter-x: 3.125rem;
  }

  .g-sm-50,
.gy-sm-50 {
    --bs-gutter-y: 3.125rem;
  }

  .g-sm-70,
.gx-sm-70 {
    --bs-gutter-x: 4.375rem;
  }

  .g-sm-70,
.gy-sm-70 {
    --bs-gutter-y: 4.375rem;
  }

  .g-sm-80,
.gx-sm-80 {
    --bs-gutter-x: 5rem;
  }

  .g-sm-80,
.gy-sm-80 {
    --bs-gutter-y: 5rem;
  }

  .g-sm-100,
.gx-sm-100 {
    --bs-gutter-x: 6.25rem;
  }

  .g-sm-100,
.gy-sm-100 {
    --bs-gutter-y: 6.25rem;
  }

  .g-sm-135,
.gx-sm-135 {
    --bs-gutter-x: 8.4375rem;
  }

  .g-sm-135,
.gy-sm-135 {
    --bs-gutter-y: 8.4375rem;
  }

  .g-sm-150,
.gx-sm-150 {
    --bs-gutter-x: 9.375rem;
  }

  .g-sm-150,
.gy-sm-150 {
    --bs-gutter-y: 9.375rem;
  }
}
@media (min-width: 768px) {
  .col-md {
    flex: 1 0 0;
  }

  .row-cols-md-auto > * {
    flex: 0 0 auto;
    width: auto;
  }

  .row-cols-md-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }

  .row-cols-md-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }

  .row-cols-md-3 > * {
    flex: 0 0 auto;
    width: 33.33333333%;
  }

  .row-cols-md-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }

  .row-cols-md-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }

  .row-cols-md-6 > * {
    flex: 0 0 auto;
    width: 16.66666667%;
  }

  .col-md-auto {
    flex: 0 0 auto;
    width: auto;
  }

  .col-md-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }

  .col-md-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }

  .col-md-3, .wp-block-gallery .wp-block-image:not([class*=col-]):nth-child(5n+2), .wp-block-gallery .wp-block-image:not([class*=col-]):nth-child(5n+3),
.wp-block-gallery .wp-block-video:not([class*=col-]):nth-child(5n+2),
.wp-block-gallery .wp-block-video:not([class*=col-]):nth-child(5n+3),
.wp-block-gallery .innerblocks .wp-block-image:not([class*=col-]):nth-child(5n+2),
.wp-block-gallery .innerblocks .wp-block-image:not([class*=col-]):nth-child(5n+3),
.wp-block-gallery .innerblocks .wp-block-video:not([class*=col-]):nth-child(5n+2),
.wp-block-gallery .innerblocks .wp-block-video:not([class*=col-]):nth-child(5n+3) {
    flex: 0 0 auto;
    width: 25%;
  }

  .col-md-4, .wp-block-gallery .wp-block-image:not([class*=col-]):nth-child(5n+4),
.wp-block-gallery .wp-block-video:not([class*=col-]):nth-child(5n+4),
.wp-block-gallery .innerblocks .wp-block-image:not([class*=col-]):nth-child(5n+4),
.wp-block-gallery .innerblocks .wp-block-video:not([class*=col-]):nth-child(5n+4) {
    flex: 0 0 auto;
    width: 33.33333333%;
  }

  .col-md-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }

  .col-md-6, .wp-block-gallery .wp-block-image:not([class*=col-]):nth-child(5n+1),
.wp-block-gallery .wp-block-video:not([class*=col-]):nth-child(5n+1),
.wp-block-gallery .innerblocks .wp-block-image:not([class*=col-]):nth-child(5n+1),
.wp-block-gallery .innerblocks .wp-block-video:not([class*=col-]):nth-child(5n+1) {
    flex: 0 0 auto;
    width: 50%;
  }

  .col-md-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }

  .col-md-8, .wp-block-gallery .wp-block-image:not([class*=col-]):nth-child(5n),
.wp-block-gallery .wp-block-video:not([class*=col-]):nth-child(5n),
.wp-block-gallery .innerblocks .wp-block-image:not([class*=col-]):nth-child(5n),
.wp-block-gallery .innerblocks .wp-block-video:not([class*=col-]):nth-child(5n) {
    flex: 0 0 auto;
    width: 66.66666667%;
  }

  .col-md-9 {
    flex: 0 0 auto;
    width: 75%;
  }

  .col-md-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }

  .col-md-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }

  .col-md-12 {
    flex: 0 0 auto;
    width: 100%;
  }

  .offset-md-0 {
    margin-left: 0;
  }

  .offset-md-1 {
    margin-left: 8.33333333%;
  }

  .offset-md-2 {
    margin-left: 16.66666667%;
  }

  .offset-md-3 {
    margin-left: 25%;
  }

  .offset-md-4 {
    margin-left: 33.33333333%;
  }

  .offset-md-5 {
    margin-left: 41.66666667%;
  }

  .offset-md-6 {
    margin-left: 50%;
  }

  .offset-md-7 {
    margin-left: 58.33333333%;
  }

  .offset-md-8 {
    margin-left: 66.66666667%;
  }

  .offset-md-9 {
    margin-left: 75%;
  }

  .offset-md-10 {
    margin-left: 83.33333333%;
  }

  .offset-md-11 {
    margin-left: 91.66666667%;
  }

  .g-md-0,
.gx-md-0 {
    --bs-gutter-x: 0;
  }

  .g-md-0,
.gy-md-0 {
    --bs-gutter-y: 0;
  }

  .g-md-5,
.gx-md-5 {
    --bs-gutter-x: 0.3125rem;
  }

  .g-md-5,
.gy-md-5 {
    --bs-gutter-y: 0.3125rem;
  }

  .g-md-10,
.gx-md-10 {
    --bs-gutter-x: 0.625rem;
  }

  .g-md-10,
.gy-md-10 {
    --bs-gutter-y: 0.625rem;
  }

  .g-md-15,
.gx-md-15 {
    --bs-gutter-x: 0.9375rem;
  }

  .g-md-15,
.gy-md-15 {
    --bs-gutter-y: 0.9375rem;
  }

  .g-md-20,
.gx-md-20 {
    --bs-gutter-x: 1.25rem;
  }

  .g-md-20,
.gy-md-20 {
    --bs-gutter-y: 1.25rem;
  }

  .g-md-25,
.gx-md-25 {
    --bs-gutter-x: 1.5625rem;
  }

  .g-md-25,
.gy-md-25 {
    --bs-gutter-y: 1.5625rem;
  }

  .g-md-30,
.gx-md-30 {
    --bs-gutter-x: 1.875rem;
  }

  .g-md-30,
.gy-md-30 {
    --bs-gutter-y: 1.875rem;
  }

  .g-md-40,
.gx-md-40 {
    --bs-gutter-x: 2.5rem;
  }

  .g-md-40,
.gy-md-40 {
    --bs-gutter-y: 2.5rem;
  }

  .g-md-50,
.gx-md-50 {
    --bs-gutter-x: 3.125rem;
  }

  .g-md-50,
.gy-md-50 {
    --bs-gutter-y: 3.125rem;
  }

  .g-md-70,
.gx-md-70 {
    --bs-gutter-x: 4.375rem;
  }

  .g-md-70,
.gy-md-70 {
    --bs-gutter-y: 4.375rem;
  }

  .g-md-80,
.gx-md-80 {
    --bs-gutter-x: 5rem;
  }

  .g-md-80,
.gy-md-80 {
    --bs-gutter-y: 5rem;
  }

  .g-md-100,
.gx-md-100 {
    --bs-gutter-x: 6.25rem;
  }

  .g-md-100,
.gy-md-100 {
    --bs-gutter-y: 6.25rem;
  }

  .g-md-135,
.gx-md-135 {
    --bs-gutter-x: 8.4375rem;
  }

  .g-md-135,
.gy-md-135 {
    --bs-gutter-y: 8.4375rem;
  }

  .g-md-150,
.gx-md-150 {
    --bs-gutter-x: 9.375rem;
  }

  .g-md-150,
.gy-md-150 {
    --bs-gutter-y: 9.375rem;
  }
}
@media (min-width: 992px) {
  .col-lg {
    flex: 1 0 0;
  }

  .row-cols-lg-auto > * {
    flex: 0 0 auto;
    width: auto;
  }

  .row-cols-lg-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }

  .row-cols-lg-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }

  .row-cols-lg-3 > * {
    flex: 0 0 auto;
    width: 33.33333333%;
  }

  .row-cols-lg-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }

  .row-cols-lg-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }

  .row-cols-lg-6 > * {
    flex: 0 0 auto;
    width: 16.66666667%;
  }

  .col-lg-auto {
    flex: 0 0 auto;
    width: auto;
  }

  .col-lg-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }

  .col-lg-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }

  .col-lg-3 {
    flex: 0 0 auto;
    width: 25%;
  }

  .col-lg-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }

  .col-lg-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }

  .col-lg-6 {
    flex: 0 0 auto;
    width: 50%;
  }

  .col-lg-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }

  .col-lg-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }

  .col-lg-9 {
    flex: 0 0 auto;
    width: 75%;
  }

  .col-lg-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }

  .col-lg-11, .wp-block-video.edge-2-edge.has-caption, .wp-block-video.has-caption {
    flex: 0 0 auto;
    width: 91.66666667%;
  }

  .col-lg-12 {
    flex: 0 0 auto;
    width: 100%;
  }

  .offset-lg-0 {
    margin-left: 0;
  }

  .offset-lg-1 {
    margin-left: 8.33333333%;
  }

  .offset-lg-2 {
    margin-left: 16.66666667%;
  }

  .offset-lg-3 {
    margin-left: 25%;
  }

  .offset-lg-4 {
    margin-left: 33.33333333%;
  }

  .offset-lg-5 {
    margin-left: 41.66666667%;
  }

  .offset-lg-6 {
    margin-left: 50%;
  }

  .offset-lg-7 {
    margin-left: 58.33333333%;
  }

  .offset-lg-8 {
    margin-left: 66.66666667%;
  }

  .offset-lg-9 {
    margin-left: 75%;
  }

  .offset-lg-10 {
    margin-left: 83.33333333%;
  }

  .offset-lg-11 {
    margin-left: 91.66666667%;
  }

  .g-lg-0,
.gx-lg-0 {
    --bs-gutter-x: 0;
  }

  .g-lg-0,
.gy-lg-0 {
    --bs-gutter-y: 0;
  }

  .g-lg-5,
.gx-lg-5 {
    --bs-gutter-x: 0.3125rem;
  }

  .g-lg-5,
.gy-lg-5 {
    --bs-gutter-y: 0.3125rem;
  }

  .g-lg-10,
.gx-lg-10 {
    --bs-gutter-x: 0.625rem;
  }

  .g-lg-10,
.gy-lg-10 {
    --bs-gutter-y: 0.625rem;
  }

  .g-lg-15,
.gx-lg-15 {
    --bs-gutter-x: 0.9375rem;
  }

  .g-lg-15,
.gy-lg-15 {
    --bs-gutter-y: 0.9375rem;
  }

  .g-lg-20,
.gx-lg-20 {
    --bs-gutter-x: 1.25rem;
  }

  .g-lg-20,
.gy-lg-20 {
    --bs-gutter-y: 1.25rem;
  }

  .g-lg-25,
.gx-lg-25 {
    --bs-gutter-x: 1.5625rem;
  }

  .g-lg-25,
.gy-lg-25 {
    --bs-gutter-y: 1.5625rem;
  }

  .g-lg-30,
.gx-lg-30 {
    --bs-gutter-x: 1.875rem;
  }

  .g-lg-30,
.gy-lg-30 {
    --bs-gutter-y: 1.875rem;
  }

  .g-lg-40,
.gx-lg-40 {
    --bs-gutter-x: 2.5rem;
  }

  .g-lg-40,
.gy-lg-40 {
    --bs-gutter-y: 2.5rem;
  }

  .g-lg-50,
.gx-lg-50 {
    --bs-gutter-x: 3.125rem;
  }

  .g-lg-50,
.gy-lg-50 {
    --bs-gutter-y: 3.125rem;
  }

  .g-lg-70,
.gx-lg-70 {
    --bs-gutter-x: 4.375rem;
  }

  .g-lg-70,
.gy-lg-70 {
    --bs-gutter-y: 4.375rem;
  }

  .g-lg-80,
.gx-lg-80 {
    --bs-gutter-x: 5rem;
  }

  .g-lg-80,
.gy-lg-80 {
    --bs-gutter-y: 5rem;
  }

  .g-lg-100,
.gx-lg-100 {
    --bs-gutter-x: 6.25rem;
  }

  .g-lg-100,
.gy-lg-100 {
    --bs-gutter-y: 6.25rem;
  }

  .g-lg-135,
.gx-lg-135 {
    --bs-gutter-x: 8.4375rem;
  }

  .g-lg-135,
.gy-lg-135 {
    --bs-gutter-y: 8.4375rem;
  }

  .g-lg-150,
.gx-lg-150 {
    --bs-gutter-x: 9.375rem;
  }

  .g-lg-150,
.gy-lg-150 {
    --bs-gutter-y: 9.375rem;
  }
}
@media (min-width: 1200px) {
  .col-xl {
    flex: 1 0 0;
  }

  .row-cols-xl-auto > * {
    flex: 0 0 auto;
    width: auto;
  }

  .row-cols-xl-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }

  .row-cols-xl-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }

  .row-cols-xl-3 > * {
    flex: 0 0 auto;
    width: 33.33333333%;
  }

  .row-cols-xl-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }

  .row-cols-xl-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }

  .row-cols-xl-6 > * {
    flex: 0 0 auto;
    width: 16.66666667%;
  }

  .col-xl-auto {
    flex: 0 0 auto;
    width: auto;
  }

  .col-xl-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }

  .col-xl-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }

  .col-xl-3 {
    flex: 0 0 auto;
    width: 25%;
  }

  .col-xl-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }

  .col-xl-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }

  .col-xl-6 {
    flex: 0 0 auto;
    width: 50%;
  }

  .col-xl-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }

  .col-xl-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }

  .col-xl-9 {
    flex: 0 0 auto;
    width: 75%;
  }

  .col-xl-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }

  .col-xl-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }

  .col-xl-12 {
    flex: 0 0 auto;
    width: 100%;
  }

  .offset-xl-0 {
    margin-left: 0;
  }

  .offset-xl-1 {
    margin-left: 8.33333333%;
  }

  .offset-xl-2 {
    margin-left: 16.66666667%;
  }

  .offset-xl-3 {
    margin-left: 25%;
  }

  .offset-xl-4 {
    margin-left: 33.33333333%;
  }

  .offset-xl-5 {
    margin-left: 41.66666667%;
  }

  .offset-xl-6 {
    margin-left: 50%;
  }

  .offset-xl-7 {
    margin-left: 58.33333333%;
  }

  .offset-xl-8 {
    margin-left: 66.66666667%;
  }

  .offset-xl-9 {
    margin-left: 75%;
  }

  .offset-xl-10 {
    margin-left: 83.33333333%;
  }

  .offset-xl-11 {
    margin-left: 91.66666667%;
  }

  .g-xl-0,
.gx-xl-0 {
    --bs-gutter-x: 0;
  }

  .g-xl-0,
.gy-xl-0 {
    --bs-gutter-y: 0;
  }

  .g-xl-5,
.gx-xl-5 {
    --bs-gutter-x: 0.3125rem;
  }

  .g-xl-5,
.gy-xl-5 {
    --bs-gutter-y: 0.3125rem;
  }

  .g-xl-10,
.gx-xl-10 {
    --bs-gutter-x: 0.625rem;
  }

  .g-xl-10,
.gy-xl-10 {
    --bs-gutter-y: 0.625rem;
  }

  .g-xl-15,
.gx-xl-15 {
    --bs-gutter-x: 0.9375rem;
  }

  .g-xl-15,
.gy-xl-15 {
    --bs-gutter-y: 0.9375rem;
  }

  .g-xl-20,
.gx-xl-20 {
    --bs-gutter-x: 1.25rem;
  }

  .g-xl-20,
.gy-xl-20 {
    --bs-gutter-y: 1.25rem;
  }

  .g-xl-25,
.gx-xl-25 {
    --bs-gutter-x: 1.5625rem;
  }

  .g-xl-25,
.gy-xl-25 {
    --bs-gutter-y: 1.5625rem;
  }

  .g-xl-30,
.gx-xl-30 {
    --bs-gutter-x: 1.875rem;
  }

  .g-xl-30,
.gy-xl-30 {
    --bs-gutter-y: 1.875rem;
  }

  .g-xl-40,
.gx-xl-40 {
    --bs-gutter-x: 2.5rem;
  }

  .g-xl-40,
.gy-xl-40 {
    --bs-gutter-y: 2.5rem;
  }

  .g-xl-50,
.gx-xl-50 {
    --bs-gutter-x: 3.125rem;
  }

  .g-xl-50,
.gy-xl-50 {
    --bs-gutter-y: 3.125rem;
  }

  .g-xl-70,
.gx-xl-70 {
    --bs-gutter-x: 4.375rem;
  }

  .g-xl-70,
.gy-xl-70 {
    --bs-gutter-y: 4.375rem;
  }

  .g-xl-80,
.gx-xl-80 {
    --bs-gutter-x: 5rem;
  }

  .g-xl-80,
.gy-xl-80 {
    --bs-gutter-y: 5rem;
  }

  .g-xl-100,
.gx-xl-100 {
    --bs-gutter-x: 6.25rem;
  }

  .g-xl-100,
.gy-xl-100 {
    --bs-gutter-y: 6.25rem;
  }

  .g-xl-135,
.gx-xl-135 {
    --bs-gutter-x: 8.4375rem;
  }

  .g-xl-135,
.gy-xl-135 {
    --bs-gutter-y: 8.4375rem;
  }

  .g-xl-150,
.gx-xl-150 {
    --bs-gutter-x: 9.375rem;
  }

  .g-xl-150,
.gy-xl-150 {
    --bs-gutter-y: 9.375rem;
  }
}
@media (min-width: 1400px) {
  .col-xxl {
    flex: 1 0 0;
  }

  .row-cols-xxl-auto > * {
    flex: 0 0 auto;
    width: auto;
  }

  .row-cols-xxl-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }

  .row-cols-xxl-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }

  .row-cols-xxl-3 > * {
    flex: 0 0 auto;
    width: 33.33333333%;
  }

  .row-cols-xxl-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }

  .row-cols-xxl-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }

  .row-cols-xxl-6 > * {
    flex: 0 0 auto;
    width: 16.66666667%;
  }

  .col-xxl-auto {
    flex: 0 0 auto;
    width: auto;
  }

  .col-xxl-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }

  .col-xxl-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }

  .col-xxl-3 {
    flex: 0 0 auto;
    width: 25%;
  }

  .col-xxl-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }

  .col-xxl-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }

  .col-xxl-6 {
    flex: 0 0 auto;
    width: 50%;
  }

  .col-xxl-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }

  .col-xxl-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }

  .col-xxl-9 {
    flex: 0 0 auto;
    width: 75%;
  }

  .col-xxl-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }

  .col-xxl-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }

  .col-xxl-12 {
    flex: 0 0 auto;
    width: 100%;
  }

  .offset-xxl-0 {
    margin-left: 0;
  }

  .offset-xxl-1 {
    margin-left: 8.33333333%;
  }

  .offset-xxl-2 {
    margin-left: 16.66666667%;
  }

  .offset-xxl-3 {
    margin-left: 25%;
  }

  .offset-xxl-4 {
    margin-left: 33.33333333%;
  }

  .offset-xxl-5 {
    margin-left: 41.66666667%;
  }

  .offset-xxl-6 {
    margin-left: 50%;
  }

  .offset-xxl-7 {
    margin-left: 58.33333333%;
  }

  .offset-xxl-8 {
    margin-left: 66.66666667%;
  }

  .offset-xxl-9 {
    margin-left: 75%;
  }

  .offset-xxl-10 {
    margin-left: 83.33333333%;
  }

  .offset-xxl-11 {
    margin-left: 91.66666667%;
  }

  .g-xxl-0,
.gx-xxl-0 {
    --bs-gutter-x: 0;
  }

  .g-xxl-0,
.gy-xxl-0 {
    --bs-gutter-y: 0;
  }

  .g-xxl-5,
.gx-xxl-5 {
    --bs-gutter-x: 0.3125rem;
  }

  .g-xxl-5,
.gy-xxl-5 {
    --bs-gutter-y: 0.3125rem;
  }

  .g-xxl-10,
.gx-xxl-10 {
    --bs-gutter-x: 0.625rem;
  }

  .g-xxl-10,
.gy-xxl-10 {
    --bs-gutter-y: 0.625rem;
  }

  .g-xxl-15,
.gx-xxl-15 {
    --bs-gutter-x: 0.9375rem;
  }

  .g-xxl-15,
.gy-xxl-15 {
    --bs-gutter-y: 0.9375rem;
  }

  .g-xxl-20,
.gx-xxl-20 {
    --bs-gutter-x: 1.25rem;
  }

  .g-xxl-20,
.gy-xxl-20 {
    --bs-gutter-y: 1.25rem;
  }

  .g-xxl-25,
.gx-xxl-25 {
    --bs-gutter-x: 1.5625rem;
  }

  .g-xxl-25,
.gy-xxl-25 {
    --bs-gutter-y: 1.5625rem;
  }

  .g-xxl-30,
.gx-xxl-30 {
    --bs-gutter-x: 1.875rem;
  }

  .g-xxl-30,
.gy-xxl-30 {
    --bs-gutter-y: 1.875rem;
  }

  .g-xxl-40,
.gx-xxl-40 {
    --bs-gutter-x: 2.5rem;
  }

  .g-xxl-40,
.gy-xxl-40 {
    --bs-gutter-y: 2.5rem;
  }

  .g-xxl-50,
.gx-xxl-50 {
    --bs-gutter-x: 3.125rem;
  }

  .g-xxl-50,
.gy-xxl-50 {
    --bs-gutter-y: 3.125rem;
  }

  .g-xxl-70,
.gx-xxl-70 {
    --bs-gutter-x: 4.375rem;
  }

  .g-xxl-70,
.gy-xxl-70 {
    --bs-gutter-y: 4.375rem;
  }

  .g-xxl-80,
.gx-xxl-80 {
    --bs-gutter-x: 5rem;
  }

  .g-xxl-80,
.gy-xxl-80 {
    --bs-gutter-y: 5rem;
  }

  .g-xxl-100,
.gx-xxl-100 {
    --bs-gutter-x: 6.25rem;
  }

  .g-xxl-100,
.gy-xxl-100 {
    --bs-gutter-y: 6.25rem;
  }

  .g-xxl-135,
.gx-xxl-135 {
    --bs-gutter-x: 8.4375rem;
  }

  .g-xxl-135,
.gy-xxl-135 {
    --bs-gutter-y: 8.4375rem;
  }

  .g-xxl-150,
.gx-xxl-150 {
    --bs-gutter-x: 9.375rem;
  }

  .g-xxl-150,
.gy-xxl-150 {
    --bs-gutter-y: 9.375rem;
  }
}
.clearfix::after {
  display: block;
  clear: both;
  content: "";
}

.text-bg-primary {
  color: #FFF9E6 !important;
  background-color: RGBA(var(--bs-primary-rgb), var(--bs-bg-opacity, 1)) !important;
}

.text-bg-secondary {
  color: #28170F !important;
  background-color: RGBA(var(--bs-secondary-rgb), var(--bs-bg-opacity, 1)) !important;
}

.text-bg-success {
  color: #FFF9E6 !important;
  background-color: RGBA(var(--bs-success-rgb), var(--bs-bg-opacity, 1)) !important;
}

.text-bg-info {
  color: #28170F !important;
  background-color: RGBA(var(--bs-info-rgb), var(--bs-bg-opacity, 1)) !important;
}

.text-bg-warning {
  color: #28170F !important;
  background-color: RGBA(var(--bs-warning-rgb), var(--bs-bg-opacity, 1)) !important;
}

.text-bg-danger {
  color: #28170F !important;
  background-color: RGBA(var(--bs-danger-rgb), var(--bs-bg-opacity, 1)) !important;
}

.text-bg-dark {
  color: #FFF9E6 !important;
  background-color: RGBA(var(--bs-dark-rgb), var(--bs-bg-opacity, 1)) !important;
}

.text-bg-green {
  color: #FFF9E6 !important;
  background-color: RGBA(var(--bs-green-rgb), var(--bs-bg-opacity, 1)) !important;
}

.text-bg-celadon {
  color: #28170F !important;
  background-color: RGBA(var(--bs-celadon-rgb), var(--bs-bg-opacity, 1)) !important;
}

.text-bg-red {
  color: #28170F !important;
  background-color: RGBA(var(--bs-red-rgb), var(--bs-bg-opacity, 1)) !important;
}

.text-bg-warm {
  color: #28170F !important;
  background-color: RGBA(var(--bs-warm-rgb), var(--bs-bg-opacity, 1)) !important;
}

.text-bg-summer {
  color: #28170F !important;
  background-color: RGBA(var(--bs-summer-rgb), var(--bs-bg-opacity, 1)) !important;
}

.text-bg-autumn {
  color: #28170F !important;
  background-color: RGBA(var(--bs-autumn-rgb), var(--bs-bg-opacity, 1)) !important;
}

.text-bg-winter {
  color: #28170F !important;
  background-color: RGBA(var(--bs-winter-rgb), var(--bs-bg-opacity, 1)) !important;
}

.text-bg-spring {
  color: #28170F !important;
  background-color: RGBA(var(--bs-spring-rgb), var(--bs-bg-opacity, 1)) !important;
}

.link-primary {
  color: RGBA(var(--bs-primary-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-primary-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-primary:hover, .link-primary:focus {
  color: RGBA(52, 65, 54, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(52, 65, 54, var(--bs-link-underline-opacity, 1)) !important;
}

.link-secondary {
  color: RGBA(var(--bs-secondary-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-secondary-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-secondary:hover, .link-secondary:focus {
  color: RGBA(240, 108, 88, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(240, 108, 88, var(--bs-link-underline-opacity, 1)) !important;
}

.link-success {
  color: RGBA(var(--bs-success-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-success-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-success:hover, .link-success:focus {
  color: RGBA(52, 65, 54, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(52, 65, 54, var(--bs-link-underline-opacity, 1)) !important;
}

.link-info {
  color: RGBA(var(--bs-info-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-info-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-info:hover, .link-info:focus {
  color: RGBA(139, 181, 169, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(139, 181, 169, var(--bs-link-underline-opacity, 1)) !important;
}

.link-warning {
  color: RGBA(var(--bs-warning-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-warning-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-warning:hover, .link-warning:focus {
  color: RGBA(139, 181, 169, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(139, 181, 169, var(--bs-link-underline-opacity, 1)) !important;
}

.link-danger {
  color: RGBA(var(--bs-danger-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-danger-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-danger:hover, .link-danger:focus {
  color: RGBA(240, 108, 88, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(240, 108, 88, var(--bs-link-underline-opacity, 1)) !important;
}

.link-dark {
  color: RGBA(var(--bs-dark-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-dark-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-dark:hover, .link-dark:focus {
  color: RGBA(32, 18, 12, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(32, 18, 12, var(--bs-link-underline-opacity, 1)) !important;
}

.link-green {
  color: RGBA(var(--bs-green-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-green-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-green:hover, .link-green:focus {
  color: RGBA(52, 65, 54, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(52, 65, 54, var(--bs-link-underline-opacity, 1)) !important;
}

.link-celadon {
  color: RGBA(var(--bs-celadon-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-celadon-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-celadon:hover, .link-celadon:focus {
  color: RGBA(139, 181, 169, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(139, 181, 169, var(--bs-link-underline-opacity, 1)) !important;
}

.link-red {
  color: RGBA(var(--bs-red-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-red-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-red:hover, .link-red:focus {
  color: RGBA(240, 108, 88, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(240, 108, 88, var(--bs-link-underline-opacity, 1)) !important;
}

.link-warm {
  color: RGBA(var(--bs-warm-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-warm-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-warm:hover, .link-warm:focus {
  color: RGBA(255, 250, 235, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(255, 250, 235, var(--bs-link-underline-opacity, 1)) !important;
}

.link-summer {
  color: RGBA(var(--bs-summer-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-summer-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-summer:hover, .link-summer:focus {
  color: RGBA(181, 181, 181, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(181, 181, 181, var(--bs-link-underline-opacity, 1)) !important;
}

.link-autumn {
  color: RGBA(var(--bs-autumn-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-autumn-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-autumn:hover, .link-autumn:focus {
  color: RGBA(181, 181, 181, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(181, 181, 181, var(--bs-link-underline-opacity, 1)) !important;
}

.link-winter {
  color: RGBA(var(--bs-winter-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-winter-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-winter:hover, .link-winter:focus {
  color: RGBA(181, 181, 181, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(181, 181, 181, var(--bs-link-underline-opacity, 1)) !important;
}

.link-spring {
  color: RGBA(var(--bs-spring-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-spring-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-spring:hover, .link-spring:focus {
  color: RGBA(181, 181, 181, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(181, 181, 181, var(--bs-link-underline-opacity, 1)) !important;
}

.link-body-emphasis {
  color: RGBA(var(--bs-emphasis-color-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-emphasis-color-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-body-emphasis:hover, .link-body-emphasis:focus {
  color: RGBA(var(--bs-emphasis-color-rgb), var(--bs-link-opacity, 0.75)) !important;
  text-decoration-color: RGBA(var(--bs-emphasis-color-rgb), var(--bs-link-underline-opacity, 0.75)) !important;
}

.focus-ring:focus {
  outline: 0;
  box-shadow: var(--bs-focus-ring-x, 0) var(--bs-focus-ring-y, 0) var(--bs-focus-ring-blur, 0) var(--bs-focus-ring-width) var(--bs-focus-ring-color);
}

.icon-link {
  display: inline-flex;
  gap: 0.375rem;
  align-items: center;
  text-decoration-color: rgba(var(--bs-link-color-rgb), var(--bs-link-opacity, 0.5));
  text-underline-offset: 0.25em;
  backface-visibility: hidden;
}
.icon-link > .bi {
  flex-shrink: 0;
  width: 1em;
  height: 1em;
  fill: currentcolor;
  transition: 0.2s ease-in-out transform;
}
@media (prefers-reduced-motion: reduce) {
  .icon-link > .bi {
    transition: none;
  }
}

.icon-link-hover:hover > .bi, .icon-link-hover:focus-visible > .bi {
  transform: var(--bs-icon-link-transform, translate3d(0.25em, 0, 0));
}

.ratio {
  position: relative;
  width: 100%;
}
.ratio::before {
  display: block;
  padding-top: var(--bs-aspect-ratio);
  content: "";
}
.ratio > * {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.ratio-1x1 {
  --bs-aspect-ratio: 100%;
}

.ratio-2x1 {
  --bs-aspect-ratio: calc(1 / 2 * 100%);
}

.ratio-1x2 {
  --bs-aspect-ratio: calc(2 / 1 * 100%);
}

.ratio-3x2 {
  --bs-aspect-ratio: calc(2 / 3 * 100%);
}

.ratio-2x3 {
  --bs-aspect-ratio: calc(3 / 2 * 100%);
}

.ratio-4x3 {
  --bs-aspect-ratio: calc(3 / 4 * 100%);
}

.ratio-3x4 {
  --bs-aspect-ratio: calc(4 / 3 * 100%);
}

.ratio-9x16 {
  --bs-aspect-ratio: calc(16 / 9 * 100%);
}

.ratio-9x13 {
  --bs-aspect-ratio: calc(13 / 9 * 100%);
}

.ratio-16x9 {
  --bs-aspect-ratio: calc(9 / 16 * 100%);
}

.ratio-21x9 {
  --bs-aspect-ratio: calc(9 / 21 * 100%);
}

.fixed-top {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1030;
}

.fixed-bottom {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1030;
}

.sticky-top {
  position: sticky;
  top: 0;
  z-index: 1020;
}

.sticky-bottom {
  position: sticky;
  bottom: 0;
  z-index: 1020;
}

@media (min-width: 576px) {
  .sticky-sm-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }

  .sticky-sm-bottom {
    position: sticky;
    bottom: 0;
    z-index: 1020;
  }
}
@media (min-width: 768px) {
  .sticky-md-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }

  .sticky-md-bottom {
    position: sticky;
    bottom: 0;
    z-index: 1020;
  }
}
@media (min-width: 992px) {
  .sticky-lg-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }

  .sticky-lg-bottom {
    position: sticky;
    bottom: 0;
    z-index: 1020;
  }
}
@media (min-width: 1200px) {
  .sticky-xl-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }

  .sticky-xl-bottom {
    position: sticky;
    bottom: 0;
    z-index: 1020;
  }
}
@media (min-width: 1400px) {
  .sticky-xxl-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }

  .sticky-xxl-bottom {
    position: sticky;
    bottom: 0;
    z-index: 1020;
  }
}
.hstack {
  display: flex;
  flex-direction: row;
  align-items: center;
  align-self: stretch;
}

.vstack {
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  align-self: stretch;
}

.visually-hidden,
.visually-hidden-focusable:not(:focus):not(:focus-within) {
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}
.visually-hidden:not(caption),
.visually-hidden-focusable:not(:focus):not(:focus-within):not(caption) {
  position: absolute !important;
}
.visually-hidden *,
.visually-hidden-focusable:not(:focus):not(:focus-within) * {
  overflow: hidden !important;
}

.stretched-link::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  content: "";
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.vr {
  display: inline-block;
  align-self: stretch;
  width: var(--bs-border-width);
  min-height: 1em;
  background-color: currentcolor;
  opacity: 0.25;
}

.nav {
  --bs-nav-link-padding-x: 0;
  --bs-nav-link-padding-y: 0;
  --bs-nav-link-font-size:1.0625rem;
  --bs-nav-link-font-weight: 400;
  --bs-nav-link-color: currentColor;
  --bs-nav-link-hover-color: #6EA394;
  --bs-nav-link-disabled-color: var(--bs-secondary-color);
  display: flex;
  flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}

.nav-link {
  display: block;
  padding: var(--bs-nav-link-padding-y) var(--bs-nav-link-padding-x);
  font-size: var(--bs-nav-link-font-size);
  font-weight: var(--bs-nav-link-font-weight);
  color: var(--bs-nav-link-color);
  text-decoration: none;
  background: none;
  border: 0;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .nav-link {
    transition: none;
  }
}
.nav-link:hover, .nav-link:focus {
  color: var(--bs-nav-link-hover-color);
}
.nav-link:focus-visible {
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(65, 81, 67, 0.25);
}
.nav-link.disabled, .nav-link:disabled {
  color: var(--bs-nav-link-disabled-color);
  pointer-events: none;
  cursor: default;
}

.nav-tabs {
  --bs-nav-tabs-border-width: var(--bs-border-width);
  --bs-nav-tabs-border-color: var(--bs-border-color);
  --bs-nav-tabs-border-radius: var(--bs-border-radius);
  --bs-nav-tabs-link-hover-border-color: var(--bs-secondary-bg) var(--bs-secondary-bg) var(--bs-border-color);
  --bs-nav-tabs-link-active-color: var(--bs-emphasis-color);
  --bs-nav-tabs-link-active-bg: var(--bs-body-bg);
  --bs-nav-tabs-link-active-border-color: var(--bs-border-color) var(--bs-border-color) var(--bs-body-bg);
  border-bottom: var(--bs-nav-tabs-border-width) solid var(--bs-nav-tabs-border-color);
}
.nav-tabs .nav-link {
  margin-bottom: calc(-1 * var(--bs-nav-tabs-border-width));
  border: var(--bs-nav-tabs-border-width) solid transparent;
  border-top-left-radius: var(--bs-nav-tabs-border-radius);
  border-top-right-radius: var(--bs-nav-tabs-border-radius);
}
.nav-tabs .nav-link:hover, .nav-tabs .nav-link:focus {
  isolation: isolate;
  border-color: var(--bs-nav-tabs-link-hover-border-color);
}
.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
  color: var(--bs-nav-tabs-link-active-color);
  background-color: var(--bs-nav-tabs-link-active-bg);
  border-color: var(--bs-nav-tabs-link-active-border-color);
}
.nav-tabs .dropdown-menu {
  margin-top: calc(-1 * var(--bs-nav-tabs-border-width));
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.nav-pills {
  --bs-nav-pills-border-radius: var(--bs-border-radius);
  --bs-nav-pills-link-active-color: #FFF9E6;
  --bs-nav-pills-link-active-bg: #415143;
}
.nav-pills .nav-link {
  border-radius: var(--bs-nav-pills-border-radius);
}
.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: var(--bs-nav-pills-link-active-color);
  background-color: var(--bs-nav-pills-link-active-bg);
}

.nav-underline {
  --bs-nav-underline-gap: 1rem;
  --bs-nav-underline-border-width: 0.125rem;
  --bs-nav-underline-link-active-color: var(--bs-emphasis-color);
  gap: var(--bs-nav-underline-gap);
}
.nav-underline .nav-link {
  padding-right: 0;
  padding-left: 0;
  border-bottom: var(--bs-nav-underline-border-width) solid transparent;
}
.nav-underline .nav-link:hover, .nav-underline .nav-link:focus {
  border-bottom-color: currentcolor;
}
.nav-underline .nav-link.active,
.nav-underline .show > .nav-link {
  font-weight: 700;
  color: var(--bs-nav-underline-link-active-color);
  border-bottom-color: currentcolor;
}

.nav-fill > .nav-link,
.nav-fill .nav-item {
  flex: 1 1 auto;
  text-align: center;
}

.nav-justified > .nav-link,
.nav-justified .nav-item {
  flex-grow: 1;
  flex-basis: 0;
  text-align: center;
}

.nav-fill .nav-item .nav-link,
.nav-justified .nav-item .nav-link {
  width: 100%;
}

.tab-content > .tab-pane {
  display: none;
}
.tab-content > .active {
  display: block;
}

.navbar {
  --bs-navbar-padding-x: 0;
  --bs-navbar-padding-y: 0;
  --bs-navbar-color: rgba(var(--bs-emphasis-color-rgb), 0.65);
  --bs-navbar-hover-color: rgba(var(--bs-emphasis-color-rgb), 0.8);
  --bs-navbar-disabled-color: rgba(var(--bs-emphasis-color-rgb), 0.3);
  --bs-navbar-active-color: rgba(var(--bs-emphasis-color-rgb), 1);
  --bs-navbar-brand-padding-y: -4.65625rem;
  --bs-navbar-brand-margin-end: 0;
  --bs-navbar-brand-font-size: 1.25rem;
  --bs-navbar-brand-color: rgba(var(--bs-emphasis-color-rgb), 1);
  --bs-navbar-brand-hover-color: rgba(var(--bs-emphasis-color-rgb), 1);
  --bs-navbar-nav-link-padding-x: 0.90625rem;
  --bs-navbar-toggler-padding-y: 0;
  --bs-navbar-toggler-padding-x: 0;
  --bs-navbar-toggler-font-size: 1.25rem;
  --bs-navbar-toggler-icon-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2865, 81, 67, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
  --bs-navbar-toggler-border-color: rgba(var(--bs-emphasis-color-rgb), 0.15);
  --bs-navbar-toggler-border-radius: 0;
  --bs-navbar-toggler-focus-width: 0.25rem;
  --bs-navbar-toggler-transition: box-shadow 0.15s ease-in-out;
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  padding: var(--bs-navbar-padding-y) var(--bs-navbar-padding-x);
}
.navbar > .container,
.navbar > .container-fluid,
.navbar > .container-xs,
.navbar > .container-sm,
.navbar > .container-md,
.navbar > .container-lg,
.navbar > .container-xl,
.navbar > .container-xxl {
  display: flex;
  flex-wrap: inherit;
  align-items: center;
  justify-content: space-between;
}
.navbar-brand {
  padding-top: var(--bs-navbar-brand-padding-y);
  padding-bottom: var(--bs-navbar-brand-padding-y);
  margin-right: var(--bs-navbar-brand-margin-end);
  font-size: var(--bs-navbar-brand-font-size);
  color: var(--bs-navbar-brand-color);
  text-decoration: none;
  white-space: nowrap;
}
.navbar-brand:hover, .navbar-brand:focus {
  color: var(--bs-navbar-brand-hover-color);
}

.navbar-nav {
  --bs-nav-link-padding-x: 0;
  --bs-nav-link-padding-y: 0;
  --bs-nav-link-font-size:1.0625rem;
  --bs-nav-link-font-weight: 400;
  --bs-nav-link-color: var(--bs-navbar-color);
  --bs-nav-link-hover-color: var(--bs-navbar-hover-color);
  --bs-nav-link-disabled-color: var(--bs-navbar-disabled-color);
  display: flex;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}
.navbar-nav .nav-link.active, .navbar-nav .nav-link.show {
  color: var(--bs-navbar-active-color);
}
.navbar-nav .dropdown-menu {
  position: static;
}

.navbar-text {
  padding-top: 0;
  padding-bottom: 0;
  color: var(--bs-navbar-color);
}
.navbar-text a,
.navbar-text a:hover,
.navbar-text a:focus {
  color: var(--bs-navbar-active-color);
}

.navbar-collapse {
  flex-grow: 1;
  flex-basis: 100%;
  align-items: center;
}

.navbar-toggler {
  padding: var(--bs-navbar-toggler-padding-y) var(--bs-navbar-toggler-padding-x);
  font-size: var(--bs-navbar-toggler-font-size);
  line-height: 1;
  color: var(--bs-navbar-color);
  background-color: transparent;
  border: var(--bs-border-width) solid var(--bs-navbar-toggler-border-color);
  border-radius: var(--bs-navbar-toggler-border-radius);
  transition: var(--bs-navbar-toggler-transition);
}
@media (prefers-reduced-motion: reduce) {
  .navbar-toggler {
    transition: none;
  }
}
.navbar-toggler:hover {
  text-decoration: none;
}
.navbar-toggler:focus {
  text-decoration: none;
  outline: 0;
  box-shadow: 0 0 0 var(--bs-navbar-toggler-focus-width);
}

.navbar-toggler-icon {
  display: inline-block;
  width: 1.5em;
  height: 1.5em;
  vertical-align: middle;
  background-image: var(--bs-navbar-toggler-icon-bg);
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100%;
}

.navbar-nav-scroll {
  max-height: var(--bs-scroll-height, 75vh);
  overflow-y: auto;
}

@media (min-width: 576px) {
  .navbar-expand-sm {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-sm .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-sm .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-sm .navbar-nav .nav-link {
    padding-right: var(--bs-navbar-nav-link-padding-x);
    padding-left: var(--bs-navbar-nav-link-padding-x);
  }
  .navbar-expand-sm .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-sm .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-sm .navbar-toggler {
    display: none;
  }
  .navbar-expand-sm .offcanvas {
    position: static;
    z-index: auto;
    flex-grow: 1;
    width: auto !important;
    height: auto !important;
    visibility: visible !important;
    background-color: transparent !important;
    border: 0 !important;
    transform: none !important;
    transition: none;
  }
  .navbar-expand-sm .offcanvas .offcanvas-header {
    display: none;
  }
  .navbar-expand-sm .offcanvas .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
@media (min-width: 768px) {
  .navbar-expand-md {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-md .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-md .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-md .navbar-nav .nav-link {
    padding-right: var(--bs-navbar-nav-link-padding-x);
    padding-left: var(--bs-navbar-nav-link-padding-x);
  }
  .navbar-expand-md .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-md .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-md .navbar-toggler {
    display: none;
  }
  .navbar-expand-md .offcanvas {
    position: static;
    z-index: auto;
    flex-grow: 1;
    width: auto !important;
    height: auto !important;
    visibility: visible !important;
    background-color: transparent !important;
    border: 0 !important;
    transform: none !important;
    transition: none;
  }
  .navbar-expand-md .offcanvas .offcanvas-header {
    display: none;
  }
  .navbar-expand-md .offcanvas .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
@media (min-width: 992px) {
  .navbar-expand-lg {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-lg .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-lg .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-lg .navbar-nav .nav-link {
    padding-right: var(--bs-navbar-nav-link-padding-x);
    padding-left: var(--bs-navbar-nav-link-padding-x);
  }
  .navbar-expand-lg .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-lg .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-lg .navbar-toggler {
    display: none;
  }
  .navbar-expand-lg .offcanvas {
    position: static;
    z-index: auto;
    flex-grow: 1;
    width: auto !important;
    height: auto !important;
    visibility: visible !important;
    background-color: transparent !important;
    border: 0 !important;
    transform: none !important;
    transition: none;
  }
  .navbar-expand-lg .offcanvas .offcanvas-header {
    display: none;
  }
  .navbar-expand-lg .offcanvas .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
@media (min-width: 1200px) {
  .navbar-expand-xl {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-xl .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-xl .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-xl .navbar-nav .nav-link {
    padding-right: var(--bs-navbar-nav-link-padding-x);
    padding-left: var(--bs-navbar-nav-link-padding-x);
  }
  .navbar-expand-xl .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-xl .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-xl .navbar-toggler {
    display: none;
  }
  .navbar-expand-xl .offcanvas {
    position: static;
    z-index: auto;
    flex-grow: 1;
    width: auto !important;
    height: auto !important;
    visibility: visible !important;
    background-color: transparent !important;
    border: 0 !important;
    transform: none !important;
    transition: none;
  }
  .navbar-expand-xl .offcanvas .offcanvas-header {
    display: none;
  }
  .navbar-expand-xl .offcanvas .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
@media (min-width: 1400px) {
  .navbar-expand-xxl {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-xxl .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-xxl .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-xxl .navbar-nav .nav-link {
    padding-right: var(--bs-navbar-nav-link-padding-x);
    padding-left: var(--bs-navbar-nav-link-padding-x);
  }
  .navbar-expand-xxl .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-xxl .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-xxl .navbar-toggler {
    display: none;
  }
  .navbar-expand-xxl .offcanvas {
    position: static;
    z-index: auto;
    flex-grow: 1;
    width: auto !important;
    height: auto !important;
    visibility: visible !important;
    background-color: transparent !important;
    border: 0 !important;
    transform: none !important;
    transition: none;
  }
  .navbar-expand-xxl .offcanvas .offcanvas-header {
    display: none;
  }
  .navbar-expand-xxl .offcanvas .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
.navbar-expand {
  flex-wrap: nowrap;
  justify-content: flex-start;
}
.navbar-expand .navbar-nav {
  flex-direction: row;
}
.navbar-expand .navbar-nav .dropdown-menu {
  position: absolute;
}
.navbar-expand .navbar-nav .nav-link {
  padding-right: var(--bs-navbar-nav-link-padding-x);
  padding-left: var(--bs-navbar-nav-link-padding-x);
}
.navbar-expand .navbar-nav-scroll {
  overflow: visible;
}
.navbar-expand .navbar-collapse {
  display: flex !important;
  flex-basis: auto;
}
.navbar-expand .navbar-toggler {
  display: none;
}
.navbar-expand .offcanvas {
  position: static;
  z-index: auto;
  flex-grow: 1;
  width: auto !important;
  height: auto !important;
  visibility: visible !important;
  background-color: transparent !important;
  border: 0 !important;
  transform: none !important;
  transition: none;
}
.navbar-expand .offcanvas .offcanvas-header {
  display: none;
}
.navbar-expand .offcanvas .offcanvas-body {
  display: flex;
  flex-grow: 0;
  padding: 0;
  overflow-y: visible;
}

.navbar-dark,
.navbar[data-bs-theme=dark] {
  --bs-navbar-color: rgba(255, 249, 230, 0.55);
  --bs-navbar-hover-color: rgba(255, 249, 230, 0.75);
  --bs-navbar-disabled-color: rgba(255, 249, 230, 0.25);
  --bs-navbar-active-color: #FFF9E6;
  --bs-navbar-brand-color: #FFF9E6;
  --bs-navbar-brand-hover-color: #FFF9E6;
  --bs-navbar-toggler-border-color: rgba(255, 249, 230, 0.1);
  --bs-navbar-toggler-icon-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 249, 230, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

[data-bs-theme=dark] .navbar-toggler-icon {
  --bs-navbar-toggler-icon-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 249, 230, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

.dropup,
.dropend,
.dropdown,
.dropstart,
.dropup-center,
.dropdown-center {
  position: relative;
}

.dropdown-toggle {
  white-space: nowrap;
}
.dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid;
  border-right: 0.3em solid transparent;
  border-bottom: 0;
  border-left: 0.3em solid transparent;
}
.dropdown-toggle:empty::after {
  margin-left: 0;
}

.dropdown-menu {
  --bs-dropdown-zindex: 1000;
  --bs-dropdown-min-width: auto;
  --bs-dropdown-padding-x: 0;
  --bs-dropdown-padding-y: 0.5rem;
  --bs-dropdown-spacer: 0.125rem;
  --bs-dropdown-font-size:1rem;
  --bs-dropdown-color: #6EA394;
  --bs-dropdown-bg: #FFF9E6;
  --bs-dropdown-border-color: var(--bs-border-color-translucent);
  --bs-dropdown-border-radius: var(--bs-border-radius);
  --bs-dropdown-border-width: var(--bs-border-width);
  --bs-dropdown-inner-border-radius: calc(var(--bs-border-radius) - var(--bs-border-width));
  --bs-dropdown-divider-bg: var(--bs-border-color-translucent);
  --bs-dropdown-divider-margin-y: 0.3125rem;
  --bs-dropdown-box-shadow: var(--bs-box-shadow);
  --bs-dropdown-link-color: var(--bs-body-color);
  --bs-dropdown-link-hover-color: var(--bs-body-color);
  --bs-dropdown-link-hover-bg: var(--bs-tertiary-bg);
  --bs-dropdown-link-active-color: #FFF9E6;
  --bs-dropdown-link-active-bg: #415143;
  --bs-dropdown-link-disabled-color: var(--bs-tertiary-color);
  --bs-dropdown-item-padding-x: 0.625rem;
  --bs-dropdown-item-padding-y: 0.15625rem;
  --bs-dropdown-header-color: #6c757d;
  --bs-dropdown-header-padding-x: 0.625rem;
  --bs-dropdown-header-padding-y: 0.5rem;
  position: absolute;
  z-index: var(--bs-dropdown-zindex);
  display: none;
  min-width: var(--bs-dropdown-min-width);
  padding: var(--bs-dropdown-padding-y) var(--bs-dropdown-padding-x);
  margin: 0;
  font-size: var(--bs-dropdown-font-size);
  color: var(--bs-dropdown-color);
  text-align: left;
  list-style: none;
  background-color: var(--bs-dropdown-bg);
  background-clip: padding-box;
  border: var(--bs-dropdown-border-width) solid var(--bs-dropdown-border-color);
  border-radius: var(--bs-dropdown-border-radius);
}
.dropdown-menu[data-bs-popper] {
  top: 100%;
  left: 0;
  margin-top: var(--bs-dropdown-spacer);
}

.dropdown-menu-start {
  --bs-position: start;
}
.dropdown-menu-start[data-bs-popper] {
  right: auto;
  left: 0;
}

.dropdown-menu-end {
  --bs-position: end;
}
.dropdown-menu-end[data-bs-popper] {
  right: 0;
  left: auto;
}

@media (min-width: 576px) {
  .dropdown-menu-sm-start {
    --bs-position: start;
  }
  .dropdown-menu-sm-start[data-bs-popper] {
    right: auto;
    left: 0;
  }

  .dropdown-menu-sm-end {
    --bs-position: end;
  }
  .dropdown-menu-sm-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
@media (min-width: 768px) {
  .dropdown-menu-md-start {
    --bs-position: start;
  }
  .dropdown-menu-md-start[data-bs-popper] {
    right: auto;
    left: 0;
  }

  .dropdown-menu-md-end {
    --bs-position: end;
  }
  .dropdown-menu-md-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
@media (min-width: 992px) {
  .dropdown-menu-lg-start {
    --bs-position: start;
  }
  .dropdown-menu-lg-start[data-bs-popper] {
    right: auto;
    left: 0;
  }

  .dropdown-menu-lg-end {
    --bs-position: end;
  }
  .dropdown-menu-lg-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
@media (min-width: 1200px) {
  .dropdown-menu-xl-start {
    --bs-position: start;
  }
  .dropdown-menu-xl-start[data-bs-popper] {
    right: auto;
    left: 0;
  }

  .dropdown-menu-xl-end {
    --bs-position: end;
  }
  .dropdown-menu-xl-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
@media (min-width: 1400px) {
  .dropdown-menu-xxl-start {
    --bs-position: start;
  }
  .dropdown-menu-xxl-start[data-bs-popper] {
    right: auto;
    left: 0;
  }

  .dropdown-menu-xxl-end {
    --bs-position: end;
  }
  .dropdown-menu-xxl-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
.dropup .dropdown-menu[data-bs-popper] {
  top: auto;
  bottom: 100%;
  margin-top: 0;
  margin-bottom: var(--bs-dropdown-spacer);
}
.dropup .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0;
  border-right: 0.3em solid transparent;
  border-bottom: 0.3em solid;
  border-left: 0.3em solid transparent;
}
.dropup .dropdown-toggle:empty::after {
  margin-left: 0;
}

.dropend .dropdown-menu[data-bs-popper] {
  top: 0;
  right: auto;
  left: 100%;
  margin-top: 0;
  margin-left: var(--bs-dropdown-spacer);
}
.dropend .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid transparent;
  border-right: 0;
  border-bottom: 0.3em solid transparent;
  border-left: 0.3em solid;
}
.dropend .dropdown-toggle:empty::after {
  margin-left: 0;
}
.dropend .dropdown-toggle::after {
  vertical-align: 0;
}

.dropstart .dropdown-menu[data-bs-popper] {
  top: 0;
  right: 100%;
  left: auto;
  margin-top: 0;
  margin-right: var(--bs-dropdown-spacer);
}
.dropstart .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
}
.dropstart .dropdown-toggle::after {
  display: none;
}
.dropstart .dropdown-toggle::before {
  display: inline-block;
  margin-right: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid transparent;
  border-right: 0.3em solid;
  border-bottom: 0.3em solid transparent;
}
.dropstart .dropdown-toggle:empty::after {
  margin-left: 0;
}
.dropstart .dropdown-toggle::before {
  vertical-align: 0;
}

.dropdown-divider {
  height: 0;
  margin: var(--bs-dropdown-divider-margin-y) 0;
  overflow: hidden;
  border-top: 1px solid var(--bs-dropdown-divider-bg);
  opacity: 1;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: var(--bs-dropdown-item-padding-y) var(--bs-dropdown-item-padding-x);
  clear: both;
  font-weight: 400;
  color: var(--bs-dropdown-link-color);
  text-align: inherit;
  text-decoration: none;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
  border-radius: var(--bs-dropdown-item-border-radius, 0);
}
.dropdown-item:hover, .dropdown-item:focus {
  color: var(--bs-dropdown-link-hover-color);
  background-color: var(--bs-dropdown-link-hover-bg);
}
.dropdown-item.active, .dropdown-item:active {
  color: var(--bs-dropdown-link-active-color);
  text-decoration: none;
  background-color: var(--bs-dropdown-link-active-bg);
}
.dropdown-item.disabled, .dropdown-item:disabled {
  color: var(--bs-dropdown-link-disabled-color);
  pointer-events: none;
  background-color: transparent;
}

.dropdown-menu.show {
  display: block;
}

.dropdown-header {
  display: block;
  padding: var(--bs-dropdown-header-padding-y) var(--bs-dropdown-header-padding-x);
  margin-bottom: 0;
  font-size: 0.75rem;
  color: var(--bs-dropdown-header-color);
  white-space: nowrap;
}

.dropdown-item-text {
  display: block;
  padding: var(--bs-dropdown-item-padding-y) var(--bs-dropdown-item-padding-x);
  color: var(--bs-dropdown-link-color);
}

.dropdown-menu-dark {
  --bs-dropdown-color: #dee2e6;
  --bs-dropdown-bg: #343a40;
  --bs-dropdown-border-color: var(--bs-border-color-translucent);
  --bs-dropdown-box-shadow: ;
  --bs-dropdown-link-color: #dee2e6;
  --bs-dropdown-link-hover-color: #FFF9E6;
  --bs-dropdown-divider-bg: var(--bs-border-color-translucent);
  --bs-dropdown-link-hover-bg: rgba(255, 249, 230, 0.15);
  --bs-dropdown-link-active-color: #FFF9E6;
  --bs-dropdown-link-active-bg: #415143;
  --bs-dropdown-link-disabled-color: #adb5bd;
  --bs-dropdown-header-color: #adb5bd;
}

.btn, .wp-block-button__link,
body .editor-styles-wrapper .wp-block-button__link {
  --bs-btn-padding-x: 1.875rem;
  --bs-btn-padding-y: 0.4375rem;
  --bs-btn-font-family: ;
  --bs-btn-font-size:0.75rem;
  --bs-btn-font-weight: 400;
  --bs-btn-line-height: 1.625;
  --bs-btn-color: var(--bs-body-color);
  --bs-btn-bg: transparent;
  --bs-btn-border-width: var(--bs-border-width);
  --bs-btn-border-color: transparent;
  --bs-btn-border-radius: 0;
  --bs-btn-hover-border-color: transparent;
  --bs-btn-box-shadow: inset 0 1px 0 rgba(255, 249, 230, 0.15), 0 1px 1px rgba(40, 23, 15, 0.075);
  --bs-btn-disabled-opacity: 0.65;
  --bs-btn-focus-box-shadow: 0 0 0 0.25rem rgba(var(--bs-btn-focus-shadow-rgb), .5);
  display: inline-block;
  padding: var(--bs-btn-padding-y) var(--bs-btn-padding-x);
  font-family: var(--bs-btn-font-family);
  font-size: var(--bs-btn-font-size);
  font-weight: var(--bs-btn-font-weight);
  line-height: var(--bs-btn-line-height);
  color: var(--bs-btn-color);
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: var(--bs-btn-border-width) solid var(--bs-btn-border-color);
  border-radius: var(--bs-btn-border-radius);
  background-color: var(--bs-btn-bg);
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .btn, .wp-block-button__link,
body .editor-styles-wrapper .wp-block-button__link {
    transition: none;
  }
}
.btn:hover, .wp-block-button__link:hover,
body .editor-styles-wrapper .wp-block-button__link:hover {
  color: var(--bs-btn-hover-color);
  background-color: var(--bs-btn-hover-bg);
  border-color: var(--bs-btn-hover-border-color);
}
.btn-check + .btn:hover, .btn-check + .wp-block-button__link:hover,
body .editor-styles-wrapper .btn-check + .wp-block-button__link:hover {
  color: var(--bs-btn-color);
  background-color: var(--bs-btn-bg);
  border-color: var(--bs-btn-border-color);
}
.btn:focus-visible, .wp-block-button__link:focus-visible,
body .editor-styles-wrapper .wp-block-button__link:focus-visible {
  color: var(--bs-btn-hover-color);
  background-color: var(--bs-btn-hover-bg);
  border-color: var(--bs-btn-hover-border-color);
  outline: 0;
  box-shadow: var(--bs-btn-focus-box-shadow);
}
.btn-check:focus-visible + .btn, .btn-check:focus-visible + .wp-block-button__link,
body .editor-styles-wrapper .btn-check:focus-visible + .wp-block-button__link {
  border-color: var(--bs-btn-hover-border-color);
  outline: 0;
  box-shadow: var(--bs-btn-focus-box-shadow);
}
.btn-check:checked + .btn, .btn-check:checked + .wp-block-button__link,
body .editor-styles-wrapper .btn-check:checked + .wp-block-button__link, :not(.btn-check) + .btn:active, :not(.btn-check) + .wp-block-button__link:active,
body .editor-styles-wrapper :not(.btn-check) + .wp-block-button__link:active, .btn:first-child:active, .wp-block-button__link:first-child:active, .btn.active, .active.wp-block-button__link,
body .editor-styles-wrapper .active.wp-block-button__link, .btn.show, .show.wp-block-button__link,
body .editor-styles-wrapper .show.wp-block-button__link {
  color: var(--bs-btn-active-color);
  background-color: var(--bs-btn-active-bg);
  border-color: var(--bs-btn-active-border-color);
}
.btn-check:checked + .btn:focus-visible, .btn-check:checked + .wp-block-button__link:focus-visible,
body .editor-styles-wrapper .btn-check:checked + .wp-block-button__link:focus-visible, :not(.btn-check) + .btn:active:focus-visible, :not(.btn-check) + .wp-block-button__link:active:focus-visible,
body .editor-styles-wrapper :not(.btn-check) + .wp-block-button__link:active:focus-visible, .btn:first-child:active:focus-visible, .wp-block-button__link:first-child:active:focus-visible, .btn.active:focus-visible, .active.wp-block-button__link:focus-visible, .btn.show:focus-visible, .show.wp-block-button__link:focus-visible {
  box-shadow: var(--bs-btn-focus-box-shadow);
}
.btn-check:checked:focus-visible + .btn, .btn-check:checked:focus-visible + .wp-block-button__link,
body .editor-styles-wrapper .btn-check:checked:focus-visible + .wp-block-button__link {
  box-shadow: var(--bs-btn-focus-box-shadow);
}
.btn:disabled, .wp-block-button__link:disabled,
body .editor-styles-wrapper .wp-block-button__link:disabled, .btn.disabled, .disabled.wp-block-button__link,
body .editor-styles-wrapper .disabled.wp-block-button__link, fieldset:disabled .btn, fieldset:disabled .wp-block-button__link {
  color: var(--bs-btn-disabled-color);
  pointer-events: none;
  background-color: var(--bs-btn-disabled-bg);
  border-color: var(--bs-btn-disabled-border-color);
  opacity: var(--bs-btn-disabled-opacity);
}

.btn-primary, body .editor-styles-wrapper .wp-block-button.is-style-fill > .wp-block-button__link,
body .wp-block-button.is-style-fill > .wp-block-button__link {
  --bs-btn-color: #FFF9E6;
  --bs-btn-bg: #415143;
  --bs-btn-border-color: #415143;
  --bs-btn-hover-color: #FFF9E6;
  --bs-btn-hover-bg: #374539;
  --bs-btn-hover-border-color: #344136;
  --bs-btn-focus-shadow-rgb: 94, 106, 91;
  --bs-btn-active-color: #FFF9E6;
  --bs-btn-active-bg: #344136;
  --bs-btn-active-border-color: #313d32;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(40, 23, 15, 0.125);
  --bs-btn-disabled-color: #FFF9E6;
  --bs-btn-disabled-bg: #415143;
  --bs-btn-disabled-border-color: #415143;
}

.btn-secondary {
  --bs-btn-color: #28170F;
  --bs-btn-bg: #EC472E;
  --bs-btn-border-color: #EC472E;
  --bs-btn-hover-color: #28170F;
  --bs-btn-hover-bg: #ef634d;
  --bs-btn-hover-border-color: #ee5943;
  --bs-btn-focus-shadow-rgb: 207, 64, 41;
  --bs-btn-active-color: #28170F;
  --bs-btn-active-bg: #f06c58;
  --bs-btn-active-border-color: #ee5943;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(40, 23, 15, 0.125);
  --bs-btn-disabled-color: #28170F;
  --bs-btn-disabled-bg: #EC472E;
  --bs-btn-disabled-border-color: #EC472E;
}

.btn-success {
  --bs-btn-color: #FFF9E6;
  --bs-btn-bg: #415143;
  --bs-btn-border-color: #415143;
  --bs-btn-hover-color: #FFF9E6;
  --bs-btn-hover-bg: #374539;
  --bs-btn-hover-border-color: #344136;
  --bs-btn-focus-shadow-rgb: 94, 106, 91;
  --bs-btn-active-color: #FFF9E6;
  --bs-btn-active-bg: #344136;
  --bs-btn-active-border-color: #313d32;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(40, 23, 15, 0.125);
  --bs-btn-disabled-color: #FFF9E6;
  --bs-btn-disabled-bg: #415143;
  --bs-btn-disabled-border-color: #415143;
}

.btn-info {
  --bs-btn-color: #28170F;
  --bs-btn-bg: #6EA394;
  --bs-btn-border-color: #6EA394;
  --bs-btn-hover-color: #28170F;
  --bs-btn-hover-bg: #84b1a4;
  --bs-btn-hover-border-color: #7dac9f;
  --bs-btn-focus-shadow-rgb: 100, 142, 128;
  --bs-btn-active-color: #28170F;
  --bs-btn-active-bg: #8bb5a9;
  --bs-btn-active-border-color: #7dac9f;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(40, 23, 15, 0.125);
  --bs-btn-disabled-color: #28170F;
  --bs-btn-disabled-bg: #6EA394;
  --bs-btn-disabled-border-color: #6EA394;
}

.btn-warning {
  --bs-btn-color: #28170F;
  --bs-btn-bg: #6EA394;
  --bs-btn-border-color: #6EA394;
  --bs-btn-hover-color: #28170F;
  --bs-btn-hover-bg: #84b1a4;
  --bs-btn-hover-border-color: #7dac9f;
  --bs-btn-focus-shadow-rgb: 100, 142, 128;
  --bs-btn-active-color: #28170F;
  --bs-btn-active-bg: #8bb5a9;
  --bs-btn-active-border-color: #7dac9f;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(40, 23, 15, 0.125);
  --bs-btn-disabled-color: #28170F;
  --bs-btn-disabled-bg: #6EA394;
  --bs-btn-disabled-border-color: #6EA394;
}

.btn-danger {
  --bs-btn-color: #28170F;
  --bs-btn-bg: #EC472E;
  --bs-btn-border-color: #EC472E;
  --bs-btn-hover-color: #28170F;
  --bs-btn-hover-bg: #ef634d;
  --bs-btn-hover-border-color: #ee5943;
  --bs-btn-focus-shadow-rgb: 207, 64, 41;
  --bs-btn-active-color: #28170F;
  --bs-btn-active-bg: #f06c58;
  --bs-btn-active-border-color: #ee5943;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(40, 23, 15, 0.125);
  --bs-btn-disabled-color: #28170F;
  --bs-btn-disabled-bg: #EC472E;
  --bs-btn-disabled-border-color: #EC472E;
}

.btn-dark {
  --bs-btn-color: #FFF9E6;
  --bs-btn-bg: #28170F;
  --bs-btn-border-color: #28170F;
  --bs-btn-hover-color: #FFF9E6;
  --bs-btn-hover-bg: #483a33;
  --bs-btn-hover-border-color: #3e2e27;
  --bs-btn-focus-shadow-rgb: 72, 57, 47;
  --bs-btn-active-color: #FFF9E6;
  --bs-btn-active-bg: #53453f;
  --bs-btn-active-border-color: #3e2e27;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(40, 23, 15, 0.125);
  --bs-btn-disabled-color: #FFF9E6;
  --bs-btn-disabled-bg: #28170F;
  --bs-btn-disabled-border-color: #28170F;
}

.btn-green {
  --bs-btn-color: #FFF9E6;
  --bs-btn-bg: #415143;
  --bs-btn-border-color: #415143;
  --bs-btn-hover-color: #FFF9E6;
  --bs-btn-hover-bg: #374539;
  --bs-btn-hover-border-color: #344136;
  --bs-btn-focus-shadow-rgb: 94, 106, 91;
  --bs-btn-active-color: #FFF9E6;
  --bs-btn-active-bg: #344136;
  --bs-btn-active-border-color: #313d32;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(40, 23, 15, 0.125);
  --bs-btn-disabled-color: #FFF9E6;
  --bs-btn-disabled-bg: #415143;
  --bs-btn-disabled-border-color: #415143;
}

.btn-celadon {
  --bs-btn-color: #28170F;
  --bs-btn-bg: #6EA394;
  --bs-btn-border-color: #6EA394;
  --bs-btn-hover-color: #28170F;
  --bs-btn-hover-bg: #84b1a4;
  --bs-btn-hover-border-color: #7dac9f;
  --bs-btn-focus-shadow-rgb: 100, 142, 128;
  --bs-btn-active-color: #28170F;
  --bs-btn-active-bg: #8bb5a9;
  --bs-btn-active-border-color: #7dac9f;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(40, 23, 15, 0.125);
  --bs-btn-disabled-color: #28170F;
  --bs-btn-disabled-bg: #6EA394;
  --bs-btn-disabled-border-color: #6EA394;
}

.btn-red {
  --bs-btn-color: #28170F;
  --bs-btn-bg: #EC472E;
  --bs-btn-border-color: #EC472E;
  --bs-btn-hover-color: #28170F;
  --bs-btn-hover-bg: #ef634d;
  --bs-btn-hover-border-color: #ee5943;
  --bs-btn-focus-shadow-rgb: 207, 64, 41;
  --bs-btn-active-color: #28170F;
  --bs-btn-active-bg: #f06c58;
  --bs-btn-active-border-color: #ee5943;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(40, 23, 15, 0.125);
  --bs-btn-disabled-color: #28170F;
  --bs-btn-disabled-bg: #EC472E;
  --bs-btn-disabled-border-color: #EC472E;
}

.btn-warm {
  --bs-btn-color: #28170F;
  --bs-btn-bg: #FFF9E6;
  --bs-btn-border-color: #FFF9E6;
  --bs-btn-hover-color: #28170F;
  --bs-btn-hover-bg: #fffaea;
  --bs-btn-hover-border-color: #fffae9;
  --bs-btn-focus-shadow-rgb: 223, 215, 198;
  --bs-btn-active-color: #28170F;
  --bs-btn-active-bg: #fffaeb;
  --bs-btn-active-border-color: #fffae9;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(40, 23, 15, 0.125);
  --bs-btn-disabled-color: #28170F;
  --bs-btn-disabled-bg: #FFF9E6;
  --bs-btn-disabled-border-color: #FFF9E6;
}

.btn-summer {
  --bs-btn-color: #28170F;
  --bs-btn-bg: #a2a2a2;
  --bs-btn-border-color: #a2a2a2;
  --bs-btn-hover-color: #28170F;
  --bs-btn-hover-bg: #b0b0b0;
  --bs-btn-hover-border-color: #ababab;
  --bs-btn-focus-shadow-rgb: 144, 141, 140;
  --bs-btn-active-color: #28170F;
  --bs-btn-active-bg: #b5b5b5;
  --bs-btn-active-border-color: #ababab;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(40, 23, 15, 0.125);
  --bs-btn-disabled-color: #28170F;
  --bs-btn-disabled-bg: #a2a2a2;
  --bs-btn-disabled-border-color: #a2a2a2;
}

.btn-autumn {
  --bs-btn-color: #28170F;
  --bs-btn-bg: #a2a2a2;
  --bs-btn-border-color: #a2a2a2;
  --bs-btn-hover-color: #28170F;
  --bs-btn-hover-bg: #b0b0b0;
  --bs-btn-hover-border-color: #ababab;
  --bs-btn-focus-shadow-rgb: 144, 141, 140;
  --bs-btn-active-color: #28170F;
  --bs-btn-active-bg: #b5b5b5;
  --bs-btn-active-border-color: #ababab;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(40, 23, 15, 0.125);
  --bs-btn-disabled-color: #28170F;
  --bs-btn-disabled-bg: #a2a2a2;
  --bs-btn-disabled-border-color: #a2a2a2;
}

.btn-winter {
  --bs-btn-color: #28170F;
  --bs-btn-bg: #a2a2a2;
  --bs-btn-border-color: #a2a2a2;
  --bs-btn-hover-color: #28170F;
  --bs-btn-hover-bg: #b0b0b0;
  --bs-btn-hover-border-color: #ababab;
  --bs-btn-focus-shadow-rgb: 144, 141, 140;
  --bs-btn-active-color: #28170F;
  --bs-btn-active-bg: #b5b5b5;
  --bs-btn-active-border-color: #ababab;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(40, 23, 15, 0.125);
  --bs-btn-disabled-color: #28170F;
  --bs-btn-disabled-bg: #a2a2a2;
  --bs-btn-disabled-border-color: #a2a2a2;
}

.btn-spring {
  --bs-btn-color: #28170F;
  --bs-btn-bg: #a2a2a2;
  --bs-btn-border-color: #a2a2a2;
  --bs-btn-hover-color: #28170F;
  --bs-btn-hover-bg: #b0b0b0;
  --bs-btn-hover-border-color: #ababab;
  --bs-btn-focus-shadow-rgb: 144, 141, 140;
  --bs-btn-active-color: #28170F;
  --bs-btn-active-bg: #b5b5b5;
  --bs-btn-active-border-color: #ababab;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(40, 23, 15, 0.125);
  --bs-btn-disabled-color: #28170F;
  --bs-btn-disabled-bg: #a2a2a2;
  --bs-btn-disabled-border-color: #a2a2a2;
}

.btn-outline-primary, body .editor-styles-wrapper .wp-block-button.is-style-outline > .wp-block-button__link,
body .wp-block-button.is-style-outline > .wp-block-button__link,
body .wp-block-button.is-style-outline:not(.has-text-color) > .wp-block-button__link {
  --bs-btn-color: #415143;
  --bs-btn-border-color: #415143;
  --bs-btn-hover-color: #FFF9E6;
  --bs-btn-hover-bg: #415143;
  --bs-btn-hover-border-color: #415143;
  --bs-btn-focus-shadow-rgb: 65, 81, 67;
  --bs-btn-active-color: #FFF9E6;
  --bs-btn-active-bg: #415143;
  --bs-btn-active-border-color: #415143;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(40, 23, 15, 0.125);
  --bs-btn-disabled-color: #415143;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #415143;
  --bs-gradient: none;
}

.btn-outline-secondary, body .editor-styles-wrapper .wp-block-button.is-style-outline-secondary > .wp-block-button__link,
body .wp-block-button.is-style-outline-secondary > .wp-block-button__link {
  --bs-btn-color: #EC472E;
  --bs-btn-border-color: #EC472E;
  --bs-btn-hover-color: #28170F;
  --bs-btn-hover-bg: #EC472E;
  --bs-btn-hover-border-color: #EC472E;
  --bs-btn-focus-shadow-rgb: 236, 71, 46;
  --bs-btn-active-color: #28170F;
  --bs-btn-active-bg: #EC472E;
  --bs-btn-active-border-color: #EC472E;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(40, 23, 15, 0.125);
  --bs-btn-disabled-color: #EC472E;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #EC472E;
  --bs-gradient: none;
}

.btn-outline-success {
  --bs-btn-color: #415143;
  --bs-btn-border-color: #415143;
  --bs-btn-hover-color: #FFF9E6;
  --bs-btn-hover-bg: #415143;
  --bs-btn-hover-border-color: #415143;
  --bs-btn-focus-shadow-rgb: 65, 81, 67;
  --bs-btn-active-color: #FFF9E6;
  --bs-btn-active-bg: #415143;
  --bs-btn-active-border-color: #415143;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(40, 23, 15, 0.125);
  --bs-btn-disabled-color: #415143;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #415143;
  --bs-gradient: none;
}

.btn-outline-info {
  --bs-btn-color: #6EA394;
  --bs-btn-border-color: #6EA394;
  --bs-btn-hover-color: #28170F;
  --bs-btn-hover-bg: #6EA394;
  --bs-btn-hover-border-color: #6EA394;
  --bs-btn-focus-shadow-rgb: 110, 163, 148;
  --bs-btn-active-color: #28170F;
  --bs-btn-active-bg: #6EA394;
  --bs-btn-active-border-color: #6EA394;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(40, 23, 15, 0.125);
  --bs-btn-disabled-color: #6EA394;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #6EA394;
  --bs-gradient: none;
}

.btn-outline-warning {
  --bs-btn-color: #6EA394;
  --bs-btn-border-color: #6EA394;
  --bs-btn-hover-color: #28170F;
  --bs-btn-hover-bg: #6EA394;
  --bs-btn-hover-border-color: #6EA394;
  --bs-btn-focus-shadow-rgb: 110, 163, 148;
  --bs-btn-active-color: #28170F;
  --bs-btn-active-bg: #6EA394;
  --bs-btn-active-border-color: #6EA394;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(40, 23, 15, 0.125);
  --bs-btn-disabled-color: #6EA394;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #6EA394;
  --bs-gradient: none;
}

.btn-outline-danger {
  --bs-btn-color: #EC472E;
  --bs-btn-border-color: #EC472E;
  --bs-btn-hover-color: #28170F;
  --bs-btn-hover-bg: #EC472E;
  --bs-btn-hover-border-color: #EC472E;
  --bs-btn-focus-shadow-rgb: 236, 71, 46;
  --bs-btn-active-color: #28170F;
  --bs-btn-active-bg: #EC472E;
  --bs-btn-active-border-color: #EC472E;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(40, 23, 15, 0.125);
  --bs-btn-disabled-color: #EC472E;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #EC472E;
  --bs-gradient: none;
}

.btn-outline-dark {
  --bs-btn-color: #28170F;
  --bs-btn-border-color: #28170F;
  --bs-btn-hover-color: #FFF9E6;
  --bs-btn-hover-bg: #28170F;
  --bs-btn-hover-border-color: #28170F;
  --bs-btn-focus-shadow-rgb: 40, 23, 15;
  --bs-btn-active-color: #FFF9E6;
  --bs-btn-active-bg: #28170F;
  --bs-btn-active-border-color: #28170F;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(40, 23, 15, 0.125);
  --bs-btn-disabled-color: #28170F;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #28170F;
  --bs-gradient: none;
}

.btn-outline-green {
  --bs-btn-color: #415143;
  --bs-btn-border-color: #415143;
  --bs-btn-hover-color: #FFF9E6;
  --bs-btn-hover-bg: #415143;
  --bs-btn-hover-border-color: #415143;
  --bs-btn-focus-shadow-rgb: 65, 81, 67;
  --bs-btn-active-color: #FFF9E6;
  --bs-btn-active-bg: #415143;
  --bs-btn-active-border-color: #415143;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(40, 23, 15, 0.125);
  --bs-btn-disabled-color: #415143;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #415143;
  --bs-gradient: none;
}

.btn-outline-celadon {
  --bs-btn-color: #6EA394;
  --bs-btn-border-color: #6EA394;
  --bs-btn-hover-color: #28170F;
  --bs-btn-hover-bg: #6EA394;
  --bs-btn-hover-border-color: #6EA394;
  --bs-btn-focus-shadow-rgb: 110, 163, 148;
  --bs-btn-active-color: #28170F;
  --bs-btn-active-bg: #6EA394;
  --bs-btn-active-border-color: #6EA394;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(40, 23, 15, 0.125);
  --bs-btn-disabled-color: #6EA394;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #6EA394;
  --bs-gradient: none;
}

.btn-outline-red {
  --bs-btn-color: #EC472E;
  --bs-btn-border-color: #EC472E;
  --bs-btn-hover-color: #28170F;
  --bs-btn-hover-bg: #EC472E;
  --bs-btn-hover-border-color: #EC472E;
  --bs-btn-focus-shadow-rgb: 236, 71, 46;
  --bs-btn-active-color: #28170F;
  --bs-btn-active-bg: #EC472E;
  --bs-btn-active-border-color: #EC472E;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(40, 23, 15, 0.125);
  --bs-btn-disabled-color: #EC472E;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #EC472E;
  --bs-gradient: none;
}

.btn-outline-warm {
  --bs-btn-color: #FFF9E6;
  --bs-btn-border-color: #FFF9E6;
  --bs-btn-hover-color: #28170F;
  --bs-btn-hover-bg: #FFF9E6;
  --bs-btn-hover-border-color: #FFF9E6;
  --bs-btn-focus-shadow-rgb: 255, 249, 230;
  --bs-btn-active-color: #28170F;
  --bs-btn-active-bg: #FFF9E6;
  --bs-btn-active-border-color: #FFF9E6;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(40, 23, 15, 0.125);
  --bs-btn-disabled-color: #FFF9E6;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #FFF9E6;
  --bs-gradient: none;
}

.btn-outline-summer {
  --bs-btn-color: #a2a2a2;
  --bs-btn-border-color: #a2a2a2;
  --bs-btn-hover-color: #28170F;
  --bs-btn-hover-bg: #a2a2a2;
  --bs-btn-hover-border-color: #a2a2a2;
  --bs-btn-focus-shadow-rgb: 162, 162, 162;
  --bs-btn-active-color: #28170F;
  --bs-btn-active-bg: #a2a2a2;
  --bs-btn-active-border-color: #a2a2a2;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(40, 23, 15, 0.125);
  --bs-btn-disabled-color: #a2a2a2;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #a2a2a2;
  --bs-gradient: none;
}

.btn-outline-autumn {
  --bs-btn-color: #a2a2a2;
  --bs-btn-border-color: #a2a2a2;
  --bs-btn-hover-color: #28170F;
  --bs-btn-hover-bg: #a2a2a2;
  --bs-btn-hover-border-color: #a2a2a2;
  --bs-btn-focus-shadow-rgb: 162, 162, 162;
  --bs-btn-active-color: #28170F;
  --bs-btn-active-bg: #a2a2a2;
  --bs-btn-active-border-color: #a2a2a2;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(40, 23, 15, 0.125);
  --bs-btn-disabled-color: #a2a2a2;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #a2a2a2;
  --bs-gradient: none;
}

.btn-outline-winter {
  --bs-btn-color: #a2a2a2;
  --bs-btn-border-color: #a2a2a2;
  --bs-btn-hover-color: #28170F;
  --bs-btn-hover-bg: #a2a2a2;
  --bs-btn-hover-border-color: #a2a2a2;
  --bs-btn-focus-shadow-rgb: 162, 162, 162;
  --bs-btn-active-color: #28170F;
  --bs-btn-active-bg: #a2a2a2;
  --bs-btn-active-border-color: #a2a2a2;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(40, 23, 15, 0.125);
  --bs-btn-disabled-color: #a2a2a2;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #a2a2a2;
  --bs-gradient: none;
}

.btn-outline-spring {
  --bs-btn-color: #a2a2a2;
  --bs-btn-border-color: #a2a2a2;
  --bs-btn-hover-color: #28170F;
  --bs-btn-hover-bg: #a2a2a2;
  --bs-btn-hover-border-color: #a2a2a2;
  --bs-btn-focus-shadow-rgb: 162, 162, 162;
  --bs-btn-active-color: #28170F;
  --bs-btn-active-bg: #a2a2a2;
  --bs-btn-active-border-color: #a2a2a2;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(40, 23, 15, 0.125);
  --bs-btn-disabled-color: #a2a2a2;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #a2a2a2;
  --bs-gradient: none;
}

.btn-link {
  --bs-btn-font-weight: 400;
  --bs-btn-color: var(--bs-link-color);
  --bs-btn-bg: transparent;
  --bs-btn-border-color: transparent;
  --bs-btn-hover-color: var(--bs-link-hover-color);
  --bs-btn-hover-border-color: transparent;
  --bs-btn-active-color: var(--bs-link-hover-color);
  --bs-btn-active-border-color: transparent;
  --bs-btn-disabled-color: #6c757d;
  --bs-btn-disabled-border-color: transparent;
  --bs-btn-box-shadow: 0 0 0 #000;
  --bs-btn-focus-shadow-rgb: 72, 57, 47;
  text-decoration: underline;
}
.btn-link:focus-visible {
  color: var(--bs-btn-color);
}
.btn-link:hover {
  color: var(--bs-btn-hover-color);
}

.btn-lg {
  --bs-btn-padding-y: 0.5rem;
  --bs-btn-padding-x: 1rem;
  --bs-btn-font-size:1.25rem;
  --bs-btn-border-radius: 0;
}

.btn-sm {
  --bs-btn-padding-y: 0.25rem;
  --bs-btn-padding-x: 0.5rem;
  --bs-btn-font-size:0.75rem;
  --bs-btn-border-radius: var(--bs-border-radius-sm);
}

.form-label {
  margin-bottom: 0;
  font-size: 1rem;
}

.col-form-label {
  padding-top: calc(0 + var(--bs-border-width));
  padding-bottom: calc(0 + var(--bs-border-width));
  margin-bottom: 0;
  font-size: inherit;
  line-height: 1.625;
}

.col-form-label-lg {
  padding-top: calc(0.5rem + var(--bs-border-width));
  padding-bottom: calc(0.5rem + var(--bs-border-width));
  font-size: 1.25rem;
}

.col-form-label-sm {
  padding-top: calc(0.25rem + var(--bs-border-width));
  padding-bottom: calc(0.25rem + var(--bs-border-width));
  font-size: 0.75rem;
}

.form-text {
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: var(--bs-secondary-color);
}

.form-control {
  display: block;
  width: 100%;
  padding: 0 0.3125rem;
  font-size: 1rem;
  font-weight: 500;
  line-height: 1.625;
  color: #415143;
  appearance: none;
  background-color: #FFF9E6;
  background-clip: padding-box;
  border: var(--bs-border-width) solid #28170F;
  border-radius: var(--bs-border-radius);
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-control {
    transition: none;
  }
}
.form-control[type=file] {
  overflow: hidden;
}
.form-control[type=file]:not(:disabled):not([readonly]) {
  cursor: pointer;
}
.form-control:focus {
  color: #415143;
  background-color: #FFF9E6;
  border-color: #a0a8a1;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(65, 81, 67, 0.25);
}
.form-control::-webkit-date-and-time-value {
  min-width: 85px;
  height: 1.625em;
  margin: 0;
}
.form-control::-webkit-datetime-edit {
  display: block;
  padding: 0;
}
.form-control::placeholder {
  color: var(--bs-secondary-color);
  opacity: 1;
}
.form-control:disabled {
  background-color: var(--bs-secondary-bg);
  opacity: 1;
}
.form-control::file-selector-button {
  padding: 0 0.3125rem;
  margin: 0 -0.3125rem;
  margin-inline-end: 0.3125rem;
  color: #415143;
  background-color: var(--bs-tertiary-bg);
  pointer-events: none;
  border-color: inherit;
  border-style: solid;
  border-width: 0;
  border-inline-end-width: var(--bs-border-width);
  border-radius: 0;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-control::file-selector-button {
    transition: none;
  }
}
.form-control:hover:not(:disabled):not([readonly])::file-selector-button {
  background-color: var(--bs-secondary-bg);
}

.form-control-plaintext {
  display: block;
  width: 100%;
  padding: 0 0;
  margin-bottom: 0;
  line-height: 1.625;
  color: #415143;
  background-color: transparent;
  border: solid transparent;
  border-width: var(--bs-border-width) 0;
}
.form-control-plaintext:focus {
  outline: 0;
}
.form-control-plaintext.form-control-sm, .form-control-plaintext.form-control-lg {
  padding-right: 0;
  padding-left: 0;
}

.form-control-sm {
  min-height: calc(1.625em + 0.5rem + calc(var(--bs-border-width) * 2));
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  border-radius: var(--bs-border-radius-sm);
}
.form-control-sm::file-selector-button {
  padding: 0.25rem 0.5rem;
  margin: -0.25rem -0.5rem;
  margin-inline-end: 0.5rem;
}

.form-control-lg {
  min-height: calc(1.625em + 1rem + calc(var(--bs-border-width) * 2));
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  border-radius: var(--bs-border-radius-lg);
}
.form-control-lg::file-selector-button {
  padding: 0.5rem 1rem;
  margin: -0.5rem -1rem;
  margin-inline-end: 1rem;
}

textarea.form-control {
  min-height: calc(1.625em + 0 + calc(var(--bs-border-width) * 2));
}
textarea.form-control-sm {
  min-height: calc(1.625em + 0.5rem + calc(var(--bs-border-width) * 2));
}
textarea.form-control-lg {
  min-height: calc(1.625em + 1rem + calc(var(--bs-border-width) * 2));
}

.form-control-color {
  width: 3rem;
  height: calc(1.625em + 0 + calc(var(--bs-border-width) * 2));
  padding: 0;
}
.form-control-color:not(:disabled):not([readonly]) {
  cursor: pointer;
}
.form-control-color::-moz-color-swatch {
  border: 0 !important;
  border-radius: var(--bs-border-radius);
}
.form-control-color::-webkit-color-swatch {
  border: 0 !important;
  border-radius: var(--bs-border-radius);
}
.form-control-color.form-control-sm {
  height: calc(1.625em + 0.5rem + calc(var(--bs-border-width) * 2));
}
.form-control-color.form-control-lg {
  height: calc(1.625em + 1rem + calc(var(--bs-border-width) * 2));
}

.form-select {
  --bs-form-select-bg-img: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e");
  display: block;
  width: 100%;
  padding: 0 0.9375rem 0 0.3125rem;
  font-size: 1rem;
  font-weight: 500;
  line-height: 1.625;
  color: #415143;
  appearance: none;
  background-color: #FFF9E6;
  background-image: var(--bs-form-select-bg-img), var(--bs-form-select-bg-icon, none);
  background-repeat: no-repeat;
  background-position: right 0.3125rem center;
  background-size: 16px 12px;
  border: var(--bs-border-width) solid #28170F;
  border-radius: var(--bs-border-radius);
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-select {
    transition: none;
  }
}
.form-select:focus {
  border-color: #a0a8a1;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(65, 81, 67, 0.25);
}
.form-select[multiple], .form-select[size]:not([size="1"]) {
  padding-right: 0.3125rem;
  background-image: none;
}
.form-select:disabled {
  background-color: var(--bs-secondary-bg);
}
.form-select:-moz-focusring {
  color: transparent;
  text-shadow: 0 0 0 #415143;
}

.form-select-sm {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  padding-left: 0.5rem;
  font-size: 0.75rem;
  border-radius: var(--bs-border-radius-sm);
}

.form-select-lg {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  font-size: 1.25rem;
  border-radius: var(--bs-border-radius-lg);
}

[data-bs-theme=dark] .form-select {
  --bs-form-select-bg-img: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23dee2e6' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e");
}

.form-check {
  display: block;
  min-height: 1.625rem;
  padding-left: 1.5em;
  margin-bottom: 0.125rem;
}
.form-check .form-check-input {
  float: left;
  margin-left: -1.5em;
}

.form-check-reverse {
  padding-right: 1.5em;
  padding-left: 0;
  text-align: right;
}
.form-check-reverse .form-check-input {
  float: right;
  margin-right: -1.5em;
  margin-left: 0;
}

.form-check-input {
  --bs-form-check-bg: #FFF9E6;
  flex-shrink: 0;
  width: 1em;
  height: 1em;
  margin-top: 0.3125em;
  vertical-align: top;
  appearance: none;
  background-color: var(--bs-form-check-bg);
  background-image: var(--bs-form-check-bg-image);
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  border: 1px solid;
  print-color-adjust: exact;
}
.form-check-input[type=checkbox] {
  border-radius: 0;
}
.form-check-input[type=radio] {
  border-radius: 50%;
}
.form-check-input:active {
  filter: brightness(90%);
}
.form-check-input:focus {
  border-color: #a0a8a1;
  outline: 0;
  box-shadow: none;
}
.form-check-input:checked {
  background-color: #415143;
  border-color: #415143;
}
.form-check-input:checked[type=checkbox] {
  --bs-form-check-bg-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23FFF9E6' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/%3e%3c/svg%3e");
}
.form-check-input:checked[type=radio] {
  --bs-form-check-bg-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23FFF9E6'/%3e%3c/svg%3e");
}
.form-check-input[type=checkbox]:indeterminate {
  background-color: #415143;
  border-color: #415143;
  --bs-form-check-bg-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23FFF9E6' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e");
}
.form-check-input:disabled {
  pointer-events: none;
  filter: none;
  opacity: 0.5;
}
.form-check-input[disabled] ~ .form-check-label, .form-check-input:disabled ~ .form-check-label {
  cursor: default;
  opacity: 0.5;
}

.form-switch {
  padding-left: 2.5em;
}
.form-switch .form-check-input {
  --bs-form-switch-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%2840, 23, 15, 0.25%29'/%3e%3c/svg%3e");
  width: 2em;
  margin-left: -2.5em;
  background-image: var(--bs-form-switch-bg);
  background-position: left center;
  border-radius: 2em;
  transition: background-position 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-switch .form-check-input {
    transition: none;
  }
}
.form-switch .form-check-input:focus {
  --bs-form-switch-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23a0a8a1'/%3e%3c/svg%3e");
}
.form-switch .form-check-input:checked {
  background-position: right center;
  --bs-form-switch-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23FFF9E6'/%3e%3c/svg%3e");
}
.form-switch.form-check-reverse {
  padding-right: 2.5em;
  padding-left: 0;
}
.form-switch.form-check-reverse .form-check-input {
  margin-right: -2.5em;
  margin-left: 0;
}

.form-check-inline {
  display: inline-block;
  margin-right: 1rem;
}

.btn-check {
  position: absolute;
  clip: rect(0, 0, 0, 0);
  pointer-events: none;
}
.btn-check[disabled] + .btn, .btn-check[disabled] + .wp-block-button__link,
body .editor-styles-wrapper .btn-check[disabled] + .wp-block-button__link, .btn-check:disabled + .btn, .btn-check:disabled + .wp-block-button__link,
body .editor-styles-wrapper .btn-check:disabled + .wp-block-button__link {
  pointer-events: none;
  filter: none;
  opacity: 0.65;
}

[data-bs-theme=dark] .form-switch .form-check-input:not(:checked):not(:focus) {
  --bs-form-switch-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%28255, 249, 230, 0.25%29'/%3e%3c/svg%3e");
}

.form-range {
  width: 100%;
  height: 1.5rem;
  padding: 0;
  appearance: none;
  background-color: transparent;
}
.form-range:focus {
  outline: 0;
}
.form-range:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 1px #FFF9E6, 0 0 0 0.25rem rgba(65, 81, 67, 0.25);
}
.form-range:focus::-moz-range-thumb {
  box-shadow: 0 0 0 1px #FFF9E6, 0 0 0 0.25rem rgba(65, 81, 67, 0.25);
}
.form-range::-moz-focus-outer {
  border: 0;
}
.form-range::-webkit-slider-thumb {
  width: 1rem;
  height: 1rem;
  margin-top: -0.25rem;
  appearance: none;
  background-color: #415143;
  border: 0;
  border-radius: 1rem;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-range::-webkit-slider-thumb {
    transition: none;
  }
}
.form-range::-webkit-slider-thumb:active {
  background-color: #c6cbc7;
}
.form-range::-webkit-slider-runnable-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: var(--bs-secondary-bg);
  border-color: transparent;
  border-radius: 1rem;
}
.form-range::-moz-range-thumb {
  width: 1rem;
  height: 1rem;
  appearance: none;
  background-color: #415143;
  border: 0;
  border-radius: 1rem;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-range::-moz-range-thumb {
    transition: none;
  }
}
.form-range::-moz-range-thumb:active {
  background-color: #c6cbc7;
}
.form-range::-moz-range-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: var(--bs-secondary-bg);
  border-color: transparent;
  border-radius: 1rem;
}
.form-range:disabled {
  pointer-events: none;
}
.form-range:disabled::-webkit-slider-thumb {
  background-color: var(--bs-secondary-color);
}
.form-range:disabled::-moz-range-thumb {
  background-color: var(--bs-secondary-color);
}

.form-floating {
  position: relative;
}
.form-floating > .form-control,
.form-floating > .form-control-plaintext,
.form-floating > .form-select {
  height: calc(3.5rem + calc(var(--bs-border-width) * 2));
  min-height: calc(3.5rem + calc(var(--bs-border-width) * 2));
  line-height: 1.25;
}
.form-floating > label {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  max-width: 100%;
  height: 100%;
  padding: 1rem 0.3125rem;
  overflow: hidden;
  color: rgba(var(--bs-body-color-rgb), 0.65);
  text-align: start;
  text-overflow: ellipsis;
  white-space: nowrap;
  pointer-events: none;
  border: var(--bs-border-width) solid transparent;
  transform-origin: 0 0;
  transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-floating > label {
    transition: none;
  }
}
.form-floating > .form-control,
.form-floating > .form-control-plaintext {
  padding: 1rem 0.3125rem;
}
.form-floating > .form-control::placeholder,
.form-floating > .form-control-plaintext::placeholder {
  color: transparent;
}
.form-floating > .form-control:focus, .form-floating > .form-control:not(:placeholder-shown),
.form-floating > .form-control-plaintext:focus,
.form-floating > .form-control-plaintext:not(:placeholder-shown) {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.form-floating > .form-control:-webkit-autofill,
.form-floating > .form-control-plaintext:-webkit-autofill {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.form-floating > .form-select {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
  padding-left: 0.3125rem;
}
.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label,
.form-floating > .form-control-plaintext ~ label,
.form-floating > .form-select ~ label {
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}
.form-floating > .form-control:-webkit-autofill ~ label {
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}
.form-floating > textarea:focus ~ label::after,
.form-floating > textarea:not(:placeholder-shown) ~ label::after {
  position: absolute;
  inset: 1rem 0.15625rem;
  z-index: -1;
  height: 1.5em;
  content: "";
  background-color: #FFF9E6;
  border-radius: var(--bs-border-radius);
}
.form-floating > textarea:disabled ~ label::after {
  background-color: var(--bs-secondary-bg);
}
.form-floating > .form-control-plaintext ~ label {
  border-width: var(--bs-border-width) 0;
}
.form-floating > :disabled ~ label,
.form-floating > .form-control:disabled ~ label {
  color: #6c757d;
}

.input-group {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  width: 100%;
}
.input-group > .form-control,
.input-group > .form-select,
.input-group > .form-floating {
  position: relative;
  flex: 1 1 auto;
  width: 1%;
  min-width: 0;
}
.input-group > .form-control:focus,
.input-group > .form-select:focus,
.input-group > .form-floating:focus-within {
  z-index: 5;
}
.input-group .btn, .input-group .wp-block-button__link,
.input-group body .editor-styles-wrapper .wp-block-button__link,
body .editor-styles-wrapper .input-group .wp-block-button__link {
  position: relative;
  z-index: 2;
}
.input-group .btn:focus, .input-group .wp-block-button__link:focus {
  z-index: 5;
}

.input-group-text {
  display: flex;
  align-items: center;
  padding: 0 0.3125rem;
  font-size: 1rem;
  font-weight: 500;
  line-height: 1.625;
  color: #415143;
  text-align: center;
  white-space: nowrap;
  background-color: var(--bs-tertiary-bg);
  border: var(--bs-border-width) solid #28170F;
  border-radius: var(--bs-border-radius);
}

.input-group-lg > .form-control,
.input-group-lg > .form-select,
.input-group-lg > .input-group-text,
.input-group-lg > .btn,
.input-group-lg > .wp-block-button__link,
body .editor-styles-wrapper .input-group-lg > .wp-block-button__link {
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  border-radius: var(--bs-border-radius-lg);
}

.input-group-sm > .form-control,
.input-group-sm > .form-select,
.input-group-sm > .input-group-text,
.input-group-sm > .btn,
.input-group-sm > .wp-block-button__link,
body .editor-styles-wrapper .input-group-sm > .wp-block-button__link {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  border-radius: var(--bs-border-radius-sm);
}

.input-group-lg > .form-select,
.input-group-sm > .form-select {
  padding-right: 1.25rem;
}

.input-group:not(.has-validation) > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating),
.input-group:not(.has-validation) > .dropdown-toggle:nth-last-child(n+3),
.input-group:not(.has-validation) > .form-floating:not(:last-child) > .form-control,
.input-group:not(.has-validation) > .form-floating:not(:last-child) > .form-select {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.input-group.has-validation > :nth-last-child(n+3):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating),
.input-group.has-validation > .dropdown-toggle:nth-last-child(n+4),
.input-group.has-validation > .form-floating:nth-last-child(n+3) > .form-control,
.input-group.has-validation > .form-floating:nth-last-child(n+3) > .form-select {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
  margin-left: calc(-1 * var(--bs-border-width));
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.input-group > .form-floating:not(:first-child) > .form-control,
.input-group > .form-floating:not(:first-child) > .form-select {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.valid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: var(--bs-form-valid-color);
}

.valid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.15625rem 0.3125rem;
  margin-top: 0.1rem;
  font-size: 0.75rem;
  color: #fff;
  background-color: var(--bs-success);
  border-radius: var(--bs-border-radius);
}

.was-validated :valid ~ .valid-feedback,
.was-validated :valid ~ .valid-tooltip,
.is-valid ~ .valid-feedback,
.is-valid ~ .valid-tooltip {
  display: block;
}

.was-validated .form-control:valid, .form-control.is-valid {
  border-color: var(--bs-form-valid-border-color);
  padding-right: 1.625em;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23415143' d='M2.3 6.73.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.40625em center;
  background-size: 0.8125em 0.8125em;
}
.was-validated .form-control:valid:focus, .form-control.is-valid:focus {
  border-color: var(--bs-form-valid-border-color);
  box-shadow: 0 0 0 0.25rem rgba(var(--bs-success-rgb), 0.25);
}

.was-validated textarea.form-control:valid, textarea.form-control.is-valid {
  padding-right: 1.625em;
  background-position: top 0.40625em right 0.40625em;
}

.was-validated .form-select:valid, .form-select.is-valid {
  border-color: var(--bs-form-valid-border-color);
}
.was-validated .form-select:valid:not([multiple]):not([size]), .was-validated .form-select:valid:not([multiple])[size="1"], .form-select.is-valid:not([multiple]):not([size]), .form-select.is-valid:not([multiple])[size="1"] {
  --bs-form-select-bg-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23415143' d='M2.3 6.73.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1'/%3e%3c/svg%3e");
  padding-right: 1.71875rem;
  background-position: right 0.3125rem center, center right 0.9375rem;
  background-size: 16px 12px, 0.8125em 0.8125em;
}
.was-validated .form-select:valid:focus, .form-select.is-valid:focus {
  border-color: var(--bs-form-valid-border-color);
  box-shadow: 0 0 0 0.25rem rgba(var(--bs-success-rgb), 0.25);
}

.was-validated .form-control-color:valid, .form-control-color.is-valid {
  width: calc(3rem + 1.625em);
}

.was-validated .form-check-input:valid, .form-check-input.is-valid {
  border-color: var(--bs-form-valid-border-color);
}
.was-validated .form-check-input:valid:checked, .form-check-input.is-valid:checked {
  background-color: var(--bs-form-valid-color);
}
.was-validated .form-check-input:valid:focus, .form-check-input.is-valid:focus {
  box-shadow: 0 0 0 0.25rem rgba(var(--bs-success-rgb), 0.25);
}
.was-validated .form-check-input:valid ~ .form-check-label, .form-check-input.is-valid ~ .form-check-label {
  color: var(--bs-form-valid-color);
}

.form-check-inline .form-check-input ~ .valid-feedback {
  margin-left: 0.5em;
}

.was-validated .input-group > .form-control:not(:focus):valid, .input-group > .form-control:not(:focus).is-valid,
.was-validated .input-group > .form-select:not(:focus):valid,
.input-group > .form-select:not(:focus).is-valid,
.was-validated .input-group > .form-floating:not(:focus-within):valid,
.input-group > .form-floating:not(:focus-within).is-valid {
  z-index: 3;
}

.invalid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: var(--bs-form-invalid-color);
}

.invalid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.15625rem 0.3125rem;
  margin-top: 0.1rem;
  font-size: 0.75rem;
  color: #fff;
  background-color: var(--bs-danger);
  border-radius: var(--bs-border-radius);
}

.was-validated :invalid ~ .invalid-feedback,
.was-validated :invalid ~ .invalid-tooltip,
.is-invalid ~ .invalid-feedback,
.is-invalid ~ .invalid-tooltip {
  display: block;
}

.was-validated .form-control:invalid, .form-control.is-invalid {
  border-color: var(--bs-form-invalid-border-color);
  padding-right: 1.625em;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23EC472E'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23EC472E' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.40625em center;
  background-size: 0.8125em 0.8125em;
}
.was-validated .form-control:invalid:focus, .form-control.is-invalid:focus {
  border-color: var(--bs-form-invalid-border-color);
  box-shadow: 0 0 0 0.25rem rgba(var(--bs-danger-rgb), 0.25);
}

.was-validated textarea.form-control:invalid, textarea.form-control.is-invalid {
  padding-right: 1.625em;
  background-position: top 0.40625em right 0.40625em;
}

.was-validated .form-select:invalid, .form-select.is-invalid {
  border-color: var(--bs-form-invalid-border-color);
}
.was-validated .form-select:invalid:not([multiple]):not([size]), .was-validated .form-select:invalid:not([multiple])[size="1"], .form-select.is-invalid:not([multiple]):not([size]), .form-select.is-invalid:not([multiple])[size="1"] {
  --bs-form-select-bg-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23EC472E'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23EC472E' stroke='none'/%3e%3c/svg%3e");
  padding-right: 1.71875rem;
  background-position: right 0.3125rem center, center right 0.9375rem;
  background-size: 16px 12px, 0.8125em 0.8125em;
}
.was-validated .form-select:invalid:focus, .form-select.is-invalid:focus {
  border-color: var(--bs-form-invalid-border-color);
  box-shadow: 0 0 0 0.25rem rgba(var(--bs-danger-rgb), 0.25);
}

.was-validated .form-control-color:invalid, .form-control-color.is-invalid {
  width: calc(3rem + 1.625em);
}

.was-validated .form-check-input:invalid, .form-check-input.is-invalid {
  border-color: var(--bs-form-invalid-border-color);
}
.was-validated .form-check-input:invalid:checked, .form-check-input.is-invalid:checked {
  background-color: var(--bs-form-invalid-color);
}
.was-validated .form-check-input:invalid:focus, .form-check-input.is-invalid:focus {
  box-shadow: 0 0 0 0.25rem rgba(var(--bs-danger-rgb), 0.25);
}
.was-validated .form-check-input:invalid ~ .form-check-label, .form-check-input.is-invalid ~ .form-check-label {
  color: var(--bs-form-invalid-color);
}

.form-check-inline .form-check-input ~ .invalid-feedback {
  margin-left: 0.5em;
}

.was-validated .input-group > .form-control:not(:focus):invalid, .input-group > .form-control:not(:focus).is-invalid,
.was-validated .input-group > .form-select:not(:focus):invalid,
.input-group > .form-select:not(:focus).is-invalid,
.was-validated .input-group > .form-floating:not(:focus-within):invalid,
.input-group > .form-floating:not(:focus-within).is-invalid {
  z-index: 4;
}

.modal {
  --bs-modal-zindex: 1055;
  --bs-modal-width: 500px;
  --bs-modal-padding: 0.625rem;
  --bs-modal-margin: 0.5rem;
  --bs-modal-color: var(--bs-body-color);
  --bs-modal-bg: var(--bs-body-bg);
  --bs-modal-border-color: var(--bs-border-color-translucent);
  --bs-modal-border-width: var(--bs-border-width);
  --bs-modal-border-radius: 0;
  --bs-modal-box-shadow: var(--bs-box-shadow-sm);
  --bs-modal-inner-border-radius: 0;
  --bs-modal-header-padding-x: 0.625rem;
  --bs-modal-header-padding-y: 0.625rem;
  --bs-modal-header-padding: 0.625rem 0.625rem;
  --bs-modal-header-border-color: var(--bs-border-color);
  --bs-modal-header-border-width: var(--bs-border-width);
  --bs-modal-title-line-height: 1.625;
  --bs-modal-footer-gap: 0.5rem;
  --bs-modal-footer-bg: ;
  --bs-modal-footer-border-color: var(--bs-border-color);
  --bs-modal-footer-border-width: var(--bs-border-width);
  position: fixed;
  top: 0;
  left: 0;
  z-index: var(--bs-modal-zindex);
  display: none;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  outline: 0;
}

.modal-dialog {
  position: relative;
  width: auto;
  margin: var(--bs-modal-margin);
  pointer-events: none;
}
.modal.fade .modal-dialog {
  transform: translate(0, -50px);
  transition: transform 0.3s ease-out;
}
@media (prefers-reduced-motion: reduce) {
  .modal.fade .modal-dialog {
    transition: none;
  }
}
.modal.show .modal-dialog {
  transform: none;
}
.modal.modal-static .modal-dialog {
  transform: scale(1.02);
}

.modal-dialog-scrollable {
  height: calc(100% - var(--bs-modal-margin) * 2);
}
.modal-dialog-scrollable .modal-content {
  max-height: 100%;
  overflow: hidden;
}
.modal-dialog-scrollable .modal-body {
  overflow-y: auto;
}

.modal-dialog-centered {
  display: flex;
  align-items: center;
  min-height: calc(100% - var(--bs-modal-margin) * 2);
}

.modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  color: var(--bs-modal-color);
  pointer-events: auto;
  background-color: var(--bs-modal-bg);
  background-clip: padding-box;
  border: var(--bs-modal-border-width) solid var(--bs-modal-border-color);
  border-radius: var(--bs-modal-border-radius);
  outline: 0;
}

.modal-backdrop {
  --bs-backdrop-zindex: 1050;
  --bs-backdrop-bg: #28170F;
  --bs-backdrop-opacity: 0.5;
  position: fixed;
  top: 0;
  left: 0;
  z-index: var(--bs-backdrop-zindex);
  width: 100vw;
  height: 100vh;
  background-color: var(--bs-backdrop-bg);
}
.modal-backdrop.fade {
  opacity: 0;
}
.modal-backdrop.show {
  opacity: var(--bs-backdrop-opacity);
}

.modal-header {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  padding: var(--bs-modal-header-padding);
  border-bottom: var(--bs-modal-header-border-width) solid var(--bs-modal-header-border-color);
  border-top-left-radius: var(--bs-modal-inner-border-radius);
  border-top-right-radius: var(--bs-modal-inner-border-radius);
}
.modal-header .btn-close {
  padding: calc(var(--bs-modal-header-padding-y) * .5) calc(var(--bs-modal-header-padding-x) * .5);
  margin-top: calc(-.5 * var(--bs-modal-header-padding-y));
  margin-right: calc(-.5 * var(--bs-modal-header-padding-x));
  margin-bottom: calc(-.5 * var(--bs-modal-header-padding-y));
  margin-left: auto;
}

.modal-title {
  margin-bottom: 0;
  line-height: var(--bs-modal-title-line-height);
}

.modal-body {
  position: relative;
  flex: 1 1 auto;
  padding: var(--bs-modal-padding);
}

.modal-footer {
  display: flex;
  flex-shrink: 0;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-end;
  padding: calc(var(--bs-modal-padding) - var(--bs-modal-footer-gap) * .5);
  background-color: var(--bs-modal-footer-bg);
  border-top: var(--bs-modal-footer-border-width) solid var(--bs-modal-footer-border-color);
  border-bottom-right-radius: var(--bs-modal-inner-border-radius);
  border-bottom-left-radius: var(--bs-modal-inner-border-radius);
}
.modal-footer > * {
  margin: calc(var(--bs-modal-footer-gap) * .5);
}

@media (min-width: 576px) {
  .modal {
    --bs-modal-margin: 1.75rem;
    --bs-modal-box-shadow: var(--bs-box-shadow);
  }

  .modal-dialog {
    max-width: var(--bs-modal-width);
    margin-right: auto;
    margin-left: auto;
  }

  .modal-sm {
    --bs-modal-width: 300px;
  }
}
@media (min-width: 992px) {
  .modal-lg,
.modal-xl {
    --bs-modal-width: 800px;
  }
}
@media (min-width: 1200px) {
  .modal-xl {
    --bs-modal-width: 1140px;
  }
}
.modal-fullscreen {
  width: 100vw;
  max-width: none;
  height: 100%;
  margin: 0;
}
.modal-fullscreen .modal-content {
  height: 100%;
  border: 0;
  border-radius: 0;
}
.modal-fullscreen .modal-header,
.modal-fullscreen .modal-footer {
  border-radius: 0;
}
.modal-fullscreen .modal-body {
  overflow-y: auto;
}

@media (max-width: 575.98px) {
  .modal-fullscreen-sm-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-sm-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-sm-down .modal-header,
.modal-fullscreen-sm-down .modal-footer {
    border-radius: 0;
  }
  .modal-fullscreen-sm-down .modal-body {
    overflow-y: auto;
  }
}
@media (max-width: 767.98px) {
  .modal-fullscreen-md-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-md-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-md-down .modal-header,
.modal-fullscreen-md-down .modal-footer {
    border-radius: 0;
  }
  .modal-fullscreen-md-down .modal-body {
    overflow-y: auto;
  }
}
@media (max-width: 991.98px) {
  .modal-fullscreen-lg-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-lg-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-lg-down .modal-header,
.modal-fullscreen-lg-down .modal-footer {
    border-radius: 0;
  }
  .modal-fullscreen-lg-down .modal-body {
    overflow-y: auto;
  }
}
@media (max-width: 1199.98px) {
  .modal-fullscreen-xl-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-xl-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-xl-down .modal-header,
.modal-fullscreen-xl-down .modal-footer {
    border-radius: 0;
  }
  .modal-fullscreen-xl-down .modal-body {
    overflow-y: auto;
  }
}
@media (max-width: 1399.98px) {
  .modal-fullscreen-xxl-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-xxl-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-xxl-down .modal-header,
.modal-fullscreen-xxl-down .modal-footer {
    border-radius: 0;
  }
  .modal-fullscreen-xxl-down .modal-body {
    overflow-y: auto;
  }
}
.accordion {
  --bs-accordion-color: var(--bs-body-color);
  --bs-accordion-bg: none;
  --bs-accordion-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, border-radius 0.15s ease;
  --bs-accordion-border-color: var(--bs-border-color);
  --bs-accordion-border-width: 0;
  --bs-accordion-border-radius: var(--bs-border-radius);
  --bs-accordion-inner-border-radius: calc(var(--bs-border-radius) - 0);
  --bs-accordion-btn-padding-x: 0;
  --bs-accordion-btn-padding-y: 1rem;
  --bs-accordion-btn-color: var(--bs-body-color);
  --bs-accordion-btn-bg: var(--bs-accordion-bg);
  --bs-accordion-btn-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='none' stroke='%23415143' stroke-linecap='round' stroke-linejoin='round'%3e%3cpath d='m2 5 6 6 6-6'/%3e%3c/svg%3e");
  --bs-accordion-btn-icon-width: 1.25rem;
  --bs-accordion-btn-icon-transform: rotate(-180deg);
  --bs-accordion-btn-icon-transition: transform 0.2s ease-in-out;
  --bs-accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='none' stroke='%231a201b' stroke-linecap='round' stroke-linejoin='round'%3e%3cpath d='m2 5 6 6 6-6'/%3e%3c/svg%3e");
  --bs-accordion-btn-focus-box-shadow: 0 0 0 0.25rem rgba(65, 81, 67, 0.25);
  --bs-accordion-body-padding-x: 0;
  --bs-accordion-body-padding-y: 1.25rem;
  --bs-accordion-active-color: var(--bs-primary-text-emphasis);
  --bs-accordion-active-bg: var(--bs-primary-bg-subtle);
}

.accordion-button {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  padding: var(--bs-accordion-btn-padding-y) var(--bs-accordion-btn-padding-x);
  font-size: 1rem;
  color: var(--bs-accordion-btn-color);
  text-align: left;
  background-color: var(--bs-accordion-btn-bg);
  border: 0;
  border-radius: 0;
  overflow-anchor: none;
  transition: var(--bs-accordion-transition);
}
@media (prefers-reduced-motion: reduce) {
  .accordion-button {
    transition: none;
  }
}
.accordion-button:not(.collapsed) {
  color: var(--bs-accordion-active-color);
  background-color: var(--bs-accordion-active-bg);
  box-shadow: inset 0 calc(-1 * var(--bs-accordion-border-width)) 0 var(--bs-accordion-border-color);
}
.accordion-button:not(.collapsed)::after {
  background-image: var(--bs-accordion-btn-active-icon);
  transform: var(--bs-accordion-btn-icon-transform);
}
.accordion-button::after {
  flex-shrink: 0;
  width: var(--bs-accordion-btn-icon-width);
  height: var(--bs-accordion-btn-icon-width);
  margin-left: auto;
  content: "";
  background-image: var(--bs-accordion-btn-icon);
  background-repeat: no-repeat;
  background-size: var(--bs-accordion-btn-icon-width);
  transition: var(--bs-accordion-btn-icon-transition);
}
@media (prefers-reduced-motion: reduce) {
  .accordion-button::after {
    transition: none;
  }
}
.accordion-button:hover {
  z-index: 2;
}
.accordion-button:focus {
  z-index: 3;
  outline: 0;
  box-shadow: var(--bs-accordion-btn-focus-box-shadow);
}

.accordion-header {
  margin-bottom: 0;
}

.accordion-item {
  color: var(--bs-accordion-color);
  background-color: var(--bs-accordion-bg);
  border: var(--bs-accordion-border-width) solid var(--bs-accordion-border-color);
}
.accordion-item:first-of-type {
  border-top-left-radius: var(--bs-accordion-border-radius);
  border-top-right-radius: var(--bs-accordion-border-radius);
}
.accordion-item:first-of-type > .accordion-header .accordion-button {
  border-top-left-radius: var(--bs-accordion-inner-border-radius);
  border-top-right-radius: var(--bs-accordion-inner-border-radius);
}
.accordion-item:not(:first-of-type) {
  border-top: 0;
}
.accordion-item:last-of-type {
  border-bottom-right-radius: var(--bs-accordion-border-radius);
  border-bottom-left-radius: var(--bs-accordion-border-radius);
}
.accordion-item:last-of-type > .accordion-header .accordion-button.collapsed {
  border-bottom-right-radius: var(--bs-accordion-inner-border-radius);
  border-bottom-left-radius: var(--bs-accordion-inner-border-radius);
}
.accordion-item:last-of-type > .accordion-collapse {
  border-bottom-right-radius: var(--bs-accordion-border-radius);
  border-bottom-left-radius: var(--bs-accordion-border-radius);
}

.accordion-body {
  padding: var(--bs-accordion-body-padding-y) var(--bs-accordion-body-padding-x);
}

.accordion-flush > .accordion-item {
  border-right: 0;
  border-left: 0;
  border-radius: 0;
}
.accordion-flush > .accordion-item:first-child {
  border-top: 0;
}
.accordion-flush > .accordion-item:last-child {
  border-bottom: 0;
}
.accordion-flush > .accordion-item > .accordion-collapse,
.accordion-flush > .accordion-item > .accordion-header .accordion-button,
.accordion-flush > .accordion-item > .accordion-header .accordion-button.collapsed {
  border-radius: 0;
}

[data-bs-theme=dark] .accordion-button::after {
  --bs-accordion-btn-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%238d978e'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708'/%3e%3c/svg%3e");
  --bs-accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%238d978e'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708'/%3e%3c/svg%3e");
}

.fade {
  transition: opacity 0.35s linear;
}
@media (prefers-reduced-motion: reduce) {
  .fade {
    transition: none;
  }
}
.fade:not(.show) {
  opacity: 0;
}

.collapse:not(.show) {
  display: none;
}

.collapsing {
  height: 0;
  overflow: hidden;
  transition: height 0.35s ease;
}
@media (prefers-reduced-motion: reduce) {
  .collapsing {
    transition: none;
  }
}
.collapsing.collapse-horizontal {
  width: 0;
  height: auto;
  transition: width 0.35s ease;
}
@media (prefers-reduced-motion: reduce) {
  .collapsing.collapse-horizontal {
    transition: none;
  }
}

.offcanvas, .offcanvas-xxl, .offcanvas-xl, .offcanvas-lg, .offcanvas-md, .offcanvas-sm {
  --bs-offcanvas-zindex: 1050;
  --bs-offcanvas-width: 400px;
  --bs-offcanvas-height: 30vh;
  --bs-offcanvas-padding-x: 10px;
  --bs-offcanvas-padding-y: 1rem;
  --bs-offcanvas-color: var(--bs-body-color);
  --bs-offcanvas-bg: #415143;
  --bs-offcanvas-border-width: 0;
  --bs-offcanvas-border-color: var(--bs-border-color-translucent);
  --bs-offcanvas-box-shadow: var(--bs-box-shadow-sm);
  --bs-offcanvas-transition: transform 0.3s ease-in-out;
  --bs-offcanvas-title-line-height: 1.625;
}

@media (max-width: 575.98px) {
  .offcanvas-sm {
    position: fixed;
    bottom: 0;
    z-index: var(--bs-offcanvas-zindex);
    display: flex;
    flex-direction: column;
    max-width: 100%;
    color: var(--bs-offcanvas-color);
    visibility: hidden;
    background-color: var(--bs-offcanvas-bg);
    background-clip: padding-box;
    outline: 0;
    transition: var(--bs-offcanvas-transition);
  }
}
@media (max-width: 575.98px) and (prefers-reduced-motion: reduce) {
  .offcanvas-sm {
    transition: none;
  }
}
@media (max-width: 575.98px) {
  .offcanvas-sm.offcanvas-start {
    top: 0;
    left: 0;
    width: var(--bs-offcanvas-width);
    border-right: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(-100%);
  }
}
@media (max-width: 575.98px) {
  .offcanvas-sm.offcanvas-end {
    top: 0;
    right: 0;
    width: var(--bs-offcanvas-width);
    border-left: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(100%);
  }
}
@media (max-width: 575.98px) {
  .offcanvas-sm.offcanvas-top {
    top: 0;
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-bottom: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(-100%);
  }
}
@media (max-width: 575.98px) {
  .offcanvas-sm.offcanvas-bottom {
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-top: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(100%);
  }
}
@media (max-width: 575.98px) {
  .offcanvas-sm.showing, .offcanvas-sm.show:not(.hiding) {
    transform: none;
  }
}
@media (max-width: 575.98px) {
  .offcanvas-sm.showing, .offcanvas-sm.hiding, .offcanvas-sm.show {
    visibility: visible;
  }
}
@media (min-width: 576px) {
  .offcanvas-sm {
    --bs-offcanvas-height: auto;
    --bs-offcanvas-border-width: 0;
    background-color: transparent !important;
  }
  .offcanvas-sm .offcanvas-header {
    display: none;
  }
  .offcanvas-sm .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
    background-color: transparent !important;
  }
}

@media (max-width: 767.98px) {
  .offcanvas-md {
    position: fixed;
    bottom: 0;
    z-index: var(--bs-offcanvas-zindex);
    display: flex;
    flex-direction: column;
    max-width: 100%;
    color: var(--bs-offcanvas-color);
    visibility: hidden;
    background-color: var(--bs-offcanvas-bg);
    background-clip: padding-box;
    outline: 0;
    transition: var(--bs-offcanvas-transition);
  }
}
@media (max-width: 767.98px) and (prefers-reduced-motion: reduce) {
  .offcanvas-md {
    transition: none;
  }
}
@media (max-width: 767.98px) {
  .offcanvas-md.offcanvas-start {
    top: 0;
    left: 0;
    width: var(--bs-offcanvas-width);
    border-right: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(-100%);
  }
}
@media (max-width: 767.98px) {
  .offcanvas-md.offcanvas-end {
    top: 0;
    right: 0;
    width: var(--bs-offcanvas-width);
    border-left: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(100%);
  }
}
@media (max-width: 767.98px) {
  .offcanvas-md.offcanvas-top {
    top: 0;
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-bottom: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(-100%);
  }
}
@media (max-width: 767.98px) {
  .offcanvas-md.offcanvas-bottom {
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-top: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(100%);
  }
}
@media (max-width: 767.98px) {
  .offcanvas-md.showing, .offcanvas-md.show:not(.hiding) {
    transform: none;
  }
}
@media (max-width: 767.98px) {
  .offcanvas-md.showing, .offcanvas-md.hiding, .offcanvas-md.show {
    visibility: visible;
  }
}
@media (min-width: 768px) {
  .offcanvas-md {
    --bs-offcanvas-height: auto;
    --bs-offcanvas-border-width: 0;
    background-color: transparent !important;
  }
  .offcanvas-md .offcanvas-header {
    display: none;
  }
  .offcanvas-md .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
    background-color: transparent !important;
  }
}

@media (max-width: 991.98px) {
  .offcanvas-lg {
    position: fixed;
    bottom: 0;
    z-index: var(--bs-offcanvas-zindex);
    display: flex;
    flex-direction: column;
    max-width: 100%;
    color: var(--bs-offcanvas-color);
    visibility: hidden;
    background-color: var(--bs-offcanvas-bg);
    background-clip: padding-box;
    outline: 0;
    transition: var(--bs-offcanvas-transition);
  }
}
@media (max-width: 991.98px) and (prefers-reduced-motion: reduce) {
  .offcanvas-lg {
    transition: none;
  }
}
@media (max-width: 991.98px) {
  .offcanvas-lg.offcanvas-start {
    top: 0;
    left: 0;
    width: var(--bs-offcanvas-width);
    border-right: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(-100%);
  }
}
@media (max-width: 991.98px) {
  .offcanvas-lg.offcanvas-end {
    top: 0;
    right: 0;
    width: var(--bs-offcanvas-width);
    border-left: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(100%);
  }
}
@media (max-width: 991.98px) {
  .offcanvas-lg.offcanvas-top {
    top: 0;
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-bottom: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(-100%);
  }
}
@media (max-width: 991.98px) {
  .offcanvas-lg.offcanvas-bottom {
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-top: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(100%);
  }
}
@media (max-width: 991.98px) {
  .offcanvas-lg.showing, .offcanvas-lg.show:not(.hiding) {
    transform: none;
  }
}
@media (max-width: 991.98px) {
  .offcanvas-lg.showing, .offcanvas-lg.hiding, .offcanvas-lg.show {
    visibility: visible;
  }
}
@media (min-width: 992px) {
  .offcanvas-lg {
    --bs-offcanvas-height: auto;
    --bs-offcanvas-border-width: 0;
    background-color: transparent !important;
  }
  .offcanvas-lg .offcanvas-header {
    display: none;
  }
  .offcanvas-lg .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
    background-color: transparent !important;
  }
}

@media (max-width: 1199.98px) {
  .offcanvas-xl {
    position: fixed;
    bottom: 0;
    z-index: var(--bs-offcanvas-zindex);
    display: flex;
    flex-direction: column;
    max-width: 100%;
    color: var(--bs-offcanvas-color);
    visibility: hidden;
    background-color: var(--bs-offcanvas-bg);
    background-clip: padding-box;
    outline: 0;
    transition: var(--bs-offcanvas-transition);
  }
}
@media (max-width: 1199.98px) and (prefers-reduced-motion: reduce) {
  .offcanvas-xl {
    transition: none;
  }
}
@media (max-width: 1199.98px) {
  .offcanvas-xl.offcanvas-start {
    top: 0;
    left: 0;
    width: var(--bs-offcanvas-width);
    border-right: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(-100%);
  }
}
@media (max-width: 1199.98px) {
  .offcanvas-xl.offcanvas-end {
    top: 0;
    right: 0;
    width: var(--bs-offcanvas-width);
    border-left: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(100%);
  }
}
@media (max-width: 1199.98px) {
  .offcanvas-xl.offcanvas-top {
    top: 0;
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-bottom: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(-100%);
  }
}
@media (max-width: 1199.98px) {
  .offcanvas-xl.offcanvas-bottom {
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-top: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(100%);
  }
}
@media (max-width: 1199.98px) {
  .offcanvas-xl.showing, .offcanvas-xl.show:not(.hiding) {
    transform: none;
  }
}
@media (max-width: 1199.98px) {
  .offcanvas-xl.showing, .offcanvas-xl.hiding, .offcanvas-xl.show {
    visibility: visible;
  }
}
@media (min-width: 1200px) {
  .offcanvas-xl {
    --bs-offcanvas-height: auto;
    --bs-offcanvas-border-width: 0;
    background-color: transparent !important;
  }
  .offcanvas-xl .offcanvas-header {
    display: none;
  }
  .offcanvas-xl .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
    background-color: transparent !important;
  }
}

@media (max-width: 1399.98px) {
  .offcanvas-xxl {
    position: fixed;
    bottom: 0;
    z-index: var(--bs-offcanvas-zindex);
    display: flex;
    flex-direction: column;
    max-width: 100%;
    color: var(--bs-offcanvas-color);
    visibility: hidden;
    background-color: var(--bs-offcanvas-bg);
    background-clip: padding-box;
    outline: 0;
    transition: var(--bs-offcanvas-transition);
  }
}
@media (max-width: 1399.98px) and (prefers-reduced-motion: reduce) {
  .offcanvas-xxl {
    transition: none;
  }
}
@media (max-width: 1399.98px) {
  .offcanvas-xxl.offcanvas-start {
    top: 0;
    left: 0;
    width: var(--bs-offcanvas-width);
    border-right: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(-100%);
  }
}
@media (max-width: 1399.98px) {
  .offcanvas-xxl.offcanvas-end {
    top: 0;
    right: 0;
    width: var(--bs-offcanvas-width);
    border-left: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(100%);
  }
}
@media (max-width: 1399.98px) {
  .offcanvas-xxl.offcanvas-top {
    top: 0;
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-bottom: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(-100%);
  }
}
@media (max-width: 1399.98px) {
  .offcanvas-xxl.offcanvas-bottom {
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-top: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(100%);
  }
}
@media (max-width: 1399.98px) {
  .offcanvas-xxl.showing, .offcanvas-xxl.show:not(.hiding) {
    transform: none;
  }
}
@media (max-width: 1399.98px) {
  .offcanvas-xxl.showing, .offcanvas-xxl.hiding, .offcanvas-xxl.show {
    visibility: visible;
  }
}
@media (min-width: 1400px) {
  .offcanvas-xxl {
    --bs-offcanvas-height: auto;
    --bs-offcanvas-border-width: 0;
    background-color: transparent !important;
  }
  .offcanvas-xxl .offcanvas-header {
    display: none;
  }
  .offcanvas-xxl .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
    background-color: transparent !important;
  }
}

.offcanvas {
  position: fixed;
  bottom: 0;
  z-index: var(--bs-offcanvas-zindex);
  display: flex;
  flex-direction: column;
  max-width: 100%;
  color: var(--bs-offcanvas-color);
  visibility: hidden;
  background-color: var(--bs-offcanvas-bg);
  background-clip: padding-box;
  outline: 0;
  transition: var(--bs-offcanvas-transition);
}
@media (prefers-reduced-motion: reduce) {
  .offcanvas {
    transition: none;
  }
}
.offcanvas.offcanvas-start {
  top: 0;
  left: 0;
  width: var(--bs-offcanvas-width);
  border-right: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
  transform: translateX(-100%);
}
.offcanvas.offcanvas-end {
  top: 0;
  right: 0;
  width: var(--bs-offcanvas-width);
  border-left: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
  transform: translateX(100%);
}
.offcanvas.offcanvas-top {
  top: 0;
  right: 0;
  left: 0;
  height: var(--bs-offcanvas-height);
  max-height: 100%;
  border-bottom: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
  transform: translateY(-100%);
}
.offcanvas.offcanvas-bottom {
  right: 0;
  left: 0;
  height: var(--bs-offcanvas-height);
  max-height: 100%;
  border-top: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
  transform: translateY(100%);
}
.offcanvas.showing, .offcanvas.show:not(.hiding) {
  transform: none;
}
.offcanvas.showing, .offcanvas.hiding, .offcanvas.show {
  visibility: visible;
}

.offcanvas-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1049;
  width: 100vw;
  height: 100vh;
  background-color: #28170F;
}
.offcanvas-backdrop.fade {
  opacity: 0;
}
.offcanvas-backdrop.show {
  opacity: 0.5;
}

.offcanvas-header {
  display: flex;
  align-items: center;
  padding: var(--bs-offcanvas-padding-y) var(--bs-offcanvas-padding-x);
}
.offcanvas-header .btn-close {
  padding: calc(var(--bs-offcanvas-padding-y) * .5) calc(var(--bs-offcanvas-padding-x) * .5);
  margin-top: calc(-.5 * var(--bs-offcanvas-padding-y));
  margin-right: calc(-.5 * var(--bs-offcanvas-padding-x));
  margin-bottom: calc(-.5 * var(--bs-offcanvas-padding-y));
  margin-left: auto;
}

.offcanvas-title {
  margin-bottom: 0;
  line-height: var(--bs-offcanvas-title-line-height);
}

.offcanvas-body {
  flex-grow: 1;
  padding: var(--bs-offcanvas-padding-y) var(--bs-offcanvas-padding-x);
  overflow-y: auto;
}

.btn-close {
  --bs-btn-close-color: #28170F;
  --bs-btn-close-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%2328170F'%3e%3cpath d='M.293.293a1 1 0 0 1 1.414 0L8 6.586 14.293.293a1 1 0 1 1 1.414 1.414L9.414 8l6.293 6.293a1 1 0 0 1-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 0 1-1.414-1.414L6.586 8 .293 1.707a1 1 0 0 1 0-1.414'/%3e%3c/svg%3e");
  --bs-btn-close-opacity: 0.5;
  --bs-btn-close-hover-opacity: 0.75;
  --bs-btn-close-focus-shadow: 0 0 0 0.25rem rgba(65, 81, 67, 0.25);
  --bs-btn-close-focus-opacity: 1;
  --bs-btn-close-disabled-opacity: 0.25;
  box-sizing: content-box;
  width: 1em;
  height: 1em;
  padding: 0.25em 0.25em;
  color: var(--bs-btn-close-color);
  background: transparent var(--bs-btn-close-bg) center/1em auto no-repeat;
  filter: var(--bs-btn-close-filter);
  border: 0;
  border-radius: 0.375rem;
  opacity: var(--bs-btn-close-opacity);
}
.btn-close:hover {
  color: var(--bs-btn-close-color);
  text-decoration: none;
  opacity: var(--bs-btn-close-hover-opacity);
}
.btn-close:focus {
  outline: 0;
  box-shadow: var(--bs-btn-close-focus-shadow);
  opacity: var(--bs-btn-close-focus-opacity);
}
.btn-close:disabled, .btn-close.disabled {
  pointer-events: none;
  user-select: none;
  opacity: var(--bs-btn-close-disabled-opacity);
}

.btn-close-white {
  --bs-btn-close-filter: invert(1) grayscale(100%) brightness(200%);
}

:root,
[data-bs-theme=light] {
  --bs-btn-close-filter: ;
}

[data-bs-theme=dark] {
  --bs-btn-close-filter: invert(1) grayscale(100%) brightness(200%);
}

.alert {
  --bs-alert-bg: transparent;
  --bs-alert-padding-x: 0.625rem;
  --bs-alert-padding-y: 0.625rem;
  --bs-alert-margin-bottom: 1rem;
  --bs-alert-color: inherit;
  --bs-alert-border-color: transparent;
  --bs-alert-border: var(--bs-border-width) solid var(--bs-alert-border-color);
  --bs-alert-border-radius: var(--bs-border-radius);
  --bs-alert-link-color: inherit;
  position: relative;
  padding: var(--bs-alert-padding-y) var(--bs-alert-padding-x);
  margin-bottom: var(--bs-alert-margin-bottom);
  color: var(--bs-alert-color);
  background-color: var(--bs-alert-bg);
  border: var(--bs-alert-border);
  border-radius: var(--bs-alert-border-radius);
}

.alert-heading {
  color: inherit;
}

.alert-link {
  font-weight: 700;
  color: var(--bs-alert-link-color);
}

.alert-dismissible {
  padding-right: 1.875rem;
}
.alert-dismissible .btn-close {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 2;
  padding: 0.78125rem 0.625rem;
}

.alert-primary {
  --bs-alert-color: var(--bs-primary-text-emphasis);
  --bs-alert-bg: var(--bs-primary-bg-subtle);
  --bs-alert-border-color: var(--bs-primary-border-subtle);
  --bs-alert-link-color: var(--bs-primary-text-emphasis);
}

.alert-secondary {
  --bs-alert-color: var(--bs-secondary-text-emphasis);
  --bs-alert-bg: var(--bs-secondary-bg-subtle);
  --bs-alert-border-color: var(--bs-secondary-border-subtle);
  --bs-alert-link-color: var(--bs-secondary-text-emphasis);
}

.alert-success {
  --bs-alert-color: var(--bs-success-text-emphasis);
  --bs-alert-bg: var(--bs-success-bg-subtle);
  --bs-alert-border-color: var(--bs-success-border-subtle);
  --bs-alert-link-color: var(--bs-success-text-emphasis);
}

.alert-info {
  --bs-alert-color: var(--bs-info-text-emphasis);
  --bs-alert-bg: var(--bs-info-bg-subtle);
  --bs-alert-border-color: var(--bs-info-border-subtle);
  --bs-alert-link-color: var(--bs-info-text-emphasis);
}

.alert-warning {
  --bs-alert-color: var(--bs-warning-text-emphasis);
  --bs-alert-bg: var(--bs-warning-bg-subtle);
  --bs-alert-border-color: var(--bs-warning-border-subtle);
  --bs-alert-link-color: var(--bs-warning-text-emphasis);
}

.alert-danger {
  --bs-alert-color: var(--bs-danger-text-emphasis);
  --bs-alert-bg: var(--bs-danger-bg-subtle);
  --bs-alert-border-color: var(--bs-danger-border-subtle);
  --bs-alert-link-color: var(--bs-danger-text-emphasis);
}

.alert-dark {
  --bs-alert-color: var(--bs-dark-text-emphasis);
  --bs-alert-bg: var(--bs-dark-bg-subtle);
  --bs-alert-border-color: var(--bs-dark-border-subtle);
  --bs-alert-link-color: var(--bs-dark-text-emphasis);
}

.alert-green {
  --bs-alert-color: var(--bs-green-text-emphasis);
  --bs-alert-bg: var(--bs-green-bg-subtle);
  --bs-alert-border-color: var(--bs-green-border-subtle);
  --bs-alert-link-color: var(--bs-green-text-emphasis);
}

.alert-celadon {
  --bs-alert-color: var(--bs-celadon-text-emphasis);
  --bs-alert-bg: var(--bs-celadon-bg-subtle);
  --bs-alert-border-color: var(--bs-celadon-border-subtle);
  --bs-alert-link-color: var(--bs-celadon-text-emphasis);
}

.alert-red {
  --bs-alert-color: var(--bs-red-text-emphasis);
  --bs-alert-bg: var(--bs-red-bg-subtle);
  --bs-alert-border-color: var(--bs-red-border-subtle);
  --bs-alert-link-color: var(--bs-red-text-emphasis);
}

.alert-warm {
  --bs-alert-color: var(--bs-warm-text-emphasis);
  --bs-alert-bg: var(--bs-warm-bg-subtle);
  --bs-alert-border-color: var(--bs-warm-border-subtle);
  --bs-alert-link-color: var(--bs-warm-text-emphasis);
}

.alert-summer {
  --bs-alert-color: var(--bs-summer-text-emphasis);
  --bs-alert-bg: var(--bs-summer-bg-subtle);
  --bs-alert-border-color: var(--bs-summer-border-subtle);
  --bs-alert-link-color: var(--bs-summer-text-emphasis);
}

.alert-autumn {
  --bs-alert-color: var(--bs-autumn-text-emphasis);
  --bs-alert-bg: var(--bs-autumn-bg-subtle);
  --bs-alert-border-color: var(--bs-autumn-border-subtle);
  --bs-alert-link-color: var(--bs-autumn-text-emphasis);
}

.alert-winter {
  --bs-alert-color: var(--bs-winter-text-emphasis);
  --bs-alert-bg: var(--bs-winter-bg-subtle);
  --bs-alert-border-color: var(--bs-winter-border-subtle);
  --bs-alert-link-color: var(--bs-winter-text-emphasis);
}

.alert-spring {
  --bs-alert-color: var(--bs-spring-text-emphasis);
  --bs-alert-bg: var(--bs-spring-bg-subtle);
  --bs-alert-border-color: var(--bs-spring-border-subtle);
  --bs-alert-link-color: var(--bs-spring-text-emphasis);
}

.align-baseline {
  vertical-align: baseline !important;
}

.align-top {
  vertical-align: top !important;
}

.align-middle {
  vertical-align: middle !important;
}

.align-bottom {
  vertical-align: bottom !important;
}

.align-text-bottom {
  vertical-align: text-bottom !important;
}

.align-text-top {
  vertical-align: text-top !important;
}

.float-start {
  float: left !important;
}

.float-end {
  float: right !important;
}

.float-none {
  float: none !important;
}

.object-fit-contain {
  object-fit: contain !important;
}

.object-fit-cover {
  object-fit: cover !important;
}

.object-fit-fill {
  object-fit: fill !important;
}

.object-fit-scale {
  object-fit: scale-down !important;
}

.object-fit-none {
  object-fit: none !important;
}

.opacity-0 {
  opacity: 0 !important;
}

.opacity-25 {
  opacity: 0.25 !important;
}

.opacity-50 {
  opacity: 0.5 !important;
}

.opacity-75 {
  opacity: 0.75 !important;
}

.opacity-100 {
  opacity: 1 !important;
}

.overflow-auto {
  overflow: auto !important;
}

.overflow-hidden {
  overflow: hidden !important;
}

.overflow-visible {
  overflow: visible !important;
}

.overflow-scroll {
  overflow: scroll !important;
}

.overflow-x-auto {
  overflow-x: auto !important;
}

.overflow-x-hidden {
  overflow-x: hidden !important;
}

.overflow-x-visible {
  overflow-x: visible !important;
}

.overflow-x-scroll {
  overflow-x: scroll !important;
}

.overflow-y-auto {
  overflow-y: auto !important;
}

.overflow-y-hidden {
  overflow-y: hidden !important;
}

.overflow-y-visible {
  overflow-y: visible !important;
}

.overflow-y-scroll {
  overflow-y: scroll !important;
}

.d-inline {
  display: inline !important;
}

.d-inline-block {
  display: inline-block !important;
}

.d-block {
  display: block !important;
}

.d-grid {
  display: grid !important;
}

.d-inline-grid {
  display: inline-grid !important;
}

.d-table {
  display: table !important;
}

.d-table-row {
  display: table-row !important;
}

.d-table-cell {
  display: table-cell !important;
}

.d-flex {
  display: flex !important;
}

.d-inline-flex {
  display: inline-flex !important;
}

.d-none {
  display: none !important;
}

.shadow {
  box-shadow: var(--bs-box-shadow) !important;
}

.shadow-sm {
  box-shadow: var(--bs-box-shadow-sm) !important;
}

.shadow-lg {
  box-shadow: var(--bs-box-shadow-lg) !important;
}

.shadow-none {
  box-shadow: none !important;
}

.focus-ring-primary {
  --bs-focus-ring-color: rgba(var(--bs-primary-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-secondary {
  --bs-focus-ring-color: rgba(var(--bs-secondary-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-success {
  --bs-focus-ring-color: rgba(var(--bs-success-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-info {
  --bs-focus-ring-color: rgba(var(--bs-info-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-warning {
  --bs-focus-ring-color: rgba(var(--bs-warning-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-danger {
  --bs-focus-ring-color: rgba(var(--bs-danger-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-dark {
  --bs-focus-ring-color: rgba(var(--bs-dark-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-green {
  --bs-focus-ring-color: rgba(var(--bs-green-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-celadon {
  --bs-focus-ring-color: rgba(var(--bs-celadon-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-red {
  --bs-focus-ring-color: rgba(var(--bs-red-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-warm {
  --bs-focus-ring-color: rgba(var(--bs-warm-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-summer {
  --bs-focus-ring-color: rgba(var(--bs-summer-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-autumn {
  --bs-focus-ring-color: rgba(var(--bs-autumn-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-winter {
  --bs-focus-ring-color: rgba(var(--bs-winter-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-spring {
  --bs-focus-ring-color: rgba(var(--bs-spring-rgb), var(--bs-focus-ring-opacity));
}

.position-static {
  position: static !important;
}

.position-relative {
  position: relative !important;
}

.position-absolute {
  position: absolute !important;
}

.position-fixed {
  position: fixed !important;
}

.position-sticky {
  position: sticky !important;
}

.top-0 {
  top: 0 !important;
}

.top-50 {
  top: 50% !important;
}

.top-100 {
  top: 100% !important;
}

.bottom-0 {
  bottom: 0 !important;
}

.bottom-50 {
  bottom: 50% !important;
}

.bottom-100 {
  bottom: 100% !important;
}

.start-0 {
  left: 0 !important;
}

.start-50 {
  left: 50% !important;
}

.start-100 {
  left: 100% !important;
}

.end-0 {
  right: 0 !important;
}

.end-50 {
  right: 50% !important;
}

.end-100 {
  right: 100% !important;
}

.translate-middle {
  transform: translate(-50%, -50%) !important;
}

.translate-middle-x {
  transform: translateX(-50%) !important;
}

.translate-middle-y {
  transform: translateY(-50%) !important;
}

.border {
  border: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
}

.border-0 {
  border: 0 !important;
}

.border-top {
  border-top: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
}

.border-top-0 {
  border-top: 0 !important;
}

.border-end {
  border-right: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
}

.border-end-0 {
  border-right: 0 !important;
}

.border-bottom {
  border-bottom: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
}

.border-bottom-0 {
  border-bottom: 0 !important;
}

.border-start {
  border-left: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
}

.border-start-0 {
  border-left: 0 !important;
}

.border-primary {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-primary-rgb), var(--bs-border-opacity)) !important;
}

.border-secondary {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-secondary-rgb), var(--bs-border-opacity)) !important;
}

.border-success {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-success-rgb), var(--bs-border-opacity)) !important;
}

.border-info {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-info-rgb), var(--bs-border-opacity)) !important;
}

.border-warning {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-warning-rgb), var(--bs-border-opacity)) !important;
}

.border-danger {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-danger-rgb), var(--bs-border-opacity)) !important;
}

.border-dark {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-dark-rgb), var(--bs-border-opacity)) !important;
}

.border-green {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-green-rgb), var(--bs-border-opacity)) !important;
}

.border-celadon {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-celadon-rgb), var(--bs-border-opacity)) !important;
}

.border-red {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-red-rgb), var(--bs-border-opacity)) !important;
}

.border-warm {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-warm-rgb), var(--bs-border-opacity)) !important;
}

.border-summer {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-summer-rgb), var(--bs-border-opacity)) !important;
}

.border-autumn {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-autumn-rgb), var(--bs-border-opacity)) !important;
}

.border-winter {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-winter-rgb), var(--bs-border-opacity)) !important;
}

.border-spring {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-spring-rgb), var(--bs-border-opacity)) !important;
}

.border-black {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-black-rgb), var(--bs-border-opacity)) !important;
}

.border-white {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-white-rgb), var(--bs-border-opacity)) !important;
}

.border-primary-subtle {
  border-color: var(--bs-primary-border-subtle) !important;
}

.border-secondary-subtle {
  border-color: var(--bs-secondary-border-subtle) !important;
}

.border-success-subtle {
  border-color: var(--bs-success-border-subtle) !important;
}

.border-info-subtle {
  border-color: var(--bs-info-border-subtle) !important;
}

.border-warning-subtle {
  border-color: var(--bs-warning-border-subtle) !important;
}

.border-danger-subtle {
  border-color: var(--bs-danger-border-subtle) !important;
}

.border-light-subtle {
  border-color: var(--bs-light-border-subtle) !important;
}

.border-dark-subtle {
  border-color: var(--bs-dark-border-subtle) !important;
}

.border-1 {
  border-width: 1px !important;
}

.border-2 {
  border-width: 2px !important;
}

.border-3 {
  border-width: 3px !important;
}

.border-4 {
  border-width: 4px !important;
}

.border-5 {
  border-width: 5px !important;
}

.border-opacity-10 {
  --bs-border-opacity: 0.1;
}

.border-opacity-25 {
  --bs-border-opacity: 0.25;
}

.border-opacity-50 {
  --bs-border-opacity: 0.5;
}

.border-opacity-75 {
  --bs-border-opacity: 0.75;
}

.border-opacity-100 {
  --bs-border-opacity: 1;
}

.w-25 {
  width: 25% !important;
}

.w-50 {
  width: 50% !important;
}

.w-75 {
  width: 75% !important;
}

.w-100 {
  width: 100% !important;
}

.w-auto {
  width: auto !important;
}

.mw-100 {
  max-width: 100% !important;
}

.vw-100 {
  width: 100vw !important;
}

.min-vw-100 {
  min-width: 100vw !important;
}

.h-25 {
  height: 25% !important;
}

.h-50 {
  height: 50% !important;
}

.h-75 {
  height: 75% !important;
}

.h-100 {
  height: 100% !important;
}

.h-auto {
  height: auto !important;
}

.mh-100 {
  max-height: 100% !important;
}

.vh-100 {
  height: 100vh !important;
}

.min-vh-100 {
  min-height: 100vh !important;
}

.flex-fill {
  flex: 1 1 auto !important;
}

.flex-row {
  flex-direction: row !important;
}

.flex-column {
  flex-direction: column !important;
}

.flex-row-reverse {
  flex-direction: row-reverse !important;
}

.flex-column-reverse {
  flex-direction: column-reverse !important;
}

.flex-grow-0 {
  flex-grow: 0 !important;
}

.flex-grow-1 {
  flex-grow: 1 !important;
}

.flex-shrink-0 {
  flex-shrink: 0 !important;
}

.flex-shrink-1 {
  flex-shrink: 1 !important;
}

.flex-wrap {
  flex-wrap: wrap !important;
}

.flex-nowrap {
  flex-wrap: nowrap !important;
}

.flex-wrap-reverse {
  flex-wrap: wrap-reverse !important;
}

.justify-content-start {
  justify-content: flex-start !important;
}

.justify-content-end {
  justify-content: flex-end !important;
}

.justify-content-center {
  justify-content: center !important;
}

.justify-content-between {
  justify-content: space-between !important;
}

.justify-content-around {
  justify-content: space-around !important;
}

.justify-content-evenly {
  justify-content: space-evenly !important;
}

.align-items-start {
  align-items: flex-start !important;
}

.align-items-end {
  align-items: flex-end !important;
}

.align-items-center {
  align-items: center !important;
}

.align-items-baseline {
  align-items: baseline !important;
}

.align-items-stretch {
  align-items: stretch !important;
}

.align-content-start {
  align-content: flex-start !important;
}

.align-content-end {
  align-content: flex-end !important;
}

.align-content-center {
  align-content: center !important;
}

.align-content-between {
  align-content: space-between !important;
}

.align-content-around {
  align-content: space-around !important;
}

.align-content-stretch {
  align-content: stretch !important;
}

.align-self-auto {
  align-self: auto !important;
}

.align-self-start {
  align-self: flex-start !important;
}

.align-self-end {
  align-self: flex-end !important;
}

.align-self-center {
  align-self: center !important;
}

.align-self-baseline {
  align-self: baseline !important;
}

.align-self-stretch {
  align-self: stretch !important;
}

.order-first {
  order: -1 !important;
}

.order-0 {
  order: 0 !important;
}

.order-1 {
  order: 1 !important;
}

.order-2 {
  order: 2 !important;
}

.order-3 {
  order: 3 !important;
}

.order-4 {
  order: 4 !important;
}

.order-5 {
  order: 5 !important;
}

.order-last {
  order: 6 !important;
}

.m-0 {
  margin: 0 !important;
}

.m-5 {
  margin: 0.3125rem !important;
}

.m-10 {
  margin: 0.625rem !important;
}

.m-15 {
  margin: 0.9375rem !important;
}

.m-20 {
  margin: 1.25rem !important;
}

.m-25 {
  margin: 1.5625rem !important;
}

.m-30 {
  margin: 1.875rem !important;
}

.m-40 {
  margin: 2.5rem !important;
}

.m-50 {
  margin: 3.125rem !important;
}

.m-70 {
  margin: 4.375rem !important;
}

.m-80 {
  margin: 5rem !important;
}

.m-100 {
  margin: 6.25rem !important;
}

.m-135 {
  margin: 8.4375rem !important;
}

.m-150 {
  margin: 9.375rem !important;
}

.m-auto {
  margin: auto !important;
}

.mx-0 {
  margin-right: 0 !important;
  margin-left: 0 !important;
}

.mx-5 {
  margin-right: 0.3125rem !important;
  margin-left: 0.3125rem !important;
}

.mx-10 {
  margin-right: 0.625rem !important;
  margin-left: 0.625rem !important;
}

.mx-15 {
  margin-right: 0.9375rem !important;
  margin-left: 0.9375rem !important;
}

.mx-20 {
  margin-right: 1.25rem !important;
  margin-left: 1.25rem !important;
}

.mx-25 {
  margin-right: 1.5625rem !important;
  margin-left: 1.5625rem !important;
}

.mx-30 {
  margin-right: 1.875rem !important;
  margin-left: 1.875rem !important;
}

.mx-40 {
  margin-right: 2.5rem !important;
  margin-left: 2.5rem !important;
}

.mx-50 {
  margin-right: 3.125rem !important;
  margin-left: 3.125rem !important;
}

.mx-70 {
  margin-right: 4.375rem !important;
  margin-left: 4.375rem !important;
}

.mx-80 {
  margin-right: 5rem !important;
  margin-left: 5rem !important;
}

.mx-100 {
  margin-right: 6.25rem !important;
  margin-left: 6.25rem !important;
}

.mx-135 {
  margin-right: 8.4375rem !important;
  margin-left: 8.4375rem !important;
}

.mx-150 {
  margin-right: 9.375rem !important;
  margin-left: 9.375rem !important;
}

.mx-auto {
  margin-right: auto !important;
  margin-left: auto !important;
}

.my-0 {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

.my-5 {
  margin-top: 0.3125rem !important;
  margin-bottom: 0.3125rem !important;
}

.my-10 {
  margin-top: 0.625rem !important;
  margin-bottom: 0.625rem !important;
}

.my-15 {
  margin-top: 0.9375rem !important;
  margin-bottom: 0.9375rem !important;
}

.my-20 {
  margin-top: 1.25rem !important;
  margin-bottom: 1.25rem !important;
}

.my-25 {
  margin-top: 1.5625rem !important;
  margin-bottom: 1.5625rem !important;
}

.my-30 {
  margin-top: 1.875rem !important;
  margin-bottom: 1.875rem !important;
}

.my-40 {
  margin-top: 2.5rem !important;
  margin-bottom: 2.5rem !important;
}

.my-50 {
  margin-top: 3.125rem !important;
  margin-bottom: 3.125rem !important;
}

.my-70 {
  margin-top: 4.375rem !important;
  margin-bottom: 4.375rem !important;
}

.my-80 {
  margin-top: 5rem !important;
  margin-bottom: 5rem !important;
}

.my-100 {
  margin-top: 6.25rem !important;
  margin-bottom: 6.25rem !important;
}

.my-135 {
  margin-top: 8.4375rem !important;
  margin-bottom: 8.4375rem !important;
}

.my-150 {
  margin-top: 9.375rem !important;
  margin-bottom: 9.375rem !important;
}

.my-auto {
  margin-top: auto !important;
  margin-bottom: auto !important;
}

.mt-0 {
  margin-top: 0 !important;
}

.mt-5 {
  margin-top: 0.3125rem !important;
}

.mt-10 {
  margin-top: 0.625rem !important;
}

.mt-15 {
  margin-top: 0.9375rem !important;
}

.mt-20 {
  margin-top: 1.25rem !important;
}

.mt-25 {
  margin-top: 1.5625rem !important;
}

.mt-30 {
  margin-top: 1.875rem !important;
}

.mt-40 {
  margin-top: 2.5rem !important;
}

.mt-50 {
  margin-top: 3.125rem !important;
}

.mt-70 {
  margin-top: 4.375rem !important;
}

.mt-80 {
  margin-top: 5rem !important;
}

.mt-100 {
  margin-top: 6.25rem !important;
}

.mt-135 {
  margin-top: 8.4375rem !important;
}

.mt-150 {
  margin-top: 9.375rem !important;
}

.mt-auto {
  margin-top: auto !important;
}

.me-0 {
  margin-right: 0 !important;
}

.me-5 {
  margin-right: 0.3125rem !important;
}

.me-10 {
  margin-right: 0.625rem !important;
}

.me-15 {
  margin-right: 0.9375rem !important;
}

.me-20 {
  margin-right: 1.25rem !important;
}

.me-25 {
  margin-right: 1.5625rem !important;
}

.me-30 {
  margin-right: 1.875rem !important;
}

.me-40 {
  margin-right: 2.5rem !important;
}

.me-50 {
  margin-right: 3.125rem !important;
}

.me-70 {
  margin-right: 4.375rem !important;
}

.me-80 {
  margin-right: 5rem !important;
}

.me-100 {
  margin-right: 6.25rem !important;
}

.me-135 {
  margin-right: 8.4375rem !important;
}

.me-150 {
  margin-right: 9.375rem !important;
}

.me-auto {
  margin-right: auto !important;
}

.mb-0 {
  margin-bottom: 0 !important;
}

.mb-5 {
  margin-bottom: 0.3125rem !important;
}

.mb-10 {
  margin-bottom: 0.625rem !important;
}

.mb-15 {
  margin-bottom: 0.9375rem !important;
}

.mb-20 {
  margin-bottom: 1.25rem !important;
}

.mb-25 {
  margin-bottom: 1.5625rem !important;
}

.mb-30 {
  margin-bottom: 1.875rem !important;
}

.mb-40 {
  margin-bottom: 2.5rem !important;
}

.mb-50 {
  margin-bottom: 3.125rem !important;
}

.mb-70 {
  margin-bottom: 4.375rem !important;
}

.mb-80 {
  margin-bottom: 5rem !important;
}

.mb-100 {
  margin-bottom: 6.25rem !important;
}

.mb-135 {
  margin-bottom: 8.4375rem !important;
}

.mb-150 {
  margin-bottom: 9.375rem !important;
}

.mb-auto {
  margin-bottom: auto !important;
}

.ms-0 {
  margin-left: 0 !important;
}

.ms-5 {
  margin-left: 0.3125rem !important;
}

.ms-10 {
  margin-left: 0.625rem !important;
}

.ms-15 {
  margin-left: 0.9375rem !important;
}

.ms-20 {
  margin-left: 1.25rem !important;
}

.ms-25 {
  margin-left: 1.5625rem !important;
}

.ms-30 {
  margin-left: 1.875rem !important;
}

.ms-40 {
  margin-left: 2.5rem !important;
}

.ms-50 {
  margin-left: 3.125rem !important;
}

.ms-70 {
  margin-left: 4.375rem !important;
}

.ms-80 {
  margin-left: 5rem !important;
}

.ms-100 {
  margin-left: 6.25rem !important;
}

.ms-135 {
  margin-left: 8.4375rem !important;
}

.ms-150 {
  margin-left: 9.375rem !important;
}

.ms-auto {
  margin-left: auto !important;
}

.m-n5 {
  margin: -0.3125rem !important;
}

.m-n10 {
  margin: -0.625rem !important;
}

.m-n15 {
  margin: -0.9375rem !important;
}

.m-n20 {
  margin: -1.25rem !important;
}

.m-n25 {
  margin: -1.5625rem !important;
}

.m-n30 {
  margin: -1.875rem !important;
}

.m-n40 {
  margin: -2.5rem !important;
}

.m-n50 {
  margin: -3.125rem !important;
}

.m-n70 {
  margin: -4.375rem !important;
}

.m-n80 {
  margin: -5rem !important;
}

.m-n100 {
  margin: -6.25rem !important;
}

.m-n135 {
  margin: -8.4375rem !important;
}

.m-n150 {
  margin: -9.375rem !important;
}

.mx-n5 {
  margin-right: -0.3125rem !important;
  margin-left: -0.3125rem !important;
}

.mx-n10 {
  margin-right: -0.625rem !important;
  margin-left: -0.625rem !important;
}

.mx-n15 {
  margin-right: -0.9375rem !important;
  margin-left: -0.9375rem !important;
}

.mx-n20 {
  margin-right: -1.25rem !important;
  margin-left: -1.25rem !important;
}

.mx-n25 {
  margin-right: -1.5625rem !important;
  margin-left: -1.5625rem !important;
}

.mx-n30 {
  margin-right: -1.875rem !important;
  margin-left: -1.875rem !important;
}

.mx-n40 {
  margin-right: -2.5rem !important;
  margin-left: -2.5rem !important;
}

.mx-n50 {
  margin-right: -3.125rem !important;
  margin-left: -3.125rem !important;
}

.mx-n70 {
  margin-right: -4.375rem !important;
  margin-left: -4.375rem !important;
}

.mx-n80 {
  margin-right: -5rem !important;
  margin-left: -5rem !important;
}

.mx-n100 {
  margin-right: -6.25rem !important;
  margin-left: -6.25rem !important;
}

.mx-n135 {
  margin-right: -8.4375rem !important;
  margin-left: -8.4375rem !important;
}

.mx-n150 {
  margin-right: -9.375rem !important;
  margin-left: -9.375rem !important;
}

.my-n5 {
  margin-top: -0.3125rem !important;
  margin-bottom: -0.3125rem !important;
}

.my-n10 {
  margin-top: -0.625rem !important;
  margin-bottom: -0.625rem !important;
}

.my-n15 {
  margin-top: -0.9375rem !important;
  margin-bottom: -0.9375rem !important;
}

.my-n20 {
  margin-top: -1.25rem !important;
  margin-bottom: -1.25rem !important;
}

.my-n25 {
  margin-top: -1.5625rem !important;
  margin-bottom: -1.5625rem !important;
}

.my-n30 {
  margin-top: -1.875rem !important;
  margin-bottom: -1.875rem !important;
}

.my-n40 {
  margin-top: -2.5rem !important;
  margin-bottom: -2.5rem !important;
}

.my-n50 {
  margin-top: -3.125rem !important;
  margin-bottom: -3.125rem !important;
}

.my-n70 {
  margin-top: -4.375rem !important;
  margin-bottom: -4.375rem !important;
}

.my-n80 {
  margin-top: -5rem !important;
  margin-bottom: -5rem !important;
}

.my-n100 {
  margin-top: -6.25rem !important;
  margin-bottom: -6.25rem !important;
}

.my-n135 {
  margin-top: -8.4375rem !important;
  margin-bottom: -8.4375rem !important;
}

.my-n150 {
  margin-top: -9.375rem !important;
  margin-bottom: -9.375rem !important;
}

.mt-n5 {
  margin-top: -0.3125rem !important;
}

.mt-n10 {
  margin-top: -0.625rem !important;
}

.mt-n15 {
  margin-top: -0.9375rem !important;
}

.mt-n20 {
  margin-top: -1.25rem !important;
}

.mt-n25 {
  margin-top: -1.5625rem !important;
}

.mt-n30 {
  margin-top: -1.875rem !important;
}

.mt-n40 {
  margin-top: -2.5rem !important;
}

.mt-n50 {
  margin-top: -3.125rem !important;
}

.mt-n70 {
  margin-top: -4.375rem !important;
}

.mt-n80 {
  margin-top: -5rem !important;
}

.mt-n100 {
  margin-top: -6.25rem !important;
}

.mt-n135 {
  margin-top: -8.4375rem !important;
}

.mt-n150 {
  margin-top: -9.375rem !important;
}

.me-n5 {
  margin-right: -0.3125rem !important;
}

.me-n10 {
  margin-right: -0.625rem !important;
}

.me-n15 {
  margin-right: -0.9375rem !important;
}

.me-n20 {
  margin-right: -1.25rem !important;
}

.me-n25 {
  margin-right: -1.5625rem !important;
}

.me-n30 {
  margin-right: -1.875rem !important;
}

.me-n40 {
  margin-right: -2.5rem !important;
}

.me-n50 {
  margin-right: -3.125rem !important;
}

.me-n70 {
  margin-right: -4.375rem !important;
}

.me-n80 {
  margin-right: -5rem !important;
}

.me-n100 {
  margin-right: -6.25rem !important;
}

.me-n135 {
  margin-right: -8.4375rem !important;
}

.me-n150 {
  margin-right: -9.375rem !important;
}

.mb-n5 {
  margin-bottom: -0.3125rem !important;
}

.mb-n10 {
  margin-bottom: -0.625rem !important;
}

.mb-n15 {
  margin-bottom: -0.9375rem !important;
}

.mb-n20 {
  margin-bottom: -1.25rem !important;
}

.mb-n25 {
  margin-bottom: -1.5625rem !important;
}

.mb-n30 {
  margin-bottom: -1.875rem !important;
}

.mb-n40 {
  margin-bottom: -2.5rem !important;
}

.mb-n50 {
  margin-bottom: -3.125rem !important;
}

.mb-n70 {
  margin-bottom: -4.375rem !important;
}

.mb-n80 {
  margin-bottom: -5rem !important;
}

.mb-n100 {
  margin-bottom: -6.25rem !important;
}

.mb-n135 {
  margin-bottom: -8.4375rem !important;
}

.mb-n150 {
  margin-bottom: -9.375rem !important;
}

.ms-n5 {
  margin-left: -0.3125rem !important;
}

.ms-n10 {
  margin-left: -0.625rem !important;
}

.ms-n15 {
  margin-left: -0.9375rem !important;
}

.ms-n20 {
  margin-left: -1.25rem !important;
}

.ms-n25 {
  margin-left: -1.5625rem !important;
}

.ms-n30 {
  margin-left: -1.875rem !important;
}

.ms-n40 {
  margin-left: -2.5rem !important;
}

.ms-n50 {
  margin-left: -3.125rem !important;
}

.ms-n70 {
  margin-left: -4.375rem !important;
}

.ms-n80 {
  margin-left: -5rem !important;
}

.ms-n100 {
  margin-left: -6.25rem !important;
}

.ms-n135 {
  margin-left: -8.4375rem !important;
}

.ms-n150 {
  margin-left: -9.375rem !important;
}

.p-0 {
  padding: 0 !important;
}

.p-5 {
  padding: 0.3125rem !important;
}

.p-10 {
  padding: 0.625rem !important;
}

.p-15 {
  padding: 0.9375rem !important;
}

.p-20 {
  padding: 1.25rem !important;
}

.p-25 {
  padding: 1.5625rem !important;
}

.p-30 {
  padding: 1.875rem !important;
}

.p-40 {
  padding: 2.5rem !important;
}

.p-50 {
  padding: 3.125rem !important;
}

.p-70 {
  padding: 4.375rem !important;
}

.p-80 {
  padding: 5rem !important;
}

.p-100 {
  padding: 6.25rem !important;
}

.p-135 {
  padding: 8.4375rem !important;
}

.p-150 {
  padding: 9.375rem !important;
}

.px-0 {
  padding-right: 0 !important;
  padding-left: 0 !important;
}

.px-5 {
  padding-right: 0.3125rem !important;
  padding-left: 0.3125rem !important;
}

.px-10 {
  padding-right: 0.625rem !important;
  padding-left: 0.625rem !important;
}

.px-15 {
  padding-right: 0.9375rem !important;
  padding-left: 0.9375rem !important;
}

.px-20 {
  padding-right: 1.25rem !important;
  padding-left: 1.25rem !important;
}

.px-25 {
  padding-right: 1.5625rem !important;
  padding-left: 1.5625rem !important;
}

.px-30 {
  padding-right: 1.875rem !important;
  padding-left: 1.875rem !important;
}

.px-40 {
  padding-right: 2.5rem !important;
  padding-left: 2.5rem !important;
}

.px-50 {
  padding-right: 3.125rem !important;
  padding-left: 3.125rem !important;
}

.px-70 {
  padding-right: 4.375rem !important;
  padding-left: 4.375rem !important;
}

.px-80 {
  padding-right: 5rem !important;
  padding-left: 5rem !important;
}

.px-100 {
  padding-right: 6.25rem !important;
  padding-left: 6.25rem !important;
}

.px-135 {
  padding-right: 8.4375rem !important;
  padding-left: 8.4375rem !important;
}

.px-150 {
  padding-right: 9.375rem !important;
  padding-left: 9.375rem !important;
}

.py-0 {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.py-5 {
  padding-top: 0.3125rem !important;
  padding-bottom: 0.3125rem !important;
}

.py-10 {
  padding-top: 0.625rem !important;
  padding-bottom: 0.625rem !important;
}

.py-15 {
  padding-top: 0.9375rem !important;
  padding-bottom: 0.9375rem !important;
}

.py-20 {
  padding-top: 1.25rem !important;
  padding-bottom: 1.25rem !important;
}

.py-25 {
  padding-top: 1.5625rem !important;
  padding-bottom: 1.5625rem !important;
}

.py-30 {
  padding-top: 1.875rem !important;
  padding-bottom: 1.875rem !important;
}

.py-40 {
  padding-top: 2.5rem !important;
  padding-bottom: 2.5rem !important;
}

.py-50 {
  padding-top: 3.125rem !important;
  padding-bottom: 3.125rem !important;
}

.py-70 {
  padding-top: 4.375rem !important;
  padding-bottom: 4.375rem !important;
}

.py-80 {
  padding-top: 5rem !important;
  padding-bottom: 5rem !important;
}

.py-100 {
  padding-top: 6.25rem !important;
  padding-bottom: 6.25rem !important;
}

.py-135 {
  padding-top: 8.4375rem !important;
  padding-bottom: 8.4375rem !important;
}

.py-150 {
  padding-top: 9.375rem !important;
  padding-bottom: 9.375rem !important;
}

.pt-0 {
  padding-top: 0 !important;
}

.pt-5 {
  padding-top: 0.3125rem !important;
}

.pt-10 {
  padding-top: 0.625rem !important;
}

.pt-15 {
  padding-top: 0.9375rem !important;
}

.pt-20 {
  padding-top: 1.25rem !important;
}

.pt-25 {
  padding-top: 1.5625rem !important;
}

.pt-30 {
  padding-top: 1.875rem !important;
}

.pt-40 {
  padding-top: 2.5rem !important;
}

.pt-50 {
  padding-top: 3.125rem !important;
}

.pt-70 {
  padding-top: 4.375rem !important;
}

.pt-80 {
  padding-top: 5rem !important;
}

.pt-100 {
  padding-top: 6.25rem !important;
}

.pt-135 {
  padding-top: 8.4375rem !important;
}

.pt-150 {
  padding-top: 9.375rem !important;
}

.pe-0 {
  padding-right: 0 !important;
}

.pe-5 {
  padding-right: 0.3125rem !important;
}

.pe-10 {
  padding-right: 0.625rem !important;
}

.pe-15 {
  padding-right: 0.9375rem !important;
}

.pe-20 {
  padding-right: 1.25rem !important;
}

.pe-25 {
  padding-right: 1.5625rem !important;
}

.pe-30 {
  padding-right: 1.875rem !important;
}

.pe-40 {
  padding-right: 2.5rem !important;
}

.pe-50 {
  padding-right: 3.125rem !important;
}

.pe-70 {
  padding-right: 4.375rem !important;
}

.pe-80 {
  padding-right: 5rem !important;
}

.pe-100 {
  padding-right: 6.25rem !important;
}

.pe-135 {
  padding-right: 8.4375rem !important;
}

.pe-150 {
  padding-right: 9.375rem !important;
}

.pb-0 {
  padding-bottom: 0 !important;
}

.pb-5 {
  padding-bottom: 0.3125rem !important;
}

.pb-10 {
  padding-bottom: 0.625rem !important;
}

.pb-15 {
  padding-bottom: 0.9375rem !important;
}

.pb-20 {
  padding-bottom: 1.25rem !important;
}

.pb-25 {
  padding-bottom: 1.5625rem !important;
}

.pb-30 {
  padding-bottom: 1.875rem !important;
}

.pb-40 {
  padding-bottom: 2.5rem !important;
}

.pb-50 {
  padding-bottom: 3.125rem !important;
}

.pb-70 {
  padding-bottom: 4.375rem !important;
}

.pb-80 {
  padding-bottom: 5rem !important;
}

.pb-100 {
  padding-bottom: 6.25rem !important;
}

.pb-135 {
  padding-bottom: 8.4375rem !important;
}

.pb-150 {
  padding-bottom: 9.375rem !important;
}

.ps-0 {
  padding-left: 0 !important;
}

.ps-5 {
  padding-left: 0.3125rem !important;
}

.ps-10 {
  padding-left: 0.625rem !important;
}

.ps-15 {
  padding-left: 0.9375rem !important;
}

.ps-20 {
  padding-left: 1.25rem !important;
}

.ps-25 {
  padding-left: 1.5625rem !important;
}

.ps-30 {
  padding-left: 1.875rem !important;
}

.ps-40 {
  padding-left: 2.5rem !important;
}

.ps-50 {
  padding-left: 3.125rem !important;
}

.ps-70 {
  padding-left: 4.375rem !important;
}

.ps-80 {
  padding-left: 5rem !important;
}

.ps-100 {
  padding-left: 6.25rem !important;
}

.ps-135 {
  padding-left: 8.4375rem !important;
}

.ps-150 {
  padding-left: 9.375rem !important;
}

.gap-0 {
  gap: 0 !important;
}

.gap-5 {
  gap: 0.3125rem !important;
}

.gap-10 {
  gap: 0.625rem !important;
}

.gap-15 {
  gap: 0.9375rem !important;
}

.gap-20 {
  gap: 1.25rem !important;
}

.gap-25 {
  gap: 1.5625rem !important;
}

.gap-30 {
  gap: 1.875rem !important;
}

.gap-40 {
  gap: 2.5rem !important;
}

.gap-50 {
  gap: 3.125rem !important;
}

.gap-70 {
  gap: 4.375rem !important;
}

.gap-80 {
  gap: 5rem !important;
}

.gap-100 {
  gap: 6.25rem !important;
}

.gap-135 {
  gap: 8.4375rem !important;
}

.gap-150 {
  gap: 9.375rem !important;
}

.row-gap-0 {
  row-gap: 0 !important;
}

.row-gap-5 {
  row-gap: 0.3125rem !important;
}

.row-gap-10 {
  row-gap: 0.625rem !important;
}

.row-gap-15 {
  row-gap: 0.9375rem !important;
}

.row-gap-20 {
  row-gap: 1.25rem !important;
}

.row-gap-25 {
  row-gap: 1.5625rem !important;
}

.row-gap-30 {
  row-gap: 1.875rem !important;
}

.row-gap-40 {
  row-gap: 2.5rem !important;
}

.row-gap-50 {
  row-gap: 3.125rem !important;
}

.row-gap-70 {
  row-gap: 4.375rem !important;
}

.row-gap-80 {
  row-gap: 5rem !important;
}

.row-gap-100 {
  row-gap: 6.25rem !important;
}

.row-gap-135 {
  row-gap: 8.4375rem !important;
}

.row-gap-150 {
  row-gap: 9.375rem !important;
}

.column-gap-0 {
  column-gap: 0 !important;
}

.column-gap-5 {
  column-gap: 0.3125rem !important;
}

.column-gap-10 {
  column-gap: 0.625rem !important;
}

.column-gap-15 {
  column-gap: 0.9375rem !important;
}

.column-gap-20 {
  column-gap: 1.25rem !important;
}

.column-gap-25 {
  column-gap: 1.5625rem !important;
}

.column-gap-30 {
  column-gap: 1.875rem !important;
}

.column-gap-40 {
  column-gap: 2.5rem !important;
}

.column-gap-50 {
  column-gap: 3.125rem !important;
}

.column-gap-70 {
  column-gap: 4.375rem !important;
}

.column-gap-80 {
  column-gap: 5rem !important;
}

.column-gap-100 {
  column-gap: 6.25rem !important;
}

.column-gap-135 {
  column-gap: 8.4375rem !important;
}

.column-gap-150 {
  column-gap: 9.375rem !important;
}

.font-monospace {
  font-family: var(--bs-font-monospace) !important;
}

.fs-1 {
  font-size: calc(1.35rem + 1.2vw) !important;
}

.fs-2 {
  font-size: calc(1.25625rem + 0.075vw) !important;
}

.fs-3 {
  font-size: 1.0625rem !important;
}

.fs-4 {
  font-size: 1.0625rem !important;
}

.fs-5 {
  font-size: 1.0625rem !important;
}

.fs-6 {
  font-size: 1rem !important;
}

.fst-italic {
  font-style: italic !important;
}

.fst-normal {
  font-style: normal !important;
}

.fw-lighter {
  font-weight: lighter !important;
}

.fw-light {
  font-weight: 300 !important;
}

.fw-normal {
  font-weight: 400 !important;
}

.fw-medium {
  font-weight: 500 !important;
}

.fw-semibold {
  font-weight: 600 !important;
}

.fw-bold {
  font-weight: 700 !important;
}

.fw-bolder {
  font-weight: bolder !important;
}

.lh-1 {
  line-height: 1 !important;
}

.lh-sm {
  line-height: 1.25 !important;
}

.lh-base {
  line-height: 1.625 !important;
}

.lh-lg {
  line-height: 2 !important;
}

.text-start {
  text-align: left !important;
}

.text-end {
  text-align: right !important;
}

.text-center {
  text-align: center !important;
}

.text-decoration-none {
  text-decoration: none !important;
}

.text-decoration-underline {
  text-decoration: underline !important;
}

.text-decoration-line-through {
  text-decoration: line-through !important;
}

.text-lowercase {
  text-transform: lowercase !important;
}

.text-uppercase {
  text-transform: uppercase !important;
}

.text-capitalize {
  text-transform: capitalize !important;
}

.text-wrap {
  white-space: normal !important;
}

.text-nowrap {
  white-space: nowrap !important;
}

/* rtl:begin:remove */
.text-break {
  word-wrap: break-word !important;
  word-break: break-word !important;
}

/* rtl:end:remove */
.text-primary {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-primary-rgb), var(--bs-text-opacity)) !important;
}

.text-secondary {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-secondary-rgb), var(--bs-text-opacity)) !important;
}

.text-success {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-success-rgb), var(--bs-text-opacity)) !important;
}

.text-info {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-info-rgb), var(--bs-text-opacity)) !important;
}

.text-warning {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-warning-rgb), var(--bs-text-opacity)) !important;
}

.text-danger {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-danger-rgb), var(--bs-text-opacity)) !important;
}

.text-dark {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-dark-rgb), var(--bs-text-opacity)) !important;
}

.text-green {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-green-rgb), var(--bs-text-opacity)) !important;
}

.text-celadon {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-celadon-rgb), var(--bs-text-opacity)) !important;
}

.text-red {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-red-rgb), var(--bs-text-opacity)) !important;
}

.text-warm {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-warm-rgb), var(--bs-text-opacity)) !important;
}

.text-summer {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-summer-rgb), var(--bs-text-opacity)) !important;
}

.text-autumn {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-autumn-rgb), var(--bs-text-opacity)) !important;
}

.text-winter {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-winter-rgb), var(--bs-text-opacity)) !important;
}

.text-spring {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-spring-rgb), var(--bs-text-opacity)) !important;
}

.text-black {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-black-rgb), var(--bs-text-opacity)) !important;
}

.text-white {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-white-rgb), var(--bs-text-opacity)) !important;
}

.text-body {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-body-color-rgb), var(--bs-text-opacity)) !important;
}

.text-muted {
  --bs-text-opacity: 1;
  color: var(--bs-secondary-color) !important;
}

.text-black-50 {
  --bs-text-opacity: 1;
  color: rgba(40, 23, 15, 0.5) !important;
}

.text-white-50 {
  --bs-text-opacity: 1;
  color: rgba(255, 249, 230, 0.5) !important;
}

.text-body-secondary {
  --bs-text-opacity: 1;
  color: var(--bs-secondary-color) !important;
}

.text-body-tertiary {
  --bs-text-opacity: 1;
  color: var(--bs-tertiary-color) !important;
}

.text-body-emphasis {
  --bs-text-opacity: 1;
  color: var(--bs-emphasis-color) !important;
}

.text-reset {
  --bs-text-opacity: 1;
  color: inherit !important;
}

.text-opacity-25 {
  --bs-text-opacity: 0.25;
}

.text-opacity-50 {
  --bs-text-opacity: 0.5;
}

.text-opacity-75 {
  --bs-text-opacity: 0.75;
}

.text-opacity-100 {
  --bs-text-opacity: 1;
}

.text-primary-emphasis {
  color: var(--bs-primary-text-emphasis) !important;
}

.text-secondary-emphasis {
  color: var(--bs-secondary-text-emphasis) !important;
}

.text-success-emphasis {
  color: var(--bs-success-text-emphasis) !important;
}

.text-info-emphasis {
  color: var(--bs-info-text-emphasis) !important;
}

.text-warning-emphasis {
  color: var(--bs-warning-text-emphasis) !important;
}

.text-danger-emphasis {
  color: var(--bs-danger-text-emphasis) !important;
}

.text-light-emphasis {
  color: var(--bs-light-text-emphasis) !important;
}

.text-dark-emphasis {
  color: var(--bs-dark-text-emphasis) !important;
}

.link-opacity-10 {
  --bs-link-opacity: 0.1;
}

.link-opacity-10-hover:hover {
  --bs-link-opacity: 0.1;
}

.link-opacity-25 {
  --bs-link-opacity: 0.25;
}

.link-opacity-25-hover:hover {
  --bs-link-opacity: 0.25;
}

.link-opacity-50 {
  --bs-link-opacity: 0.5;
}

.link-opacity-50-hover:hover {
  --bs-link-opacity: 0.5;
}

.link-opacity-75 {
  --bs-link-opacity: 0.75;
}

.link-opacity-75-hover:hover {
  --bs-link-opacity: 0.75;
}

.link-opacity-100 {
  --bs-link-opacity: 1;
}

.link-opacity-100-hover:hover {
  --bs-link-opacity: 1;
}

.link-offset-1 {
  text-underline-offset: 0.125em !important;
}

.link-offset-1-hover:hover {
  text-underline-offset: 0.125em !important;
}

.link-offset-2 {
  text-underline-offset: 0.25em !important;
}

.link-offset-2-hover:hover {
  text-underline-offset: 0.25em !important;
}

.link-offset-3 {
  text-underline-offset: 0.375em !important;
}

.link-offset-3-hover:hover {
  text-underline-offset: 0.375em !important;
}

.link-underline-primary {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-primary-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-secondary {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-secondary-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-success {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-success-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-info {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-info-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-warning {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-warning-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-danger {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-danger-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-dark {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-dark-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-green {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-green-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-celadon {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-celadon-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-red {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-red-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-warm {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-warm-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-summer {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-summer-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-autumn {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-autumn-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-winter {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-winter-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-spring {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-spring-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-link-color-rgb), var(--bs-link-underline-opacity, 1)) !important;
}

.link-underline-opacity-0 {
  --bs-link-underline-opacity: 0;
}

.link-underline-opacity-0-hover:hover {
  --bs-link-underline-opacity: 0;
}

.link-underline-opacity-10 {
  --bs-link-underline-opacity: 0.1;
}

.link-underline-opacity-10-hover:hover {
  --bs-link-underline-opacity: 0.1;
}

.link-underline-opacity-25 {
  --bs-link-underline-opacity: 0.25;
}

.link-underline-opacity-25-hover:hover {
  --bs-link-underline-opacity: 0.25;
}

.link-underline-opacity-50 {
  --bs-link-underline-opacity: 0.5;
}

.link-underline-opacity-50-hover:hover {
  --bs-link-underline-opacity: 0.5;
}

.link-underline-opacity-75 {
  --bs-link-underline-opacity: 0.75;
}

.link-underline-opacity-75-hover:hover {
  --bs-link-underline-opacity: 0.75;
}

.link-underline-opacity-100 {
  --bs-link-underline-opacity: 1;
}

.link-underline-opacity-100-hover:hover {
  --bs-link-underline-opacity: 1;
}

.bg-primary {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-primary-rgb), var(--bs-bg-opacity)) !important;
}

.bg-secondary {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-secondary-rgb), var(--bs-bg-opacity)) !important;
}

.bg-success {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-success-rgb), var(--bs-bg-opacity)) !important;
}

.bg-info {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-info-rgb), var(--bs-bg-opacity)) !important;
}

.bg-warning {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-warning-rgb), var(--bs-bg-opacity)) !important;
}

.bg-danger {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-danger-rgb), var(--bs-bg-opacity)) !important;
}

.bg-dark {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-dark-rgb), var(--bs-bg-opacity)) !important;
}

.bg-green {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-green-rgb), var(--bs-bg-opacity)) !important;
}

.bg-celadon {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-celadon-rgb), var(--bs-bg-opacity)) !important;
}

.bg-red {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-red-rgb), var(--bs-bg-opacity)) !important;
}

.bg-warm {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-warm-rgb), var(--bs-bg-opacity)) !important;
}

.bg-summer {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-summer-rgb), var(--bs-bg-opacity)) !important;
}

.bg-autumn {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-autumn-rgb), var(--bs-bg-opacity)) !important;
}

.bg-winter {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-winter-rgb), var(--bs-bg-opacity)) !important;
}

.bg-spring {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-spring-rgb), var(--bs-bg-opacity)) !important;
}

.bg-black {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-black-rgb), var(--bs-bg-opacity)) !important;
}

.bg-white {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-white-rgb), var(--bs-bg-opacity)) !important;
}

.bg-body {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-body-bg-rgb), var(--bs-bg-opacity)) !important;
}

.bg-transparent {
  --bs-bg-opacity: 1;
  background-color: transparent !important;
}

.bg-body-secondary {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-secondary-bg-rgb), var(--bs-bg-opacity)) !important;
}

.bg-body-tertiary {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-tertiary-bg-rgb), var(--bs-bg-opacity)) !important;
}

.bg-opacity-10 {
  --bs-bg-opacity: 0.1;
}

.bg-opacity-25 {
  --bs-bg-opacity: 0.25;
}

.bg-opacity-50 {
  --bs-bg-opacity: 0.5;
}

.bg-opacity-75 {
  --bs-bg-opacity: 0.75;
}

.bg-opacity-100 {
  --bs-bg-opacity: 1;
}

.bg-primary-subtle {
  background-color: var(--bs-primary-bg-subtle) !important;
}

.bg-secondary-subtle {
  background-color: var(--bs-secondary-bg-subtle) !important;
}

.bg-success-subtle {
  background-color: var(--bs-success-bg-subtle) !important;
}

.bg-info-subtle {
  background-color: var(--bs-info-bg-subtle) !important;
}

.bg-warning-subtle {
  background-color: var(--bs-warning-bg-subtle) !important;
}

.bg-danger-subtle {
  background-color: var(--bs-danger-bg-subtle) !important;
}

.bg-light-subtle {
  background-color: var(--bs-light-bg-subtle) !important;
}

.bg-dark-subtle {
  background-color: var(--bs-dark-bg-subtle) !important;
}

.bg-gradient {
  background-image: var(--bs-gradient) !important;
}

.user-select-all {
  user-select: all !important;
}

.user-select-auto {
  user-select: auto !important;
}

.user-select-none {
  user-select: none !important;
}

.pe-none {
  pointer-events: none !important;
}

.pe-auto {
  pointer-events: auto !important;
}

.rounded {
  border-radius: var(--bs-border-radius) !important;
}

.rounded-0 {
  border-radius: 0 !important;
}

.rounded-1 {
  border-radius: var(--bs-border-radius-sm) !important;
}

.rounded-2 {
  border-radius: var(--bs-border-radius) !important;
}

.rounded-3 {
  border-radius: var(--bs-border-radius-lg) !important;
}

.rounded-4 {
  border-radius: var(--bs-border-radius-xl) !important;
}

.rounded-5 {
  border-radius: var(--bs-border-radius-xxl) !important;
}

.rounded-circle {
  border-radius: 50% !important;
}

.rounded-pill {
  border-radius: var(--bs-border-radius-pill) !important;
}

.rounded-top {
  border-top-left-radius: var(--bs-border-radius) !important;
  border-top-right-radius: var(--bs-border-radius) !important;
}

.rounded-top-0 {
  border-top-left-radius: 0 !important;
  border-top-right-radius: 0 !important;
}

.rounded-top-1 {
  border-top-left-radius: var(--bs-border-radius-sm) !important;
  border-top-right-radius: var(--bs-border-radius-sm) !important;
}

.rounded-top-2 {
  border-top-left-radius: var(--bs-border-radius) !important;
  border-top-right-radius: var(--bs-border-radius) !important;
}

.rounded-top-3 {
  border-top-left-radius: var(--bs-border-radius-lg) !important;
  border-top-right-radius: var(--bs-border-radius-lg) !important;
}

.rounded-top-4 {
  border-top-left-radius: var(--bs-border-radius-xl) !important;
  border-top-right-radius: var(--bs-border-radius-xl) !important;
}

.rounded-top-5 {
  border-top-left-radius: var(--bs-border-radius-xxl) !important;
  border-top-right-radius: var(--bs-border-radius-xxl) !important;
}

.rounded-top-circle {
  border-top-left-radius: 50% !important;
  border-top-right-radius: 50% !important;
}

.rounded-top-pill {
  border-top-left-radius: var(--bs-border-radius-pill) !important;
  border-top-right-radius: var(--bs-border-radius-pill) !important;
}

.rounded-end {
  border-top-right-radius: var(--bs-border-radius) !important;
  border-bottom-right-radius: var(--bs-border-radius) !important;
}

.rounded-end-0 {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

.rounded-end-1 {
  border-top-right-radius: var(--bs-border-radius-sm) !important;
  border-bottom-right-radius: var(--bs-border-radius-sm) !important;
}

.rounded-end-2 {
  border-top-right-radius: var(--bs-border-radius) !important;
  border-bottom-right-radius: var(--bs-border-radius) !important;
}

.rounded-end-3 {
  border-top-right-radius: var(--bs-border-radius-lg) !important;
  border-bottom-right-radius: var(--bs-border-radius-lg) !important;
}

.rounded-end-4 {
  border-top-right-radius: var(--bs-border-radius-xl) !important;
  border-bottom-right-radius: var(--bs-border-radius-xl) !important;
}

.rounded-end-5 {
  border-top-right-radius: var(--bs-border-radius-xxl) !important;
  border-bottom-right-radius: var(--bs-border-radius-xxl) !important;
}

.rounded-end-circle {
  border-top-right-radius: 50% !important;
  border-bottom-right-radius: 50% !important;
}

.rounded-end-pill {
  border-top-right-radius: var(--bs-border-radius-pill) !important;
  border-bottom-right-radius: var(--bs-border-radius-pill) !important;
}

.rounded-bottom {
  border-bottom-right-radius: var(--bs-border-radius) !important;
  border-bottom-left-radius: var(--bs-border-radius) !important;
}

.rounded-bottom-0 {
  border-bottom-right-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}

.rounded-bottom-1 {
  border-bottom-right-radius: var(--bs-border-radius-sm) !important;
  border-bottom-left-radius: var(--bs-border-radius-sm) !important;
}

.rounded-bottom-2 {
  border-bottom-right-radius: var(--bs-border-radius) !important;
  border-bottom-left-radius: var(--bs-border-radius) !important;
}

.rounded-bottom-3 {
  border-bottom-right-radius: var(--bs-border-radius-lg) !important;
  border-bottom-left-radius: var(--bs-border-radius-lg) !important;
}

.rounded-bottom-4 {
  border-bottom-right-radius: var(--bs-border-radius-xl) !important;
  border-bottom-left-radius: var(--bs-border-radius-xl) !important;
}

.rounded-bottom-5 {
  border-bottom-right-radius: var(--bs-border-radius-xxl) !important;
  border-bottom-left-radius: var(--bs-border-radius-xxl) !important;
}

.rounded-bottom-circle {
  border-bottom-right-radius: 50% !important;
  border-bottom-left-radius: 50% !important;
}

.rounded-bottom-pill {
  border-bottom-right-radius: var(--bs-border-radius-pill) !important;
  border-bottom-left-radius: var(--bs-border-radius-pill) !important;
}

.rounded-start {
  border-bottom-left-radius: var(--bs-border-radius) !important;
  border-top-left-radius: var(--bs-border-radius) !important;
}

.rounded-start-0 {
  border-bottom-left-radius: 0 !important;
  border-top-left-radius: 0 !important;
}

.rounded-start-1 {
  border-bottom-left-radius: var(--bs-border-radius-sm) !important;
  border-top-left-radius: var(--bs-border-radius-sm) !important;
}

.rounded-start-2 {
  border-bottom-left-radius: var(--bs-border-radius) !important;
  border-top-left-radius: var(--bs-border-radius) !important;
}

.rounded-start-3 {
  border-bottom-left-radius: var(--bs-border-radius-lg) !important;
  border-top-left-radius: var(--bs-border-radius-lg) !important;
}

.rounded-start-4 {
  border-bottom-left-radius: var(--bs-border-radius-xl) !important;
  border-top-left-radius: var(--bs-border-radius-xl) !important;
}

.rounded-start-5 {
  border-bottom-left-radius: var(--bs-border-radius-xxl) !important;
  border-top-left-radius: var(--bs-border-radius-xxl) !important;
}

.rounded-start-circle {
  border-bottom-left-radius: 50% !important;
  border-top-left-radius: 50% !important;
}

.rounded-start-pill {
  border-bottom-left-radius: var(--bs-border-radius-pill) !important;
  border-top-left-radius: var(--bs-border-radius-pill) !important;
}

.visible {
  visibility: visible !important;
}

.invisible {
  visibility: hidden !important;
}

.z-n1 {
  z-index: -1 !important;
}

.z-0 {
  z-index: 0 !important;
}

.z-1 {
  z-index: 1 !important;
}

.z-2 {
  z-index: 2 !important;
}

.z-3 {
  z-index: 3 !important;
}

@media (min-width: 576px) {
  .float-sm-start {
    float: left !important;
  }

  .float-sm-end {
    float: right !important;
  }

  .float-sm-none {
    float: none !important;
  }

  .object-fit-sm-contain {
    object-fit: contain !important;
  }

  .object-fit-sm-cover {
    object-fit: cover !important;
  }

  .object-fit-sm-fill {
    object-fit: fill !important;
  }

  .object-fit-sm-scale {
    object-fit: scale-down !important;
  }

  .object-fit-sm-none {
    object-fit: none !important;
  }

  .d-sm-inline {
    display: inline !important;
  }

  .d-sm-inline-block {
    display: inline-block !important;
  }

  .d-sm-block {
    display: block !important;
  }

  .d-sm-grid {
    display: grid !important;
  }

  .d-sm-inline-grid {
    display: inline-grid !important;
  }

  .d-sm-table {
    display: table !important;
  }

  .d-sm-table-row {
    display: table-row !important;
  }

  .d-sm-table-cell {
    display: table-cell !important;
  }

  .d-sm-flex {
    display: flex !important;
  }

  .d-sm-inline-flex {
    display: inline-flex !important;
  }

  .d-sm-none {
    display: none !important;
  }

  .flex-sm-fill {
    flex: 1 1 auto !important;
  }

  .flex-sm-row {
    flex-direction: row !important;
  }

  .flex-sm-column {
    flex-direction: column !important;
  }

  .flex-sm-row-reverse {
    flex-direction: row-reverse !important;
  }

  .flex-sm-column-reverse {
    flex-direction: column-reverse !important;
  }

  .flex-sm-grow-0 {
    flex-grow: 0 !important;
  }

  .flex-sm-grow-1 {
    flex-grow: 1 !important;
  }

  .flex-sm-shrink-0 {
    flex-shrink: 0 !important;
  }

  .flex-sm-shrink-1 {
    flex-shrink: 1 !important;
  }

  .flex-sm-wrap {
    flex-wrap: wrap !important;
  }

  .flex-sm-nowrap {
    flex-wrap: nowrap !important;
  }

  .flex-sm-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }

  .justify-content-sm-start {
    justify-content: flex-start !important;
  }

  .justify-content-sm-end {
    justify-content: flex-end !important;
  }

  .justify-content-sm-center {
    justify-content: center !important;
  }

  .justify-content-sm-between {
    justify-content: space-between !important;
  }

  .justify-content-sm-around {
    justify-content: space-around !important;
  }

  .justify-content-sm-evenly {
    justify-content: space-evenly !important;
  }

  .align-items-sm-start {
    align-items: flex-start !important;
  }

  .align-items-sm-end {
    align-items: flex-end !important;
  }

  .align-items-sm-center {
    align-items: center !important;
  }

  .align-items-sm-baseline {
    align-items: baseline !important;
  }

  .align-items-sm-stretch {
    align-items: stretch !important;
  }

  .align-content-sm-start {
    align-content: flex-start !important;
  }

  .align-content-sm-end {
    align-content: flex-end !important;
  }

  .align-content-sm-center {
    align-content: center !important;
  }

  .align-content-sm-between {
    align-content: space-between !important;
  }

  .align-content-sm-around {
    align-content: space-around !important;
  }

  .align-content-sm-stretch {
    align-content: stretch !important;
  }

  .align-self-sm-auto {
    align-self: auto !important;
  }

  .align-self-sm-start {
    align-self: flex-start !important;
  }

  .align-self-sm-end {
    align-self: flex-end !important;
  }

  .align-self-sm-center {
    align-self: center !important;
  }

  .align-self-sm-baseline {
    align-self: baseline !important;
  }

  .align-self-sm-stretch {
    align-self: stretch !important;
  }

  .order-sm-first {
    order: -1 !important;
  }

  .order-sm-0 {
    order: 0 !important;
  }

  .order-sm-1 {
    order: 1 !important;
  }

  .order-sm-2 {
    order: 2 !important;
  }

  .order-sm-3 {
    order: 3 !important;
  }

  .order-sm-4 {
    order: 4 !important;
  }

  .order-sm-5 {
    order: 5 !important;
  }

  .order-sm-last {
    order: 6 !important;
  }

  .m-sm-0 {
    margin: 0 !important;
  }

  .m-sm-5 {
    margin: 0.3125rem !important;
  }

  .m-sm-10 {
    margin: 0.625rem !important;
  }

  .m-sm-15 {
    margin: 0.9375rem !important;
  }

  .m-sm-20 {
    margin: 1.25rem !important;
  }

  .m-sm-25 {
    margin: 1.5625rem !important;
  }

  .m-sm-30 {
    margin: 1.875rem !important;
  }

  .m-sm-40 {
    margin: 2.5rem !important;
  }

  .m-sm-50 {
    margin: 3.125rem !important;
  }

  .m-sm-70 {
    margin: 4.375rem !important;
  }

  .m-sm-80 {
    margin: 5rem !important;
  }

  .m-sm-100 {
    margin: 6.25rem !important;
  }

  .m-sm-135 {
    margin: 8.4375rem !important;
  }

  .m-sm-150 {
    margin: 9.375rem !important;
  }

  .m-sm-auto {
    margin: auto !important;
  }

  .mx-sm-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }

  .mx-sm-5 {
    margin-right: 0.3125rem !important;
    margin-left: 0.3125rem !important;
  }

  .mx-sm-10 {
    margin-right: 0.625rem !important;
    margin-left: 0.625rem !important;
  }

  .mx-sm-15 {
    margin-right: 0.9375rem !important;
    margin-left: 0.9375rem !important;
  }

  .mx-sm-20 {
    margin-right: 1.25rem !important;
    margin-left: 1.25rem !important;
  }

  .mx-sm-25 {
    margin-right: 1.5625rem !important;
    margin-left: 1.5625rem !important;
  }

  .mx-sm-30 {
    margin-right: 1.875rem !important;
    margin-left: 1.875rem !important;
  }

  .mx-sm-40 {
    margin-right: 2.5rem !important;
    margin-left: 2.5rem !important;
  }

  .mx-sm-50 {
    margin-right: 3.125rem !important;
    margin-left: 3.125rem !important;
  }

  .mx-sm-70 {
    margin-right: 4.375rem !important;
    margin-left: 4.375rem !important;
  }

  .mx-sm-80 {
    margin-right: 5rem !important;
    margin-left: 5rem !important;
  }

  .mx-sm-100 {
    margin-right: 6.25rem !important;
    margin-left: 6.25rem !important;
  }

  .mx-sm-135 {
    margin-right: 8.4375rem !important;
    margin-left: 8.4375rem !important;
  }

  .mx-sm-150 {
    margin-right: 9.375rem !important;
    margin-left: 9.375rem !important;
  }

  .mx-sm-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }

  .my-sm-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }

  .my-sm-5 {
    margin-top: 0.3125rem !important;
    margin-bottom: 0.3125rem !important;
  }

  .my-sm-10 {
    margin-top: 0.625rem !important;
    margin-bottom: 0.625rem !important;
  }

  .my-sm-15 {
    margin-top: 0.9375rem !important;
    margin-bottom: 0.9375rem !important;
  }

  .my-sm-20 {
    margin-top: 1.25rem !important;
    margin-bottom: 1.25rem !important;
  }

  .my-sm-25 {
    margin-top: 1.5625rem !important;
    margin-bottom: 1.5625rem !important;
  }

  .my-sm-30 {
    margin-top: 1.875rem !important;
    margin-bottom: 1.875rem !important;
  }

  .my-sm-40 {
    margin-top: 2.5rem !important;
    margin-bottom: 2.5rem !important;
  }

  .my-sm-50 {
    margin-top: 3.125rem !important;
    margin-bottom: 3.125rem !important;
  }

  .my-sm-70 {
    margin-top: 4.375rem !important;
    margin-bottom: 4.375rem !important;
  }

  .my-sm-80 {
    margin-top: 5rem !important;
    margin-bottom: 5rem !important;
  }

  .my-sm-100 {
    margin-top: 6.25rem !important;
    margin-bottom: 6.25rem !important;
  }

  .my-sm-135 {
    margin-top: 8.4375rem !important;
    margin-bottom: 8.4375rem !important;
  }

  .my-sm-150 {
    margin-top: 9.375rem !important;
    margin-bottom: 9.375rem !important;
  }

  .my-sm-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }

  .mt-sm-0 {
    margin-top: 0 !important;
  }

  .mt-sm-5 {
    margin-top: 0.3125rem !important;
  }

  .mt-sm-10 {
    margin-top: 0.625rem !important;
  }

  .mt-sm-15 {
    margin-top: 0.9375rem !important;
  }

  .mt-sm-20 {
    margin-top: 1.25rem !important;
  }

  .mt-sm-25 {
    margin-top: 1.5625rem !important;
  }

  .mt-sm-30 {
    margin-top: 1.875rem !important;
  }

  .mt-sm-40 {
    margin-top: 2.5rem !important;
  }

  .mt-sm-50 {
    margin-top: 3.125rem !important;
  }

  .mt-sm-70 {
    margin-top: 4.375rem !important;
  }

  .mt-sm-80 {
    margin-top: 5rem !important;
  }

  .mt-sm-100 {
    margin-top: 6.25rem !important;
  }

  .mt-sm-135 {
    margin-top: 8.4375rem !important;
  }

  .mt-sm-150 {
    margin-top: 9.375rem !important;
  }

  .mt-sm-auto {
    margin-top: auto !important;
  }

  .me-sm-0 {
    margin-right: 0 !important;
  }

  .me-sm-5 {
    margin-right: 0.3125rem !important;
  }

  .me-sm-10 {
    margin-right: 0.625rem !important;
  }

  .me-sm-15 {
    margin-right: 0.9375rem !important;
  }

  .me-sm-20 {
    margin-right: 1.25rem !important;
  }

  .me-sm-25 {
    margin-right: 1.5625rem !important;
  }

  .me-sm-30 {
    margin-right: 1.875rem !important;
  }

  .me-sm-40 {
    margin-right: 2.5rem !important;
  }

  .me-sm-50 {
    margin-right: 3.125rem !important;
  }

  .me-sm-70 {
    margin-right: 4.375rem !important;
  }

  .me-sm-80 {
    margin-right: 5rem !important;
  }

  .me-sm-100 {
    margin-right: 6.25rem !important;
  }

  .me-sm-135 {
    margin-right: 8.4375rem !important;
  }

  .me-sm-150 {
    margin-right: 9.375rem !important;
  }

  .me-sm-auto {
    margin-right: auto !important;
  }

  .mb-sm-0 {
    margin-bottom: 0 !important;
  }

  .mb-sm-5 {
    margin-bottom: 0.3125rem !important;
  }

  .mb-sm-10 {
    margin-bottom: 0.625rem !important;
  }

  .mb-sm-15 {
    margin-bottom: 0.9375rem !important;
  }

  .mb-sm-20 {
    margin-bottom: 1.25rem !important;
  }

  .mb-sm-25 {
    margin-bottom: 1.5625rem !important;
  }

  .mb-sm-30 {
    margin-bottom: 1.875rem !important;
  }

  .mb-sm-40 {
    margin-bottom: 2.5rem !important;
  }

  .mb-sm-50 {
    margin-bottom: 3.125rem !important;
  }

  .mb-sm-70 {
    margin-bottom: 4.375rem !important;
  }

  .mb-sm-80 {
    margin-bottom: 5rem !important;
  }

  .mb-sm-100 {
    margin-bottom: 6.25rem !important;
  }

  .mb-sm-135 {
    margin-bottom: 8.4375rem !important;
  }

  .mb-sm-150 {
    margin-bottom: 9.375rem !important;
  }

  .mb-sm-auto {
    margin-bottom: auto !important;
  }

  .ms-sm-0 {
    margin-left: 0 !important;
  }

  .ms-sm-5 {
    margin-left: 0.3125rem !important;
  }

  .ms-sm-10 {
    margin-left: 0.625rem !important;
  }

  .ms-sm-15 {
    margin-left: 0.9375rem !important;
  }

  .ms-sm-20 {
    margin-left: 1.25rem !important;
  }

  .ms-sm-25 {
    margin-left: 1.5625rem !important;
  }

  .ms-sm-30 {
    margin-left: 1.875rem !important;
  }

  .ms-sm-40 {
    margin-left: 2.5rem !important;
  }

  .ms-sm-50 {
    margin-left: 3.125rem !important;
  }

  .ms-sm-70 {
    margin-left: 4.375rem !important;
  }

  .ms-sm-80 {
    margin-left: 5rem !important;
  }

  .ms-sm-100 {
    margin-left: 6.25rem !important;
  }

  .ms-sm-135 {
    margin-left: 8.4375rem !important;
  }

  .ms-sm-150 {
    margin-left: 9.375rem !important;
  }

  .ms-sm-auto {
    margin-left: auto !important;
  }

  .m-sm-n5 {
    margin: -0.3125rem !important;
  }

  .m-sm-n10 {
    margin: -0.625rem !important;
  }

  .m-sm-n15 {
    margin: -0.9375rem !important;
  }

  .m-sm-n20 {
    margin: -1.25rem !important;
  }

  .m-sm-n25 {
    margin: -1.5625rem !important;
  }

  .m-sm-n30 {
    margin: -1.875rem !important;
  }

  .m-sm-n40 {
    margin: -2.5rem !important;
  }

  .m-sm-n50 {
    margin: -3.125rem !important;
  }

  .m-sm-n70 {
    margin: -4.375rem !important;
  }

  .m-sm-n80 {
    margin: -5rem !important;
  }

  .m-sm-n100 {
    margin: -6.25rem !important;
  }

  .m-sm-n135 {
    margin: -8.4375rem !important;
  }

  .m-sm-n150 {
    margin: -9.375rem !important;
  }

  .mx-sm-n5 {
    margin-right: -0.3125rem !important;
    margin-left: -0.3125rem !important;
  }

  .mx-sm-n10 {
    margin-right: -0.625rem !important;
    margin-left: -0.625rem !important;
  }

  .mx-sm-n15 {
    margin-right: -0.9375rem !important;
    margin-left: -0.9375rem !important;
  }

  .mx-sm-n20 {
    margin-right: -1.25rem !important;
    margin-left: -1.25rem !important;
  }

  .mx-sm-n25 {
    margin-right: -1.5625rem !important;
    margin-left: -1.5625rem !important;
  }

  .mx-sm-n30 {
    margin-right: -1.875rem !important;
    margin-left: -1.875rem !important;
  }

  .mx-sm-n40 {
    margin-right: -2.5rem !important;
    margin-left: -2.5rem !important;
  }

  .mx-sm-n50 {
    margin-right: -3.125rem !important;
    margin-left: -3.125rem !important;
  }

  .mx-sm-n70 {
    margin-right: -4.375rem !important;
    margin-left: -4.375rem !important;
  }

  .mx-sm-n80 {
    margin-right: -5rem !important;
    margin-left: -5rem !important;
  }

  .mx-sm-n100 {
    margin-right: -6.25rem !important;
    margin-left: -6.25rem !important;
  }

  .mx-sm-n135 {
    margin-right: -8.4375rem !important;
    margin-left: -8.4375rem !important;
  }

  .mx-sm-n150 {
    margin-right: -9.375rem !important;
    margin-left: -9.375rem !important;
  }

  .my-sm-n5 {
    margin-top: -0.3125rem !important;
    margin-bottom: -0.3125rem !important;
  }

  .my-sm-n10 {
    margin-top: -0.625rem !important;
    margin-bottom: -0.625rem !important;
  }

  .my-sm-n15 {
    margin-top: -0.9375rem !important;
    margin-bottom: -0.9375rem !important;
  }

  .my-sm-n20 {
    margin-top: -1.25rem !important;
    margin-bottom: -1.25rem !important;
  }

  .my-sm-n25 {
    margin-top: -1.5625rem !important;
    margin-bottom: -1.5625rem !important;
  }

  .my-sm-n30 {
    margin-top: -1.875rem !important;
    margin-bottom: -1.875rem !important;
  }

  .my-sm-n40 {
    margin-top: -2.5rem !important;
    margin-bottom: -2.5rem !important;
  }

  .my-sm-n50 {
    margin-top: -3.125rem !important;
    margin-bottom: -3.125rem !important;
  }

  .my-sm-n70 {
    margin-top: -4.375rem !important;
    margin-bottom: -4.375rem !important;
  }

  .my-sm-n80 {
    margin-top: -5rem !important;
    margin-bottom: -5rem !important;
  }

  .my-sm-n100 {
    margin-top: -6.25rem !important;
    margin-bottom: -6.25rem !important;
  }

  .my-sm-n135 {
    margin-top: -8.4375rem !important;
    margin-bottom: -8.4375rem !important;
  }

  .my-sm-n150 {
    margin-top: -9.375rem !important;
    margin-bottom: -9.375rem !important;
  }

  .mt-sm-n5 {
    margin-top: -0.3125rem !important;
  }

  .mt-sm-n10 {
    margin-top: -0.625rem !important;
  }

  .mt-sm-n15 {
    margin-top: -0.9375rem !important;
  }

  .mt-sm-n20 {
    margin-top: -1.25rem !important;
  }

  .mt-sm-n25 {
    margin-top: -1.5625rem !important;
  }

  .mt-sm-n30 {
    margin-top: -1.875rem !important;
  }

  .mt-sm-n40 {
    margin-top: -2.5rem !important;
  }

  .mt-sm-n50 {
    margin-top: -3.125rem !important;
  }

  .mt-sm-n70 {
    margin-top: -4.375rem !important;
  }

  .mt-sm-n80 {
    margin-top: -5rem !important;
  }

  .mt-sm-n100 {
    margin-top: -6.25rem !important;
  }

  .mt-sm-n135 {
    margin-top: -8.4375rem !important;
  }

  .mt-sm-n150 {
    margin-top: -9.375rem !important;
  }

  .me-sm-n5 {
    margin-right: -0.3125rem !important;
  }

  .me-sm-n10 {
    margin-right: -0.625rem !important;
  }

  .me-sm-n15 {
    margin-right: -0.9375rem !important;
  }

  .me-sm-n20 {
    margin-right: -1.25rem !important;
  }

  .me-sm-n25 {
    margin-right: -1.5625rem !important;
  }

  .me-sm-n30 {
    margin-right: -1.875rem !important;
  }

  .me-sm-n40 {
    margin-right: -2.5rem !important;
  }

  .me-sm-n50 {
    margin-right: -3.125rem !important;
  }

  .me-sm-n70 {
    margin-right: -4.375rem !important;
  }

  .me-sm-n80 {
    margin-right: -5rem !important;
  }

  .me-sm-n100 {
    margin-right: -6.25rem !important;
  }

  .me-sm-n135 {
    margin-right: -8.4375rem !important;
  }

  .me-sm-n150 {
    margin-right: -9.375rem !important;
  }

  .mb-sm-n5 {
    margin-bottom: -0.3125rem !important;
  }

  .mb-sm-n10 {
    margin-bottom: -0.625rem !important;
  }

  .mb-sm-n15 {
    margin-bottom: -0.9375rem !important;
  }

  .mb-sm-n20 {
    margin-bottom: -1.25rem !important;
  }

  .mb-sm-n25 {
    margin-bottom: -1.5625rem !important;
  }

  .mb-sm-n30 {
    margin-bottom: -1.875rem !important;
  }

  .mb-sm-n40 {
    margin-bottom: -2.5rem !important;
  }

  .mb-sm-n50 {
    margin-bottom: -3.125rem !important;
  }

  .mb-sm-n70 {
    margin-bottom: -4.375rem !important;
  }

  .mb-sm-n80 {
    margin-bottom: -5rem !important;
  }

  .mb-sm-n100 {
    margin-bottom: -6.25rem !important;
  }

  .mb-sm-n135 {
    margin-bottom: -8.4375rem !important;
  }

  .mb-sm-n150 {
    margin-bottom: -9.375rem !important;
  }

  .ms-sm-n5 {
    margin-left: -0.3125rem !important;
  }

  .ms-sm-n10 {
    margin-left: -0.625rem !important;
  }

  .ms-sm-n15 {
    margin-left: -0.9375rem !important;
  }

  .ms-sm-n20 {
    margin-left: -1.25rem !important;
  }

  .ms-sm-n25 {
    margin-left: -1.5625rem !important;
  }

  .ms-sm-n30 {
    margin-left: -1.875rem !important;
  }

  .ms-sm-n40 {
    margin-left: -2.5rem !important;
  }

  .ms-sm-n50 {
    margin-left: -3.125rem !important;
  }

  .ms-sm-n70 {
    margin-left: -4.375rem !important;
  }

  .ms-sm-n80 {
    margin-left: -5rem !important;
  }

  .ms-sm-n100 {
    margin-left: -6.25rem !important;
  }

  .ms-sm-n135 {
    margin-left: -8.4375rem !important;
  }

  .ms-sm-n150 {
    margin-left: -9.375rem !important;
  }

  .p-sm-0 {
    padding: 0 !important;
  }

  .p-sm-5 {
    padding: 0.3125rem !important;
  }

  .p-sm-10 {
    padding: 0.625rem !important;
  }

  .p-sm-15 {
    padding: 0.9375rem !important;
  }

  .p-sm-20 {
    padding: 1.25rem !important;
  }

  .p-sm-25 {
    padding: 1.5625rem !important;
  }

  .p-sm-30 {
    padding: 1.875rem !important;
  }

  .p-sm-40 {
    padding: 2.5rem !important;
  }

  .p-sm-50 {
    padding: 3.125rem !important;
  }

  .p-sm-70 {
    padding: 4.375rem !important;
  }

  .p-sm-80 {
    padding: 5rem !important;
  }

  .p-sm-100 {
    padding: 6.25rem !important;
  }

  .p-sm-135 {
    padding: 8.4375rem !important;
  }

  .p-sm-150 {
    padding: 9.375rem !important;
  }

  .px-sm-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }

  .px-sm-5 {
    padding-right: 0.3125rem !important;
    padding-left: 0.3125rem !important;
  }

  .px-sm-10 {
    padding-right: 0.625rem !important;
    padding-left: 0.625rem !important;
  }

  .px-sm-15 {
    padding-right: 0.9375rem !important;
    padding-left: 0.9375rem !important;
  }

  .px-sm-20 {
    padding-right: 1.25rem !important;
    padding-left: 1.25rem !important;
  }

  .px-sm-25 {
    padding-right: 1.5625rem !important;
    padding-left: 1.5625rem !important;
  }

  .px-sm-30 {
    padding-right: 1.875rem !important;
    padding-left: 1.875rem !important;
  }

  .px-sm-40 {
    padding-right: 2.5rem !important;
    padding-left: 2.5rem !important;
  }

  .px-sm-50 {
    padding-right: 3.125rem !important;
    padding-left: 3.125rem !important;
  }

  .px-sm-70 {
    padding-right: 4.375rem !important;
    padding-left: 4.375rem !important;
  }

  .px-sm-80 {
    padding-right: 5rem !important;
    padding-left: 5rem !important;
  }

  .px-sm-100 {
    padding-right: 6.25rem !important;
    padding-left: 6.25rem !important;
  }

  .px-sm-135 {
    padding-right: 8.4375rem !important;
    padding-left: 8.4375rem !important;
  }

  .px-sm-150 {
    padding-right: 9.375rem !important;
    padding-left: 9.375rem !important;
  }

  .py-sm-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  .py-sm-5 {
    padding-top: 0.3125rem !important;
    padding-bottom: 0.3125rem !important;
  }

  .py-sm-10 {
    padding-top: 0.625rem !important;
    padding-bottom: 0.625rem !important;
  }

  .py-sm-15 {
    padding-top: 0.9375rem !important;
    padding-bottom: 0.9375rem !important;
  }

  .py-sm-20 {
    padding-top: 1.25rem !important;
    padding-bottom: 1.25rem !important;
  }

  .py-sm-25 {
    padding-top: 1.5625rem !important;
    padding-bottom: 1.5625rem !important;
  }

  .py-sm-30 {
    padding-top: 1.875rem !important;
    padding-bottom: 1.875rem !important;
  }

  .py-sm-40 {
    padding-top: 2.5rem !important;
    padding-bottom: 2.5rem !important;
  }

  .py-sm-50 {
    padding-top: 3.125rem !important;
    padding-bottom: 3.125rem !important;
  }

  .py-sm-70 {
    padding-top: 4.375rem !important;
    padding-bottom: 4.375rem !important;
  }

  .py-sm-80 {
    padding-top: 5rem !important;
    padding-bottom: 5rem !important;
  }

  .py-sm-100 {
    padding-top: 6.25rem !important;
    padding-bottom: 6.25rem !important;
  }

  .py-sm-135 {
    padding-top: 8.4375rem !important;
    padding-bottom: 8.4375rem !important;
  }

  .py-sm-150 {
    padding-top: 9.375rem !important;
    padding-bottom: 9.375rem !important;
  }

  .pt-sm-0 {
    padding-top: 0 !important;
  }

  .pt-sm-5 {
    padding-top: 0.3125rem !important;
  }

  .pt-sm-10 {
    padding-top: 0.625rem !important;
  }

  .pt-sm-15 {
    padding-top: 0.9375rem !important;
  }

  .pt-sm-20 {
    padding-top: 1.25rem !important;
  }

  .pt-sm-25 {
    padding-top: 1.5625rem !important;
  }

  .pt-sm-30 {
    padding-top: 1.875rem !important;
  }

  .pt-sm-40 {
    padding-top: 2.5rem !important;
  }

  .pt-sm-50 {
    padding-top: 3.125rem !important;
  }

  .pt-sm-70 {
    padding-top: 4.375rem !important;
  }

  .pt-sm-80 {
    padding-top: 5rem !important;
  }

  .pt-sm-100 {
    padding-top: 6.25rem !important;
  }

  .pt-sm-135 {
    padding-top: 8.4375rem !important;
  }

  .pt-sm-150 {
    padding-top: 9.375rem !important;
  }

  .pe-sm-0 {
    padding-right: 0 !important;
  }

  .pe-sm-5 {
    padding-right: 0.3125rem !important;
  }

  .pe-sm-10 {
    padding-right: 0.625rem !important;
  }

  .pe-sm-15 {
    padding-right: 0.9375rem !important;
  }

  .pe-sm-20 {
    padding-right: 1.25rem !important;
  }

  .pe-sm-25 {
    padding-right: 1.5625rem !important;
  }

  .pe-sm-30 {
    padding-right: 1.875rem !important;
  }

  .pe-sm-40 {
    padding-right: 2.5rem !important;
  }

  .pe-sm-50 {
    padding-right: 3.125rem !important;
  }

  .pe-sm-70 {
    padding-right: 4.375rem !important;
  }

  .pe-sm-80 {
    padding-right: 5rem !important;
  }

  .pe-sm-100 {
    padding-right: 6.25rem !important;
  }

  .pe-sm-135 {
    padding-right: 8.4375rem !important;
  }

  .pe-sm-150 {
    padding-right: 9.375rem !important;
  }

  .pb-sm-0 {
    padding-bottom: 0 !important;
  }

  .pb-sm-5 {
    padding-bottom: 0.3125rem !important;
  }

  .pb-sm-10 {
    padding-bottom: 0.625rem !important;
  }

  .pb-sm-15 {
    padding-bottom: 0.9375rem !important;
  }

  .pb-sm-20 {
    padding-bottom: 1.25rem !important;
  }

  .pb-sm-25 {
    padding-bottom: 1.5625rem !important;
  }

  .pb-sm-30 {
    padding-bottom: 1.875rem !important;
  }

  .pb-sm-40 {
    padding-bottom: 2.5rem !important;
  }

  .pb-sm-50 {
    padding-bottom: 3.125rem !important;
  }

  .pb-sm-70 {
    padding-bottom: 4.375rem !important;
  }

  .pb-sm-80 {
    padding-bottom: 5rem !important;
  }

  .pb-sm-100 {
    padding-bottom: 6.25rem !important;
  }

  .pb-sm-135 {
    padding-bottom: 8.4375rem !important;
  }

  .pb-sm-150 {
    padding-bottom: 9.375rem !important;
  }

  .ps-sm-0 {
    padding-left: 0 !important;
  }

  .ps-sm-5 {
    padding-left: 0.3125rem !important;
  }

  .ps-sm-10 {
    padding-left: 0.625rem !important;
  }

  .ps-sm-15 {
    padding-left: 0.9375rem !important;
  }

  .ps-sm-20 {
    padding-left: 1.25rem !important;
  }

  .ps-sm-25 {
    padding-left: 1.5625rem !important;
  }

  .ps-sm-30 {
    padding-left: 1.875rem !important;
  }

  .ps-sm-40 {
    padding-left: 2.5rem !important;
  }

  .ps-sm-50 {
    padding-left: 3.125rem !important;
  }

  .ps-sm-70 {
    padding-left: 4.375rem !important;
  }

  .ps-sm-80 {
    padding-left: 5rem !important;
  }

  .ps-sm-100 {
    padding-left: 6.25rem !important;
  }

  .ps-sm-135 {
    padding-left: 8.4375rem !important;
  }

  .ps-sm-150 {
    padding-left: 9.375rem !important;
  }

  .gap-sm-0 {
    gap: 0 !important;
  }

  .gap-sm-5 {
    gap: 0.3125rem !important;
  }

  .gap-sm-10 {
    gap: 0.625rem !important;
  }

  .gap-sm-15 {
    gap: 0.9375rem !important;
  }

  .gap-sm-20 {
    gap: 1.25rem !important;
  }

  .gap-sm-25 {
    gap: 1.5625rem !important;
  }

  .gap-sm-30 {
    gap: 1.875rem !important;
  }

  .gap-sm-40 {
    gap: 2.5rem !important;
  }

  .gap-sm-50 {
    gap: 3.125rem !important;
  }

  .gap-sm-70 {
    gap: 4.375rem !important;
  }

  .gap-sm-80 {
    gap: 5rem !important;
  }

  .gap-sm-100 {
    gap: 6.25rem !important;
  }

  .gap-sm-135 {
    gap: 8.4375rem !important;
  }

  .gap-sm-150 {
    gap: 9.375rem !important;
  }

  .row-gap-sm-0 {
    row-gap: 0 !important;
  }

  .row-gap-sm-5 {
    row-gap: 0.3125rem !important;
  }

  .row-gap-sm-10 {
    row-gap: 0.625rem !important;
  }

  .row-gap-sm-15 {
    row-gap: 0.9375rem !important;
  }

  .row-gap-sm-20 {
    row-gap: 1.25rem !important;
  }

  .row-gap-sm-25 {
    row-gap: 1.5625rem !important;
  }

  .row-gap-sm-30 {
    row-gap: 1.875rem !important;
  }

  .row-gap-sm-40 {
    row-gap: 2.5rem !important;
  }

  .row-gap-sm-50 {
    row-gap: 3.125rem !important;
  }

  .row-gap-sm-70 {
    row-gap: 4.375rem !important;
  }

  .row-gap-sm-80 {
    row-gap: 5rem !important;
  }

  .row-gap-sm-100 {
    row-gap: 6.25rem !important;
  }

  .row-gap-sm-135 {
    row-gap: 8.4375rem !important;
  }

  .row-gap-sm-150 {
    row-gap: 9.375rem !important;
  }

  .column-gap-sm-0 {
    column-gap: 0 !important;
  }

  .column-gap-sm-5 {
    column-gap: 0.3125rem !important;
  }

  .column-gap-sm-10 {
    column-gap: 0.625rem !important;
  }

  .column-gap-sm-15 {
    column-gap: 0.9375rem !important;
  }

  .column-gap-sm-20 {
    column-gap: 1.25rem !important;
  }

  .column-gap-sm-25 {
    column-gap: 1.5625rem !important;
  }

  .column-gap-sm-30 {
    column-gap: 1.875rem !important;
  }

  .column-gap-sm-40 {
    column-gap: 2.5rem !important;
  }

  .column-gap-sm-50 {
    column-gap: 3.125rem !important;
  }

  .column-gap-sm-70 {
    column-gap: 4.375rem !important;
  }

  .column-gap-sm-80 {
    column-gap: 5rem !important;
  }

  .column-gap-sm-100 {
    column-gap: 6.25rem !important;
  }

  .column-gap-sm-135 {
    column-gap: 8.4375rem !important;
  }

  .column-gap-sm-150 {
    column-gap: 9.375rem !important;
  }

  .text-sm-start {
    text-align: left !important;
  }

  .text-sm-end {
    text-align: right !important;
  }

  .text-sm-center {
    text-align: center !important;
  }
}
@media (min-width: 768px) {
  .float-md-start {
    float: left !important;
  }

  .float-md-end {
    float: right !important;
  }

  .float-md-none {
    float: none !important;
  }

  .object-fit-md-contain {
    object-fit: contain !important;
  }

  .object-fit-md-cover {
    object-fit: cover !important;
  }

  .object-fit-md-fill {
    object-fit: fill !important;
  }

  .object-fit-md-scale {
    object-fit: scale-down !important;
  }

  .object-fit-md-none {
    object-fit: none !important;
  }

  .d-md-inline {
    display: inline !important;
  }

  .d-md-inline-block {
    display: inline-block !important;
  }

  .d-md-block {
    display: block !important;
  }

  .d-md-grid {
    display: grid !important;
  }

  .d-md-inline-grid {
    display: inline-grid !important;
  }

  .d-md-table {
    display: table !important;
  }

  .d-md-table-row {
    display: table-row !important;
  }

  .d-md-table-cell {
    display: table-cell !important;
  }

  .d-md-flex {
    display: flex !important;
  }

  .d-md-inline-flex {
    display: inline-flex !important;
  }

  .d-md-none {
    display: none !important;
  }

  .flex-md-fill {
    flex: 1 1 auto !important;
  }

  .flex-md-row {
    flex-direction: row !important;
  }

  .flex-md-column {
    flex-direction: column !important;
  }

  .flex-md-row-reverse {
    flex-direction: row-reverse !important;
  }

  .flex-md-column-reverse {
    flex-direction: column-reverse !important;
  }

  .flex-md-grow-0 {
    flex-grow: 0 !important;
  }

  .flex-md-grow-1 {
    flex-grow: 1 !important;
  }

  .flex-md-shrink-0 {
    flex-shrink: 0 !important;
  }

  .flex-md-shrink-1 {
    flex-shrink: 1 !important;
  }

  .flex-md-wrap {
    flex-wrap: wrap !important;
  }

  .flex-md-nowrap {
    flex-wrap: nowrap !important;
  }

  .flex-md-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }

  .justify-content-md-start {
    justify-content: flex-start !important;
  }

  .justify-content-md-end {
    justify-content: flex-end !important;
  }

  .justify-content-md-center {
    justify-content: center !important;
  }

  .justify-content-md-between {
    justify-content: space-between !important;
  }

  .justify-content-md-around {
    justify-content: space-around !important;
  }

  .justify-content-md-evenly {
    justify-content: space-evenly !important;
  }

  .align-items-md-start {
    align-items: flex-start !important;
  }

  .align-items-md-end {
    align-items: flex-end !important;
  }

  .align-items-md-center {
    align-items: center !important;
  }

  .align-items-md-baseline {
    align-items: baseline !important;
  }

  .align-items-md-stretch {
    align-items: stretch !important;
  }

  .align-content-md-start {
    align-content: flex-start !important;
  }

  .align-content-md-end {
    align-content: flex-end !important;
  }

  .align-content-md-center {
    align-content: center !important;
  }

  .align-content-md-between {
    align-content: space-between !important;
  }

  .align-content-md-around {
    align-content: space-around !important;
  }

  .align-content-md-stretch {
    align-content: stretch !important;
  }

  .align-self-md-auto {
    align-self: auto !important;
  }

  .align-self-md-start {
    align-self: flex-start !important;
  }

  .align-self-md-end {
    align-self: flex-end !important;
  }

  .align-self-md-center {
    align-self: center !important;
  }

  .align-self-md-baseline {
    align-self: baseline !important;
  }

  .align-self-md-stretch {
    align-self: stretch !important;
  }

  .order-md-first {
    order: -1 !important;
  }

  .order-md-0 {
    order: 0 !important;
  }

  .order-md-1 {
    order: 1 !important;
  }

  .order-md-2 {
    order: 2 !important;
  }

  .order-md-3 {
    order: 3 !important;
  }

  .order-md-4 {
    order: 4 !important;
  }

  .order-md-5 {
    order: 5 !important;
  }

  .order-md-last {
    order: 6 !important;
  }

  .m-md-0 {
    margin: 0 !important;
  }

  .m-md-5 {
    margin: 0.3125rem !important;
  }

  .m-md-10 {
    margin: 0.625rem !important;
  }

  .m-md-15 {
    margin: 0.9375rem !important;
  }

  .m-md-20 {
    margin: 1.25rem !important;
  }

  .m-md-25 {
    margin: 1.5625rem !important;
  }

  .m-md-30 {
    margin: 1.875rem !important;
  }

  .m-md-40 {
    margin: 2.5rem !important;
  }

  .m-md-50 {
    margin: 3.125rem !important;
  }

  .m-md-70 {
    margin: 4.375rem !important;
  }

  .m-md-80 {
    margin: 5rem !important;
  }

  .m-md-100 {
    margin: 6.25rem !important;
  }

  .m-md-135 {
    margin: 8.4375rem !important;
  }

  .m-md-150 {
    margin: 9.375rem !important;
  }

  .m-md-auto {
    margin: auto !important;
  }

  .mx-md-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }

  .mx-md-5 {
    margin-right: 0.3125rem !important;
    margin-left: 0.3125rem !important;
  }

  .mx-md-10 {
    margin-right: 0.625rem !important;
    margin-left: 0.625rem !important;
  }

  .mx-md-15 {
    margin-right: 0.9375rem !important;
    margin-left: 0.9375rem !important;
  }

  .mx-md-20 {
    margin-right: 1.25rem !important;
    margin-left: 1.25rem !important;
  }

  .mx-md-25 {
    margin-right: 1.5625rem !important;
    margin-left: 1.5625rem !important;
  }

  .mx-md-30 {
    margin-right: 1.875rem !important;
    margin-left: 1.875rem !important;
  }

  .mx-md-40 {
    margin-right: 2.5rem !important;
    margin-left: 2.5rem !important;
  }

  .mx-md-50 {
    margin-right: 3.125rem !important;
    margin-left: 3.125rem !important;
  }

  .mx-md-70 {
    margin-right: 4.375rem !important;
    margin-left: 4.375rem !important;
  }

  .mx-md-80 {
    margin-right: 5rem !important;
    margin-left: 5rem !important;
  }

  .mx-md-100 {
    margin-right: 6.25rem !important;
    margin-left: 6.25rem !important;
  }

  .mx-md-135 {
    margin-right: 8.4375rem !important;
    margin-left: 8.4375rem !important;
  }

  .mx-md-150 {
    margin-right: 9.375rem !important;
    margin-left: 9.375rem !important;
  }

  .mx-md-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }

  .my-md-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }

  .my-md-5 {
    margin-top: 0.3125rem !important;
    margin-bottom: 0.3125rem !important;
  }

  .my-md-10 {
    margin-top: 0.625rem !important;
    margin-bottom: 0.625rem !important;
  }

  .my-md-15 {
    margin-top: 0.9375rem !important;
    margin-bottom: 0.9375rem !important;
  }

  .my-md-20 {
    margin-top: 1.25rem !important;
    margin-bottom: 1.25rem !important;
  }

  .my-md-25 {
    margin-top: 1.5625rem !important;
    margin-bottom: 1.5625rem !important;
  }

  .my-md-30 {
    margin-top: 1.875rem !important;
    margin-bottom: 1.875rem !important;
  }

  .my-md-40 {
    margin-top: 2.5rem !important;
    margin-bottom: 2.5rem !important;
  }

  .my-md-50 {
    margin-top: 3.125rem !important;
    margin-bottom: 3.125rem !important;
  }

  .my-md-70 {
    margin-top: 4.375rem !important;
    margin-bottom: 4.375rem !important;
  }

  .my-md-80 {
    margin-top: 5rem !important;
    margin-bottom: 5rem !important;
  }

  .my-md-100 {
    margin-top: 6.25rem !important;
    margin-bottom: 6.25rem !important;
  }

  .my-md-135 {
    margin-top: 8.4375rem !important;
    margin-bottom: 8.4375rem !important;
  }

  .my-md-150 {
    margin-top: 9.375rem !important;
    margin-bottom: 9.375rem !important;
  }

  .my-md-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }

  .mt-md-0 {
    margin-top: 0 !important;
  }

  .mt-md-5 {
    margin-top: 0.3125rem !important;
  }

  .mt-md-10 {
    margin-top: 0.625rem !important;
  }

  .mt-md-15 {
    margin-top: 0.9375rem !important;
  }

  .mt-md-20 {
    margin-top: 1.25rem !important;
  }

  .mt-md-25 {
    margin-top: 1.5625rem !important;
  }

  .mt-md-30 {
    margin-top: 1.875rem !important;
  }

  .mt-md-40 {
    margin-top: 2.5rem !important;
  }

  .mt-md-50 {
    margin-top: 3.125rem !important;
  }

  .mt-md-70 {
    margin-top: 4.375rem !important;
  }

  .mt-md-80 {
    margin-top: 5rem !important;
  }

  .mt-md-100 {
    margin-top: 6.25rem !important;
  }

  .mt-md-135 {
    margin-top: 8.4375rem !important;
  }

  .mt-md-150 {
    margin-top: 9.375rem !important;
  }

  .mt-md-auto {
    margin-top: auto !important;
  }

  .me-md-0 {
    margin-right: 0 !important;
  }

  .me-md-5 {
    margin-right: 0.3125rem !important;
  }

  .me-md-10 {
    margin-right: 0.625rem !important;
  }

  .me-md-15 {
    margin-right: 0.9375rem !important;
  }

  .me-md-20 {
    margin-right: 1.25rem !important;
  }

  .me-md-25 {
    margin-right: 1.5625rem !important;
  }

  .me-md-30 {
    margin-right: 1.875rem !important;
  }

  .me-md-40 {
    margin-right: 2.5rem !important;
  }

  .me-md-50 {
    margin-right: 3.125rem !important;
  }

  .me-md-70 {
    margin-right: 4.375rem !important;
  }

  .me-md-80 {
    margin-right: 5rem !important;
  }

  .me-md-100 {
    margin-right: 6.25rem !important;
  }

  .me-md-135 {
    margin-right: 8.4375rem !important;
  }

  .me-md-150 {
    margin-right: 9.375rem !important;
  }

  .me-md-auto {
    margin-right: auto !important;
  }

  .mb-md-0 {
    margin-bottom: 0 !important;
  }

  .mb-md-5 {
    margin-bottom: 0.3125rem !important;
  }

  .mb-md-10 {
    margin-bottom: 0.625rem !important;
  }

  .mb-md-15 {
    margin-bottom: 0.9375rem !important;
  }

  .mb-md-20 {
    margin-bottom: 1.25rem !important;
  }

  .mb-md-25 {
    margin-bottom: 1.5625rem !important;
  }

  .mb-md-30 {
    margin-bottom: 1.875rem !important;
  }

  .mb-md-40 {
    margin-bottom: 2.5rem !important;
  }

  .mb-md-50 {
    margin-bottom: 3.125rem !important;
  }

  .mb-md-70 {
    margin-bottom: 4.375rem !important;
  }

  .mb-md-80 {
    margin-bottom: 5rem !important;
  }

  .mb-md-100 {
    margin-bottom: 6.25rem !important;
  }

  .mb-md-135 {
    margin-bottom: 8.4375rem !important;
  }

  .mb-md-150 {
    margin-bottom: 9.375rem !important;
  }

  .mb-md-auto {
    margin-bottom: auto !important;
  }

  .ms-md-0 {
    margin-left: 0 !important;
  }

  .ms-md-5 {
    margin-left: 0.3125rem !important;
  }

  .ms-md-10 {
    margin-left: 0.625rem !important;
  }

  .ms-md-15 {
    margin-left: 0.9375rem !important;
  }

  .ms-md-20 {
    margin-left: 1.25rem !important;
  }

  .ms-md-25 {
    margin-left: 1.5625rem !important;
  }

  .ms-md-30 {
    margin-left: 1.875rem !important;
  }

  .ms-md-40 {
    margin-left: 2.5rem !important;
  }

  .ms-md-50 {
    margin-left: 3.125rem !important;
  }

  .ms-md-70 {
    margin-left: 4.375rem !important;
  }

  .ms-md-80 {
    margin-left: 5rem !important;
  }

  .ms-md-100 {
    margin-left: 6.25rem !important;
  }

  .ms-md-135 {
    margin-left: 8.4375rem !important;
  }

  .ms-md-150 {
    margin-left: 9.375rem !important;
  }

  .ms-md-auto {
    margin-left: auto !important;
  }

  .m-md-n5 {
    margin: -0.3125rem !important;
  }

  .m-md-n10 {
    margin: -0.625rem !important;
  }

  .m-md-n15 {
    margin: -0.9375rem !important;
  }

  .m-md-n20 {
    margin: -1.25rem !important;
  }

  .m-md-n25 {
    margin: -1.5625rem !important;
  }

  .m-md-n30 {
    margin: -1.875rem !important;
  }

  .m-md-n40 {
    margin: -2.5rem !important;
  }

  .m-md-n50 {
    margin: -3.125rem !important;
  }

  .m-md-n70 {
    margin: -4.375rem !important;
  }

  .m-md-n80 {
    margin: -5rem !important;
  }

  .m-md-n100 {
    margin: -6.25rem !important;
  }

  .m-md-n135 {
    margin: -8.4375rem !important;
  }

  .m-md-n150 {
    margin: -9.375rem !important;
  }

  .mx-md-n5 {
    margin-right: -0.3125rem !important;
    margin-left: -0.3125rem !important;
  }

  .mx-md-n10 {
    margin-right: -0.625rem !important;
    margin-left: -0.625rem !important;
  }

  .mx-md-n15 {
    margin-right: -0.9375rem !important;
    margin-left: -0.9375rem !important;
  }

  .mx-md-n20 {
    margin-right: -1.25rem !important;
    margin-left: -1.25rem !important;
  }

  .mx-md-n25 {
    margin-right: -1.5625rem !important;
    margin-left: -1.5625rem !important;
  }

  .mx-md-n30 {
    margin-right: -1.875rem !important;
    margin-left: -1.875rem !important;
  }

  .mx-md-n40 {
    margin-right: -2.5rem !important;
    margin-left: -2.5rem !important;
  }

  .mx-md-n50 {
    margin-right: -3.125rem !important;
    margin-left: -3.125rem !important;
  }

  .mx-md-n70 {
    margin-right: -4.375rem !important;
    margin-left: -4.375rem !important;
  }

  .mx-md-n80 {
    margin-right: -5rem !important;
    margin-left: -5rem !important;
  }

  .mx-md-n100 {
    margin-right: -6.25rem !important;
    margin-left: -6.25rem !important;
  }

  .mx-md-n135 {
    margin-right: -8.4375rem !important;
    margin-left: -8.4375rem !important;
  }

  .mx-md-n150 {
    margin-right: -9.375rem !important;
    margin-left: -9.375rem !important;
  }

  .my-md-n5 {
    margin-top: -0.3125rem !important;
    margin-bottom: -0.3125rem !important;
  }

  .my-md-n10 {
    margin-top: -0.625rem !important;
    margin-bottom: -0.625rem !important;
  }

  .my-md-n15 {
    margin-top: -0.9375rem !important;
    margin-bottom: -0.9375rem !important;
  }

  .my-md-n20 {
    margin-top: -1.25rem !important;
    margin-bottom: -1.25rem !important;
  }

  .my-md-n25 {
    margin-top: -1.5625rem !important;
    margin-bottom: -1.5625rem !important;
  }

  .my-md-n30 {
    margin-top: -1.875rem !important;
    margin-bottom: -1.875rem !important;
  }

  .my-md-n40 {
    margin-top: -2.5rem !important;
    margin-bottom: -2.5rem !important;
  }

  .my-md-n50 {
    margin-top: -3.125rem !important;
    margin-bottom: -3.125rem !important;
  }

  .my-md-n70 {
    margin-top: -4.375rem !important;
    margin-bottom: -4.375rem !important;
  }

  .my-md-n80 {
    margin-top: -5rem !important;
    margin-bottom: -5rem !important;
  }

  .my-md-n100 {
    margin-top: -6.25rem !important;
    margin-bottom: -6.25rem !important;
  }

  .my-md-n135 {
    margin-top: -8.4375rem !important;
    margin-bottom: -8.4375rem !important;
  }

  .my-md-n150 {
    margin-top: -9.375rem !important;
    margin-bottom: -9.375rem !important;
  }

  .mt-md-n5 {
    margin-top: -0.3125rem !important;
  }

  .mt-md-n10 {
    margin-top: -0.625rem !important;
  }

  .mt-md-n15 {
    margin-top: -0.9375rem !important;
  }

  .mt-md-n20 {
    margin-top: -1.25rem !important;
  }

  .mt-md-n25 {
    margin-top: -1.5625rem !important;
  }

  .mt-md-n30 {
    margin-top: -1.875rem !important;
  }

  .mt-md-n40 {
    margin-top: -2.5rem !important;
  }

  .mt-md-n50 {
    margin-top: -3.125rem !important;
  }

  .mt-md-n70 {
    margin-top: -4.375rem !important;
  }

  .mt-md-n80 {
    margin-top: -5rem !important;
  }

  .mt-md-n100 {
    margin-top: -6.25rem !important;
  }

  .mt-md-n135 {
    margin-top: -8.4375rem !important;
  }

  .mt-md-n150 {
    margin-top: -9.375rem !important;
  }

  .me-md-n5 {
    margin-right: -0.3125rem !important;
  }

  .me-md-n10 {
    margin-right: -0.625rem !important;
  }

  .me-md-n15 {
    margin-right: -0.9375rem !important;
  }

  .me-md-n20 {
    margin-right: -1.25rem !important;
  }

  .me-md-n25 {
    margin-right: -1.5625rem !important;
  }

  .me-md-n30 {
    margin-right: -1.875rem !important;
  }

  .me-md-n40 {
    margin-right: -2.5rem !important;
  }

  .me-md-n50 {
    margin-right: -3.125rem !important;
  }

  .me-md-n70 {
    margin-right: -4.375rem !important;
  }

  .me-md-n80 {
    margin-right: -5rem !important;
  }

  .me-md-n100 {
    margin-right: -6.25rem !important;
  }

  .me-md-n135 {
    margin-right: -8.4375rem !important;
  }

  .me-md-n150 {
    margin-right: -9.375rem !important;
  }

  .mb-md-n5 {
    margin-bottom: -0.3125rem !important;
  }

  .mb-md-n10 {
    margin-bottom: -0.625rem !important;
  }

  .mb-md-n15 {
    margin-bottom: -0.9375rem !important;
  }

  .mb-md-n20 {
    margin-bottom: -1.25rem !important;
  }

  .mb-md-n25 {
    margin-bottom: -1.5625rem !important;
  }

  .mb-md-n30 {
    margin-bottom: -1.875rem !important;
  }

  .mb-md-n40 {
    margin-bottom: -2.5rem !important;
  }

  .mb-md-n50 {
    margin-bottom: -3.125rem !important;
  }

  .mb-md-n70 {
    margin-bottom: -4.375rem !important;
  }

  .mb-md-n80 {
    margin-bottom: -5rem !important;
  }

  .mb-md-n100 {
    margin-bottom: -6.25rem !important;
  }

  .mb-md-n135 {
    margin-bottom: -8.4375rem !important;
  }

  .mb-md-n150 {
    margin-bottom: -9.375rem !important;
  }

  .ms-md-n5 {
    margin-left: -0.3125rem !important;
  }

  .ms-md-n10 {
    margin-left: -0.625rem !important;
  }

  .ms-md-n15 {
    margin-left: -0.9375rem !important;
  }

  .ms-md-n20 {
    margin-left: -1.25rem !important;
  }

  .ms-md-n25 {
    margin-left: -1.5625rem !important;
  }

  .ms-md-n30 {
    margin-left: -1.875rem !important;
  }

  .ms-md-n40 {
    margin-left: -2.5rem !important;
  }

  .ms-md-n50 {
    margin-left: -3.125rem !important;
  }

  .ms-md-n70 {
    margin-left: -4.375rem !important;
  }

  .ms-md-n80 {
    margin-left: -5rem !important;
  }

  .ms-md-n100 {
    margin-left: -6.25rem !important;
  }

  .ms-md-n135 {
    margin-left: -8.4375rem !important;
  }

  .ms-md-n150 {
    margin-left: -9.375rem !important;
  }

  .p-md-0 {
    padding: 0 !important;
  }

  .p-md-5 {
    padding: 0.3125rem !important;
  }

  .p-md-10 {
    padding: 0.625rem !important;
  }

  .p-md-15 {
    padding: 0.9375rem !important;
  }

  .p-md-20 {
    padding: 1.25rem !important;
  }

  .p-md-25 {
    padding: 1.5625rem !important;
  }

  .p-md-30 {
    padding: 1.875rem !important;
  }

  .p-md-40 {
    padding: 2.5rem !important;
  }

  .p-md-50 {
    padding: 3.125rem !important;
  }

  .p-md-70 {
    padding: 4.375rem !important;
  }

  .p-md-80 {
    padding: 5rem !important;
  }

  .p-md-100 {
    padding: 6.25rem !important;
  }

  .p-md-135 {
    padding: 8.4375rem !important;
  }

  .p-md-150 {
    padding: 9.375rem !important;
  }

  .px-md-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }

  .px-md-5 {
    padding-right: 0.3125rem !important;
    padding-left: 0.3125rem !important;
  }

  .px-md-10 {
    padding-right: 0.625rem !important;
    padding-left: 0.625rem !important;
  }

  .px-md-15 {
    padding-right: 0.9375rem !important;
    padding-left: 0.9375rem !important;
  }

  .px-md-20 {
    padding-right: 1.25rem !important;
    padding-left: 1.25rem !important;
  }

  .px-md-25 {
    padding-right: 1.5625rem !important;
    padding-left: 1.5625rem !important;
  }

  .px-md-30 {
    padding-right: 1.875rem !important;
    padding-left: 1.875rem !important;
  }

  .px-md-40 {
    padding-right: 2.5rem !important;
    padding-left: 2.5rem !important;
  }

  .px-md-50 {
    padding-right: 3.125rem !important;
    padding-left: 3.125rem !important;
  }

  .px-md-70 {
    padding-right: 4.375rem !important;
    padding-left: 4.375rem !important;
  }

  .px-md-80 {
    padding-right: 5rem !important;
    padding-left: 5rem !important;
  }

  .px-md-100 {
    padding-right: 6.25rem !important;
    padding-left: 6.25rem !important;
  }

  .px-md-135 {
    padding-right: 8.4375rem !important;
    padding-left: 8.4375rem !important;
  }

  .px-md-150 {
    padding-right: 9.375rem !important;
    padding-left: 9.375rem !important;
  }

  .py-md-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  .py-md-5 {
    padding-top: 0.3125rem !important;
    padding-bottom: 0.3125rem !important;
  }

  .py-md-10 {
    padding-top: 0.625rem !important;
    padding-bottom: 0.625rem !important;
  }

  .py-md-15 {
    padding-top: 0.9375rem !important;
    padding-bottom: 0.9375rem !important;
  }

  .py-md-20 {
    padding-top: 1.25rem !important;
    padding-bottom: 1.25rem !important;
  }

  .py-md-25 {
    padding-top: 1.5625rem !important;
    padding-bottom: 1.5625rem !important;
  }

  .py-md-30 {
    padding-top: 1.875rem !important;
    padding-bottom: 1.875rem !important;
  }

  .py-md-40 {
    padding-top: 2.5rem !important;
    padding-bottom: 2.5rem !important;
  }

  .py-md-50 {
    padding-top: 3.125rem !important;
    padding-bottom: 3.125rem !important;
  }

  .py-md-70 {
    padding-top: 4.375rem !important;
    padding-bottom: 4.375rem !important;
  }

  .py-md-80 {
    padding-top: 5rem !important;
    padding-bottom: 5rem !important;
  }

  .py-md-100 {
    padding-top: 6.25rem !important;
    padding-bottom: 6.25rem !important;
  }

  .py-md-135 {
    padding-top: 8.4375rem !important;
    padding-bottom: 8.4375rem !important;
  }

  .py-md-150 {
    padding-top: 9.375rem !important;
    padding-bottom: 9.375rem !important;
  }

  .pt-md-0 {
    padding-top: 0 !important;
  }

  .pt-md-5 {
    padding-top: 0.3125rem !important;
  }

  .pt-md-10 {
    padding-top: 0.625rem !important;
  }

  .pt-md-15 {
    padding-top: 0.9375rem !important;
  }

  .pt-md-20 {
    padding-top: 1.25rem !important;
  }

  .pt-md-25 {
    padding-top: 1.5625rem !important;
  }

  .pt-md-30 {
    padding-top: 1.875rem !important;
  }

  .pt-md-40 {
    padding-top: 2.5rem !important;
  }

  .pt-md-50 {
    padding-top: 3.125rem !important;
  }

  .pt-md-70 {
    padding-top: 4.375rem !important;
  }

  .pt-md-80 {
    padding-top: 5rem !important;
  }

  .pt-md-100 {
    padding-top: 6.25rem !important;
  }

  .pt-md-135 {
    padding-top: 8.4375rem !important;
  }

  .pt-md-150 {
    padding-top: 9.375rem !important;
  }

  .pe-md-0 {
    padding-right: 0 !important;
  }

  .pe-md-5 {
    padding-right: 0.3125rem !important;
  }

  .pe-md-10 {
    padding-right: 0.625rem !important;
  }

  .pe-md-15 {
    padding-right: 0.9375rem !important;
  }

  .pe-md-20 {
    padding-right: 1.25rem !important;
  }

  .pe-md-25 {
    padding-right: 1.5625rem !important;
  }

  .pe-md-30 {
    padding-right: 1.875rem !important;
  }

  .pe-md-40 {
    padding-right: 2.5rem !important;
  }

  .pe-md-50 {
    padding-right: 3.125rem !important;
  }

  .pe-md-70 {
    padding-right: 4.375rem !important;
  }

  .pe-md-80 {
    padding-right: 5rem !important;
  }

  .pe-md-100 {
    padding-right: 6.25rem !important;
  }

  .pe-md-135 {
    padding-right: 8.4375rem !important;
  }

  .pe-md-150 {
    padding-right: 9.375rem !important;
  }

  .pb-md-0 {
    padding-bottom: 0 !important;
  }

  .pb-md-5 {
    padding-bottom: 0.3125rem !important;
  }

  .pb-md-10 {
    padding-bottom: 0.625rem !important;
  }

  .pb-md-15 {
    padding-bottom: 0.9375rem !important;
  }

  .pb-md-20 {
    padding-bottom: 1.25rem !important;
  }

  .pb-md-25 {
    padding-bottom: 1.5625rem !important;
  }

  .pb-md-30 {
    padding-bottom: 1.875rem !important;
  }

  .pb-md-40 {
    padding-bottom: 2.5rem !important;
  }

  .pb-md-50 {
    padding-bottom: 3.125rem !important;
  }

  .pb-md-70 {
    padding-bottom: 4.375rem !important;
  }

  .pb-md-80 {
    padding-bottom: 5rem !important;
  }

  .pb-md-100 {
    padding-bottom: 6.25rem !important;
  }

  .pb-md-135 {
    padding-bottom: 8.4375rem !important;
  }

  .pb-md-150 {
    padding-bottom: 9.375rem !important;
  }

  .ps-md-0 {
    padding-left: 0 !important;
  }

  .ps-md-5 {
    padding-left: 0.3125rem !important;
  }

  .ps-md-10 {
    padding-left: 0.625rem !important;
  }

  .ps-md-15 {
    padding-left: 0.9375rem !important;
  }

  .ps-md-20 {
    padding-left: 1.25rem !important;
  }

  .ps-md-25 {
    padding-left: 1.5625rem !important;
  }

  .ps-md-30 {
    padding-left: 1.875rem !important;
  }

  .ps-md-40 {
    padding-left: 2.5rem !important;
  }

  .ps-md-50 {
    padding-left: 3.125rem !important;
  }

  .ps-md-70 {
    padding-left: 4.375rem !important;
  }

  .ps-md-80 {
    padding-left: 5rem !important;
  }

  .ps-md-100 {
    padding-left: 6.25rem !important;
  }

  .ps-md-135 {
    padding-left: 8.4375rem !important;
  }

  .ps-md-150 {
    padding-left: 9.375rem !important;
  }

  .gap-md-0 {
    gap: 0 !important;
  }

  .gap-md-5 {
    gap: 0.3125rem !important;
  }

  .gap-md-10 {
    gap: 0.625rem !important;
  }

  .gap-md-15 {
    gap: 0.9375rem !important;
  }

  .gap-md-20 {
    gap: 1.25rem !important;
  }

  .gap-md-25 {
    gap: 1.5625rem !important;
  }

  .gap-md-30 {
    gap: 1.875rem !important;
  }

  .gap-md-40 {
    gap: 2.5rem !important;
  }

  .gap-md-50 {
    gap: 3.125rem !important;
  }

  .gap-md-70 {
    gap: 4.375rem !important;
  }

  .gap-md-80 {
    gap: 5rem !important;
  }

  .gap-md-100 {
    gap: 6.25rem !important;
  }

  .gap-md-135 {
    gap: 8.4375rem !important;
  }

  .gap-md-150 {
    gap: 9.375rem !important;
  }

  .row-gap-md-0 {
    row-gap: 0 !important;
  }

  .row-gap-md-5 {
    row-gap: 0.3125rem !important;
  }

  .row-gap-md-10 {
    row-gap: 0.625rem !important;
  }

  .row-gap-md-15 {
    row-gap: 0.9375rem !important;
  }

  .row-gap-md-20 {
    row-gap: 1.25rem !important;
  }

  .row-gap-md-25 {
    row-gap: 1.5625rem !important;
  }

  .row-gap-md-30 {
    row-gap: 1.875rem !important;
  }

  .row-gap-md-40 {
    row-gap: 2.5rem !important;
  }

  .row-gap-md-50 {
    row-gap: 3.125rem !important;
  }

  .row-gap-md-70 {
    row-gap: 4.375rem !important;
  }

  .row-gap-md-80 {
    row-gap: 5rem !important;
  }

  .row-gap-md-100 {
    row-gap: 6.25rem !important;
  }

  .row-gap-md-135 {
    row-gap: 8.4375rem !important;
  }

  .row-gap-md-150 {
    row-gap: 9.375rem !important;
  }

  .column-gap-md-0 {
    column-gap: 0 !important;
  }

  .column-gap-md-5 {
    column-gap: 0.3125rem !important;
  }

  .column-gap-md-10 {
    column-gap: 0.625rem !important;
  }

  .column-gap-md-15 {
    column-gap: 0.9375rem !important;
  }

  .column-gap-md-20 {
    column-gap: 1.25rem !important;
  }

  .column-gap-md-25 {
    column-gap: 1.5625rem !important;
  }

  .column-gap-md-30 {
    column-gap: 1.875rem !important;
  }

  .column-gap-md-40 {
    column-gap: 2.5rem !important;
  }

  .column-gap-md-50 {
    column-gap: 3.125rem !important;
  }

  .column-gap-md-70 {
    column-gap: 4.375rem !important;
  }

  .column-gap-md-80 {
    column-gap: 5rem !important;
  }

  .column-gap-md-100 {
    column-gap: 6.25rem !important;
  }

  .column-gap-md-135 {
    column-gap: 8.4375rem !important;
  }

  .column-gap-md-150 {
    column-gap: 9.375rem !important;
  }

  .text-md-start {
    text-align: left !important;
  }

  .text-md-end {
    text-align: right !important;
  }

  .text-md-center {
    text-align: center !important;
  }
}
@media (min-width: 992px) {
  .float-lg-start {
    float: left !important;
  }

  .float-lg-end {
    float: right !important;
  }

  .float-lg-none {
    float: none !important;
  }

  .object-fit-lg-contain {
    object-fit: contain !important;
  }

  .object-fit-lg-cover {
    object-fit: cover !important;
  }

  .object-fit-lg-fill {
    object-fit: fill !important;
  }

  .object-fit-lg-scale {
    object-fit: scale-down !important;
  }

  .object-fit-lg-none {
    object-fit: none !important;
  }

  .d-lg-inline {
    display: inline !important;
  }

  .d-lg-inline-block {
    display: inline-block !important;
  }

  .d-lg-block {
    display: block !important;
  }

  .d-lg-grid {
    display: grid !important;
  }

  .d-lg-inline-grid {
    display: inline-grid !important;
  }

  .d-lg-table {
    display: table !important;
  }

  .d-lg-table-row {
    display: table-row !important;
  }

  .d-lg-table-cell {
    display: table-cell !important;
  }

  .d-lg-flex {
    display: flex !important;
  }

  .d-lg-inline-flex {
    display: inline-flex !important;
  }

  .d-lg-none {
    display: none !important;
  }

  .flex-lg-fill {
    flex: 1 1 auto !important;
  }

  .flex-lg-row {
    flex-direction: row !important;
  }

  .flex-lg-column {
    flex-direction: column !important;
  }

  .flex-lg-row-reverse {
    flex-direction: row-reverse !important;
  }

  .flex-lg-column-reverse {
    flex-direction: column-reverse !important;
  }

  .flex-lg-grow-0 {
    flex-grow: 0 !important;
  }

  .flex-lg-grow-1 {
    flex-grow: 1 !important;
  }

  .flex-lg-shrink-0 {
    flex-shrink: 0 !important;
  }

  .flex-lg-shrink-1 {
    flex-shrink: 1 !important;
  }

  .flex-lg-wrap {
    flex-wrap: wrap !important;
  }

  .flex-lg-nowrap {
    flex-wrap: nowrap !important;
  }

  .flex-lg-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }

  .justify-content-lg-start {
    justify-content: flex-start !important;
  }

  .justify-content-lg-end {
    justify-content: flex-end !important;
  }

  .justify-content-lg-center {
    justify-content: center !important;
  }

  .justify-content-lg-between {
    justify-content: space-between !important;
  }

  .justify-content-lg-around {
    justify-content: space-around !important;
  }

  .justify-content-lg-evenly {
    justify-content: space-evenly !important;
  }

  .align-items-lg-start {
    align-items: flex-start !important;
  }

  .align-items-lg-end {
    align-items: flex-end !important;
  }

  .align-items-lg-center {
    align-items: center !important;
  }

  .align-items-lg-baseline {
    align-items: baseline !important;
  }

  .align-items-lg-stretch {
    align-items: stretch !important;
  }

  .align-content-lg-start {
    align-content: flex-start !important;
  }

  .align-content-lg-end {
    align-content: flex-end !important;
  }

  .align-content-lg-center {
    align-content: center !important;
  }

  .align-content-lg-between {
    align-content: space-between !important;
  }

  .align-content-lg-around {
    align-content: space-around !important;
  }

  .align-content-lg-stretch {
    align-content: stretch !important;
  }

  .align-self-lg-auto {
    align-self: auto !important;
  }

  .align-self-lg-start {
    align-self: flex-start !important;
  }

  .align-self-lg-end {
    align-self: flex-end !important;
  }

  .align-self-lg-center {
    align-self: center !important;
  }

  .align-self-lg-baseline {
    align-self: baseline !important;
  }

  .align-self-lg-stretch {
    align-self: stretch !important;
  }

  .order-lg-first {
    order: -1 !important;
  }

  .order-lg-0 {
    order: 0 !important;
  }

  .order-lg-1 {
    order: 1 !important;
  }

  .order-lg-2 {
    order: 2 !important;
  }

  .order-lg-3 {
    order: 3 !important;
  }

  .order-lg-4 {
    order: 4 !important;
  }

  .order-lg-5 {
    order: 5 !important;
  }

  .order-lg-last {
    order: 6 !important;
  }

  .m-lg-0 {
    margin: 0 !important;
  }

  .m-lg-5 {
    margin: 0.3125rem !important;
  }

  .m-lg-10 {
    margin: 0.625rem !important;
  }

  .m-lg-15 {
    margin: 0.9375rem !important;
  }

  .m-lg-20 {
    margin: 1.25rem !important;
  }

  .m-lg-25 {
    margin: 1.5625rem !important;
  }

  .m-lg-30 {
    margin: 1.875rem !important;
  }

  .m-lg-40 {
    margin: 2.5rem !important;
  }

  .m-lg-50 {
    margin: 3.125rem !important;
  }

  .m-lg-70 {
    margin: 4.375rem !important;
  }

  .m-lg-80 {
    margin: 5rem !important;
  }

  .m-lg-100 {
    margin: 6.25rem !important;
  }

  .m-lg-135 {
    margin: 8.4375rem !important;
  }

  .m-lg-150 {
    margin: 9.375rem !important;
  }

  .m-lg-auto {
    margin: auto !important;
  }

  .mx-lg-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }

  .mx-lg-5 {
    margin-right: 0.3125rem !important;
    margin-left: 0.3125rem !important;
  }

  .mx-lg-10 {
    margin-right: 0.625rem !important;
    margin-left: 0.625rem !important;
  }

  .mx-lg-15 {
    margin-right: 0.9375rem !important;
    margin-left: 0.9375rem !important;
  }

  .mx-lg-20 {
    margin-right: 1.25rem !important;
    margin-left: 1.25rem !important;
  }

  .mx-lg-25 {
    margin-right: 1.5625rem !important;
    margin-left: 1.5625rem !important;
  }

  .mx-lg-30 {
    margin-right: 1.875rem !important;
    margin-left: 1.875rem !important;
  }

  .mx-lg-40 {
    margin-right: 2.5rem !important;
    margin-left: 2.5rem !important;
  }

  .mx-lg-50 {
    margin-right: 3.125rem !important;
    margin-left: 3.125rem !important;
  }

  .mx-lg-70 {
    margin-right: 4.375rem !important;
    margin-left: 4.375rem !important;
  }

  .mx-lg-80 {
    margin-right: 5rem !important;
    margin-left: 5rem !important;
  }

  .mx-lg-100 {
    margin-right: 6.25rem !important;
    margin-left: 6.25rem !important;
  }

  .mx-lg-135 {
    margin-right: 8.4375rem !important;
    margin-left: 8.4375rem !important;
  }

  .mx-lg-150 {
    margin-right: 9.375rem !important;
    margin-left: 9.375rem !important;
  }

  .mx-lg-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }

  .my-lg-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }

  .my-lg-5 {
    margin-top: 0.3125rem !important;
    margin-bottom: 0.3125rem !important;
  }

  .my-lg-10 {
    margin-top: 0.625rem !important;
    margin-bottom: 0.625rem !important;
  }

  .my-lg-15 {
    margin-top: 0.9375rem !important;
    margin-bottom: 0.9375rem !important;
  }

  .my-lg-20 {
    margin-top: 1.25rem !important;
    margin-bottom: 1.25rem !important;
  }

  .my-lg-25 {
    margin-top: 1.5625rem !important;
    margin-bottom: 1.5625rem !important;
  }

  .my-lg-30 {
    margin-top: 1.875rem !important;
    margin-bottom: 1.875rem !important;
  }

  .my-lg-40 {
    margin-top: 2.5rem !important;
    margin-bottom: 2.5rem !important;
  }

  .my-lg-50 {
    margin-top: 3.125rem !important;
    margin-bottom: 3.125rem !important;
  }

  .my-lg-70 {
    margin-top: 4.375rem !important;
    margin-bottom: 4.375rem !important;
  }

  .my-lg-80 {
    margin-top: 5rem !important;
    margin-bottom: 5rem !important;
  }

  .my-lg-100 {
    margin-top: 6.25rem !important;
    margin-bottom: 6.25rem !important;
  }

  .my-lg-135 {
    margin-top: 8.4375rem !important;
    margin-bottom: 8.4375rem !important;
  }

  .my-lg-150 {
    margin-top: 9.375rem !important;
    margin-bottom: 9.375rem !important;
  }

  .my-lg-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }

  .mt-lg-0 {
    margin-top: 0 !important;
  }

  .mt-lg-5 {
    margin-top: 0.3125rem !important;
  }

  .mt-lg-10 {
    margin-top: 0.625rem !important;
  }

  .mt-lg-15 {
    margin-top: 0.9375rem !important;
  }

  .mt-lg-20 {
    margin-top: 1.25rem !important;
  }

  .mt-lg-25 {
    margin-top: 1.5625rem !important;
  }

  .mt-lg-30 {
    margin-top: 1.875rem !important;
  }

  .mt-lg-40 {
    margin-top: 2.5rem !important;
  }

  .mt-lg-50 {
    margin-top: 3.125rem !important;
  }

  .mt-lg-70 {
    margin-top: 4.375rem !important;
  }

  .mt-lg-80 {
    margin-top: 5rem !important;
  }

  .mt-lg-100 {
    margin-top: 6.25rem !important;
  }

  .mt-lg-135 {
    margin-top: 8.4375rem !important;
  }

  .mt-lg-150 {
    margin-top: 9.375rem !important;
  }

  .mt-lg-auto {
    margin-top: auto !important;
  }

  .me-lg-0 {
    margin-right: 0 !important;
  }

  .me-lg-5 {
    margin-right: 0.3125rem !important;
  }

  .me-lg-10 {
    margin-right: 0.625rem !important;
  }

  .me-lg-15 {
    margin-right: 0.9375rem !important;
  }

  .me-lg-20 {
    margin-right: 1.25rem !important;
  }

  .me-lg-25 {
    margin-right: 1.5625rem !important;
  }

  .me-lg-30 {
    margin-right: 1.875rem !important;
  }

  .me-lg-40 {
    margin-right: 2.5rem !important;
  }

  .me-lg-50 {
    margin-right: 3.125rem !important;
  }

  .me-lg-70 {
    margin-right: 4.375rem !important;
  }

  .me-lg-80 {
    margin-right: 5rem !important;
  }

  .me-lg-100 {
    margin-right: 6.25rem !important;
  }

  .me-lg-135 {
    margin-right: 8.4375rem !important;
  }

  .me-lg-150 {
    margin-right: 9.375rem !important;
  }

  .me-lg-auto {
    margin-right: auto !important;
  }

  .mb-lg-0 {
    margin-bottom: 0 !important;
  }

  .mb-lg-5 {
    margin-bottom: 0.3125rem !important;
  }

  .mb-lg-10 {
    margin-bottom: 0.625rem !important;
  }

  .mb-lg-15 {
    margin-bottom: 0.9375rem !important;
  }

  .mb-lg-20 {
    margin-bottom: 1.25rem !important;
  }

  .mb-lg-25 {
    margin-bottom: 1.5625rem !important;
  }

  .mb-lg-30 {
    margin-bottom: 1.875rem !important;
  }

  .mb-lg-40 {
    margin-bottom: 2.5rem !important;
  }

  .mb-lg-50 {
    margin-bottom: 3.125rem !important;
  }

  .mb-lg-70 {
    margin-bottom: 4.375rem !important;
  }

  .mb-lg-80 {
    margin-bottom: 5rem !important;
  }

  .mb-lg-100 {
    margin-bottom: 6.25rem !important;
  }

  .mb-lg-135 {
    margin-bottom: 8.4375rem !important;
  }

  .mb-lg-150 {
    margin-bottom: 9.375rem !important;
  }

  .mb-lg-auto {
    margin-bottom: auto !important;
  }

  .ms-lg-0 {
    margin-left: 0 !important;
  }

  .ms-lg-5 {
    margin-left: 0.3125rem !important;
  }

  .ms-lg-10 {
    margin-left: 0.625rem !important;
  }

  .ms-lg-15 {
    margin-left: 0.9375rem !important;
  }

  .ms-lg-20 {
    margin-left: 1.25rem !important;
  }

  .ms-lg-25 {
    margin-left: 1.5625rem !important;
  }

  .ms-lg-30 {
    margin-left: 1.875rem !important;
  }

  .ms-lg-40 {
    margin-left: 2.5rem !important;
  }

  .ms-lg-50 {
    margin-left: 3.125rem !important;
  }

  .ms-lg-70 {
    margin-left: 4.375rem !important;
  }

  .ms-lg-80 {
    margin-left: 5rem !important;
  }

  .ms-lg-100 {
    margin-left: 6.25rem !important;
  }

  .ms-lg-135 {
    margin-left: 8.4375rem !important;
  }

  .ms-lg-150 {
    margin-left: 9.375rem !important;
  }

  .ms-lg-auto {
    margin-left: auto !important;
  }

  .m-lg-n5 {
    margin: -0.3125rem !important;
  }

  .m-lg-n10 {
    margin: -0.625rem !important;
  }

  .m-lg-n15 {
    margin: -0.9375rem !important;
  }

  .m-lg-n20 {
    margin: -1.25rem !important;
  }

  .m-lg-n25 {
    margin: -1.5625rem !important;
  }

  .m-lg-n30 {
    margin: -1.875rem !important;
  }

  .m-lg-n40 {
    margin: -2.5rem !important;
  }

  .m-lg-n50 {
    margin: -3.125rem !important;
  }

  .m-lg-n70 {
    margin: -4.375rem !important;
  }

  .m-lg-n80 {
    margin: -5rem !important;
  }

  .m-lg-n100 {
    margin: -6.25rem !important;
  }

  .m-lg-n135 {
    margin: -8.4375rem !important;
  }

  .m-lg-n150 {
    margin: -9.375rem !important;
  }

  .mx-lg-n5 {
    margin-right: -0.3125rem !important;
    margin-left: -0.3125rem !important;
  }

  .mx-lg-n10 {
    margin-right: -0.625rem !important;
    margin-left: -0.625rem !important;
  }

  .mx-lg-n15 {
    margin-right: -0.9375rem !important;
    margin-left: -0.9375rem !important;
  }

  .mx-lg-n20 {
    margin-right: -1.25rem !important;
    margin-left: -1.25rem !important;
  }

  .mx-lg-n25 {
    margin-right: -1.5625rem !important;
    margin-left: -1.5625rem !important;
  }

  .mx-lg-n30 {
    margin-right: -1.875rem !important;
    margin-left: -1.875rem !important;
  }

  .mx-lg-n40 {
    margin-right: -2.5rem !important;
    margin-left: -2.5rem !important;
  }

  .mx-lg-n50 {
    margin-right: -3.125rem !important;
    margin-left: -3.125rem !important;
  }

  .mx-lg-n70 {
    margin-right: -4.375rem !important;
    margin-left: -4.375rem !important;
  }

  .mx-lg-n80 {
    margin-right: -5rem !important;
    margin-left: -5rem !important;
  }

  .mx-lg-n100 {
    margin-right: -6.25rem !important;
    margin-left: -6.25rem !important;
  }

  .mx-lg-n135 {
    margin-right: -8.4375rem !important;
    margin-left: -8.4375rem !important;
  }

  .mx-lg-n150 {
    margin-right: -9.375rem !important;
    margin-left: -9.375rem !important;
  }

  .my-lg-n5 {
    margin-top: -0.3125rem !important;
    margin-bottom: -0.3125rem !important;
  }

  .my-lg-n10 {
    margin-top: -0.625rem !important;
    margin-bottom: -0.625rem !important;
  }

  .my-lg-n15 {
    margin-top: -0.9375rem !important;
    margin-bottom: -0.9375rem !important;
  }

  .my-lg-n20 {
    margin-top: -1.25rem !important;
    margin-bottom: -1.25rem !important;
  }

  .my-lg-n25 {
    margin-top: -1.5625rem !important;
    margin-bottom: -1.5625rem !important;
  }

  .my-lg-n30 {
    margin-top: -1.875rem !important;
    margin-bottom: -1.875rem !important;
  }

  .my-lg-n40 {
    margin-top: -2.5rem !important;
    margin-bottom: -2.5rem !important;
  }

  .my-lg-n50 {
    margin-top: -3.125rem !important;
    margin-bottom: -3.125rem !important;
  }

  .my-lg-n70 {
    margin-top: -4.375rem !important;
    margin-bottom: -4.375rem !important;
  }

  .my-lg-n80 {
    margin-top: -5rem !important;
    margin-bottom: -5rem !important;
  }

  .my-lg-n100 {
    margin-top: -6.25rem !important;
    margin-bottom: -6.25rem !important;
  }

  .my-lg-n135 {
    margin-top: -8.4375rem !important;
    margin-bottom: -8.4375rem !important;
  }

  .my-lg-n150 {
    margin-top: -9.375rem !important;
    margin-bottom: -9.375rem !important;
  }

  .mt-lg-n5 {
    margin-top: -0.3125rem !important;
  }

  .mt-lg-n10 {
    margin-top: -0.625rem !important;
  }

  .mt-lg-n15 {
    margin-top: -0.9375rem !important;
  }

  .mt-lg-n20 {
    margin-top: -1.25rem !important;
  }

  .mt-lg-n25 {
    margin-top: -1.5625rem !important;
  }

  .mt-lg-n30 {
    margin-top: -1.875rem !important;
  }

  .mt-lg-n40 {
    margin-top: -2.5rem !important;
  }

  .mt-lg-n50 {
    margin-top: -3.125rem !important;
  }

  .mt-lg-n70 {
    margin-top: -4.375rem !important;
  }

  .mt-lg-n80 {
    margin-top: -5rem !important;
  }

  .mt-lg-n100 {
    margin-top: -6.25rem !important;
  }

  .mt-lg-n135 {
    margin-top: -8.4375rem !important;
  }

  .mt-lg-n150 {
    margin-top: -9.375rem !important;
  }

  .me-lg-n5 {
    margin-right: -0.3125rem !important;
  }

  .me-lg-n10 {
    margin-right: -0.625rem !important;
  }

  .me-lg-n15 {
    margin-right: -0.9375rem !important;
  }

  .me-lg-n20 {
    margin-right: -1.25rem !important;
  }

  .me-lg-n25 {
    margin-right: -1.5625rem !important;
  }

  .me-lg-n30 {
    margin-right: -1.875rem !important;
  }

  .me-lg-n40 {
    margin-right: -2.5rem !important;
  }

  .me-lg-n50 {
    margin-right: -3.125rem !important;
  }

  .me-lg-n70 {
    margin-right: -4.375rem !important;
  }

  .me-lg-n80 {
    margin-right: -5rem !important;
  }

  .me-lg-n100 {
    margin-right: -6.25rem !important;
  }

  .me-lg-n135 {
    margin-right: -8.4375rem !important;
  }

  .me-lg-n150 {
    margin-right: -9.375rem !important;
  }

  .mb-lg-n5 {
    margin-bottom: -0.3125rem !important;
  }

  .mb-lg-n10 {
    margin-bottom: -0.625rem !important;
  }

  .mb-lg-n15 {
    margin-bottom: -0.9375rem !important;
  }

  .mb-lg-n20 {
    margin-bottom: -1.25rem !important;
  }

  .mb-lg-n25 {
    margin-bottom: -1.5625rem !important;
  }

  .mb-lg-n30 {
    margin-bottom: -1.875rem !important;
  }

  .mb-lg-n40 {
    margin-bottom: -2.5rem !important;
  }

  .mb-lg-n50 {
    margin-bottom: -3.125rem !important;
  }

  .mb-lg-n70 {
    margin-bottom: -4.375rem !important;
  }

  .mb-lg-n80 {
    margin-bottom: -5rem !important;
  }

  .mb-lg-n100 {
    margin-bottom: -6.25rem !important;
  }

  .mb-lg-n135 {
    margin-bottom: -8.4375rem !important;
  }

  .mb-lg-n150 {
    margin-bottom: -9.375rem !important;
  }

  .ms-lg-n5 {
    margin-left: -0.3125rem !important;
  }

  .ms-lg-n10 {
    margin-left: -0.625rem !important;
  }

  .ms-lg-n15 {
    margin-left: -0.9375rem !important;
  }

  .ms-lg-n20 {
    margin-left: -1.25rem !important;
  }

  .ms-lg-n25 {
    margin-left: -1.5625rem !important;
  }

  .ms-lg-n30 {
    margin-left: -1.875rem !important;
  }

  .ms-lg-n40 {
    margin-left: -2.5rem !important;
  }

  .ms-lg-n50 {
    margin-left: -3.125rem !important;
  }

  .ms-lg-n70 {
    margin-left: -4.375rem !important;
  }

  .ms-lg-n80 {
    margin-left: -5rem !important;
  }

  .ms-lg-n100 {
    margin-left: -6.25rem !important;
  }

  .ms-lg-n135 {
    margin-left: -8.4375rem !important;
  }

  .ms-lg-n150 {
    margin-left: -9.375rem !important;
  }

  .p-lg-0 {
    padding: 0 !important;
  }

  .p-lg-5 {
    padding: 0.3125rem !important;
  }

  .p-lg-10 {
    padding: 0.625rem !important;
  }

  .p-lg-15 {
    padding: 0.9375rem !important;
  }

  .p-lg-20 {
    padding: 1.25rem !important;
  }

  .p-lg-25 {
    padding: 1.5625rem !important;
  }

  .p-lg-30 {
    padding: 1.875rem !important;
  }

  .p-lg-40 {
    padding: 2.5rem !important;
  }

  .p-lg-50 {
    padding: 3.125rem !important;
  }

  .p-lg-70 {
    padding: 4.375rem !important;
  }

  .p-lg-80 {
    padding: 5rem !important;
  }

  .p-lg-100 {
    padding: 6.25rem !important;
  }

  .p-lg-135 {
    padding: 8.4375rem !important;
  }

  .p-lg-150 {
    padding: 9.375rem !important;
  }

  .px-lg-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }

  .px-lg-5 {
    padding-right: 0.3125rem !important;
    padding-left: 0.3125rem !important;
  }

  .px-lg-10 {
    padding-right: 0.625rem !important;
    padding-left: 0.625rem !important;
  }

  .px-lg-15 {
    padding-right: 0.9375rem !important;
    padding-left: 0.9375rem !important;
  }

  .px-lg-20 {
    padding-right: 1.25rem !important;
    padding-left: 1.25rem !important;
  }

  .px-lg-25 {
    padding-right: 1.5625rem !important;
    padding-left: 1.5625rem !important;
  }

  .px-lg-30 {
    padding-right: 1.875rem !important;
    padding-left: 1.875rem !important;
  }

  .px-lg-40 {
    padding-right: 2.5rem !important;
    padding-left: 2.5rem !important;
  }

  .px-lg-50 {
    padding-right: 3.125rem !important;
    padding-left: 3.125rem !important;
  }

  .px-lg-70 {
    padding-right: 4.375rem !important;
    padding-left: 4.375rem !important;
  }

  .px-lg-80 {
    padding-right: 5rem !important;
    padding-left: 5rem !important;
  }

  .px-lg-100 {
    padding-right: 6.25rem !important;
    padding-left: 6.25rem !important;
  }

  .px-lg-135 {
    padding-right: 8.4375rem !important;
    padding-left: 8.4375rem !important;
  }

  .px-lg-150 {
    padding-right: 9.375rem !important;
    padding-left: 9.375rem !important;
  }

  .py-lg-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  .py-lg-5 {
    padding-top: 0.3125rem !important;
    padding-bottom: 0.3125rem !important;
  }

  .py-lg-10 {
    padding-top: 0.625rem !important;
    padding-bottom: 0.625rem !important;
  }

  .py-lg-15 {
    padding-top: 0.9375rem !important;
    padding-bottom: 0.9375rem !important;
  }

  .py-lg-20 {
    padding-top: 1.25rem !important;
    padding-bottom: 1.25rem !important;
  }

  .py-lg-25 {
    padding-top: 1.5625rem !important;
    padding-bottom: 1.5625rem !important;
  }

  .py-lg-30 {
    padding-top: 1.875rem !important;
    padding-bottom: 1.875rem !important;
  }

  .py-lg-40 {
    padding-top: 2.5rem !important;
    padding-bottom: 2.5rem !important;
  }

  .py-lg-50 {
    padding-top: 3.125rem !important;
    padding-bottom: 3.125rem !important;
  }

  .py-lg-70 {
    padding-top: 4.375rem !important;
    padding-bottom: 4.375rem !important;
  }

  .py-lg-80 {
    padding-top: 5rem !important;
    padding-bottom: 5rem !important;
  }

  .py-lg-100 {
    padding-top: 6.25rem !important;
    padding-bottom: 6.25rem !important;
  }

  .py-lg-135 {
    padding-top: 8.4375rem !important;
    padding-bottom: 8.4375rem !important;
  }

  .py-lg-150 {
    padding-top: 9.375rem !important;
    padding-bottom: 9.375rem !important;
  }

  .pt-lg-0 {
    padding-top: 0 !important;
  }

  .pt-lg-5 {
    padding-top: 0.3125rem !important;
  }

  .pt-lg-10 {
    padding-top: 0.625rem !important;
  }

  .pt-lg-15 {
    padding-top: 0.9375rem !important;
  }

  .pt-lg-20 {
    padding-top: 1.25rem !important;
  }

  .pt-lg-25 {
    padding-top: 1.5625rem !important;
  }

  .pt-lg-30 {
    padding-top: 1.875rem !important;
  }

  .pt-lg-40 {
    padding-top: 2.5rem !important;
  }

  .pt-lg-50 {
    padding-top: 3.125rem !important;
  }

  .pt-lg-70 {
    padding-top: 4.375rem !important;
  }

  .pt-lg-80 {
    padding-top: 5rem !important;
  }

  .pt-lg-100 {
    padding-top: 6.25rem !important;
  }

  .pt-lg-135 {
    padding-top: 8.4375rem !important;
  }

  .pt-lg-150 {
    padding-top: 9.375rem !important;
  }

  .pe-lg-0 {
    padding-right: 0 !important;
  }

  .pe-lg-5 {
    padding-right: 0.3125rem !important;
  }

  .pe-lg-10 {
    padding-right: 0.625rem !important;
  }

  .pe-lg-15 {
    padding-right: 0.9375rem !important;
  }

  .pe-lg-20 {
    padding-right: 1.25rem !important;
  }

  .pe-lg-25 {
    padding-right: 1.5625rem !important;
  }

  .pe-lg-30 {
    padding-right: 1.875rem !important;
  }

  .pe-lg-40 {
    padding-right: 2.5rem !important;
  }

  .pe-lg-50 {
    padding-right: 3.125rem !important;
  }

  .pe-lg-70 {
    padding-right: 4.375rem !important;
  }

  .pe-lg-80 {
    padding-right: 5rem !important;
  }

  .pe-lg-100 {
    padding-right: 6.25rem !important;
  }

  .pe-lg-135 {
    padding-right: 8.4375rem !important;
  }

  .pe-lg-150 {
    padding-right: 9.375rem !important;
  }

  .pb-lg-0 {
    padding-bottom: 0 !important;
  }

  .pb-lg-5 {
    padding-bottom: 0.3125rem !important;
  }

  .pb-lg-10 {
    padding-bottom: 0.625rem !important;
  }

  .pb-lg-15 {
    padding-bottom: 0.9375rem !important;
  }

  .pb-lg-20 {
    padding-bottom: 1.25rem !important;
  }

  .pb-lg-25 {
    padding-bottom: 1.5625rem !important;
  }

  .pb-lg-30 {
    padding-bottom: 1.875rem !important;
  }

  .pb-lg-40 {
    padding-bottom: 2.5rem !important;
  }

  .pb-lg-50 {
    padding-bottom: 3.125rem !important;
  }

  .pb-lg-70 {
    padding-bottom: 4.375rem !important;
  }

  .pb-lg-80 {
    padding-bottom: 5rem !important;
  }

  .pb-lg-100 {
    padding-bottom: 6.25rem !important;
  }

  .pb-lg-135 {
    padding-bottom: 8.4375rem !important;
  }

  .pb-lg-150 {
    padding-bottom: 9.375rem !important;
  }

  .ps-lg-0 {
    padding-left: 0 !important;
  }

  .ps-lg-5 {
    padding-left: 0.3125rem !important;
  }

  .ps-lg-10 {
    padding-left: 0.625rem !important;
  }

  .ps-lg-15 {
    padding-left: 0.9375rem !important;
  }

  .ps-lg-20 {
    padding-left: 1.25rem !important;
  }

  .ps-lg-25 {
    padding-left: 1.5625rem !important;
  }

  .ps-lg-30 {
    padding-left: 1.875rem !important;
  }

  .ps-lg-40 {
    padding-left: 2.5rem !important;
  }

  .ps-lg-50 {
    padding-left: 3.125rem !important;
  }

  .ps-lg-70 {
    padding-left: 4.375rem !important;
  }

  .ps-lg-80 {
    padding-left: 5rem !important;
  }

  .ps-lg-100 {
    padding-left: 6.25rem !important;
  }

  .ps-lg-135 {
    padding-left: 8.4375rem !important;
  }

  .ps-lg-150 {
    padding-left: 9.375rem !important;
  }

  .gap-lg-0 {
    gap: 0 !important;
  }

  .gap-lg-5 {
    gap: 0.3125rem !important;
  }

  .gap-lg-10 {
    gap: 0.625rem !important;
  }

  .gap-lg-15 {
    gap: 0.9375rem !important;
  }

  .gap-lg-20 {
    gap: 1.25rem !important;
  }

  .gap-lg-25 {
    gap: 1.5625rem !important;
  }

  .gap-lg-30 {
    gap: 1.875rem !important;
  }

  .gap-lg-40 {
    gap: 2.5rem !important;
  }

  .gap-lg-50 {
    gap: 3.125rem !important;
  }

  .gap-lg-70 {
    gap: 4.375rem !important;
  }

  .gap-lg-80 {
    gap: 5rem !important;
  }

  .gap-lg-100 {
    gap: 6.25rem !important;
  }

  .gap-lg-135 {
    gap: 8.4375rem !important;
  }

  .gap-lg-150 {
    gap: 9.375rem !important;
  }

  .row-gap-lg-0 {
    row-gap: 0 !important;
  }

  .row-gap-lg-5 {
    row-gap: 0.3125rem !important;
  }

  .row-gap-lg-10 {
    row-gap: 0.625rem !important;
  }

  .row-gap-lg-15 {
    row-gap: 0.9375rem !important;
  }

  .row-gap-lg-20 {
    row-gap: 1.25rem !important;
  }

  .row-gap-lg-25 {
    row-gap: 1.5625rem !important;
  }

  .row-gap-lg-30 {
    row-gap: 1.875rem !important;
  }

  .row-gap-lg-40 {
    row-gap: 2.5rem !important;
  }

  .row-gap-lg-50 {
    row-gap: 3.125rem !important;
  }

  .row-gap-lg-70 {
    row-gap: 4.375rem !important;
  }

  .row-gap-lg-80 {
    row-gap: 5rem !important;
  }

  .row-gap-lg-100 {
    row-gap: 6.25rem !important;
  }

  .row-gap-lg-135 {
    row-gap: 8.4375rem !important;
  }

  .row-gap-lg-150 {
    row-gap: 9.375rem !important;
  }

  .column-gap-lg-0 {
    column-gap: 0 !important;
  }

  .column-gap-lg-5 {
    column-gap: 0.3125rem !important;
  }

  .column-gap-lg-10 {
    column-gap: 0.625rem !important;
  }

  .column-gap-lg-15 {
    column-gap: 0.9375rem !important;
  }

  .column-gap-lg-20 {
    column-gap: 1.25rem !important;
  }

  .column-gap-lg-25 {
    column-gap: 1.5625rem !important;
  }

  .column-gap-lg-30 {
    column-gap: 1.875rem !important;
  }

  .column-gap-lg-40 {
    column-gap: 2.5rem !important;
  }

  .column-gap-lg-50 {
    column-gap: 3.125rem !important;
  }

  .column-gap-lg-70 {
    column-gap: 4.375rem !important;
  }

  .column-gap-lg-80 {
    column-gap: 5rem !important;
  }

  .column-gap-lg-100 {
    column-gap: 6.25rem !important;
  }

  .column-gap-lg-135 {
    column-gap: 8.4375rem !important;
  }

  .column-gap-lg-150 {
    column-gap: 9.375rem !important;
  }

  .text-lg-start {
    text-align: left !important;
  }

  .text-lg-end {
    text-align: right !important;
  }

  .text-lg-center {
    text-align: center !important;
  }
}
@media (min-width: 1200px) {
  .float-xl-start {
    float: left !important;
  }

  .float-xl-end {
    float: right !important;
  }

  .float-xl-none {
    float: none !important;
  }

  .object-fit-xl-contain {
    object-fit: contain !important;
  }

  .object-fit-xl-cover {
    object-fit: cover !important;
  }

  .object-fit-xl-fill {
    object-fit: fill !important;
  }

  .object-fit-xl-scale {
    object-fit: scale-down !important;
  }

  .object-fit-xl-none {
    object-fit: none !important;
  }

  .d-xl-inline {
    display: inline !important;
  }

  .d-xl-inline-block {
    display: inline-block !important;
  }

  .d-xl-block {
    display: block !important;
  }

  .d-xl-grid {
    display: grid !important;
  }

  .d-xl-inline-grid {
    display: inline-grid !important;
  }

  .d-xl-table {
    display: table !important;
  }

  .d-xl-table-row {
    display: table-row !important;
  }

  .d-xl-table-cell {
    display: table-cell !important;
  }

  .d-xl-flex {
    display: flex !important;
  }

  .d-xl-inline-flex {
    display: inline-flex !important;
  }

  .d-xl-none {
    display: none !important;
  }

  .flex-xl-fill {
    flex: 1 1 auto !important;
  }

  .flex-xl-row {
    flex-direction: row !important;
  }

  .flex-xl-column {
    flex-direction: column !important;
  }

  .flex-xl-row-reverse {
    flex-direction: row-reverse !important;
  }

  .flex-xl-column-reverse {
    flex-direction: column-reverse !important;
  }

  .flex-xl-grow-0 {
    flex-grow: 0 !important;
  }

  .flex-xl-grow-1 {
    flex-grow: 1 !important;
  }

  .flex-xl-shrink-0 {
    flex-shrink: 0 !important;
  }

  .flex-xl-shrink-1 {
    flex-shrink: 1 !important;
  }

  .flex-xl-wrap {
    flex-wrap: wrap !important;
  }

  .flex-xl-nowrap {
    flex-wrap: nowrap !important;
  }

  .flex-xl-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }

  .justify-content-xl-start {
    justify-content: flex-start !important;
  }

  .justify-content-xl-end {
    justify-content: flex-end !important;
  }

  .justify-content-xl-center {
    justify-content: center !important;
  }

  .justify-content-xl-between {
    justify-content: space-between !important;
  }

  .justify-content-xl-around {
    justify-content: space-around !important;
  }

  .justify-content-xl-evenly {
    justify-content: space-evenly !important;
  }

  .align-items-xl-start {
    align-items: flex-start !important;
  }

  .align-items-xl-end {
    align-items: flex-end !important;
  }

  .align-items-xl-center {
    align-items: center !important;
  }

  .align-items-xl-baseline {
    align-items: baseline !important;
  }

  .align-items-xl-stretch {
    align-items: stretch !important;
  }

  .align-content-xl-start {
    align-content: flex-start !important;
  }

  .align-content-xl-end {
    align-content: flex-end !important;
  }

  .align-content-xl-center {
    align-content: center !important;
  }

  .align-content-xl-between {
    align-content: space-between !important;
  }

  .align-content-xl-around {
    align-content: space-around !important;
  }

  .align-content-xl-stretch {
    align-content: stretch !important;
  }

  .align-self-xl-auto {
    align-self: auto !important;
  }

  .align-self-xl-start {
    align-self: flex-start !important;
  }

  .align-self-xl-end {
    align-self: flex-end !important;
  }

  .align-self-xl-center {
    align-self: center !important;
  }

  .align-self-xl-baseline {
    align-self: baseline !important;
  }

  .align-self-xl-stretch {
    align-self: stretch !important;
  }

  .order-xl-first {
    order: -1 !important;
  }

  .order-xl-0 {
    order: 0 !important;
  }

  .order-xl-1 {
    order: 1 !important;
  }

  .order-xl-2 {
    order: 2 !important;
  }

  .order-xl-3 {
    order: 3 !important;
  }

  .order-xl-4 {
    order: 4 !important;
  }

  .order-xl-5 {
    order: 5 !important;
  }

  .order-xl-last {
    order: 6 !important;
  }

  .m-xl-0 {
    margin: 0 !important;
  }

  .m-xl-5 {
    margin: 0.3125rem !important;
  }

  .m-xl-10 {
    margin: 0.625rem !important;
  }

  .m-xl-15 {
    margin: 0.9375rem !important;
  }

  .m-xl-20 {
    margin: 1.25rem !important;
  }

  .m-xl-25 {
    margin: 1.5625rem !important;
  }

  .m-xl-30 {
    margin: 1.875rem !important;
  }

  .m-xl-40 {
    margin: 2.5rem !important;
  }

  .m-xl-50 {
    margin: 3.125rem !important;
  }

  .m-xl-70 {
    margin: 4.375rem !important;
  }

  .m-xl-80 {
    margin: 5rem !important;
  }

  .m-xl-100 {
    margin: 6.25rem !important;
  }

  .m-xl-135 {
    margin: 8.4375rem !important;
  }

  .m-xl-150 {
    margin: 9.375rem !important;
  }

  .m-xl-auto {
    margin: auto !important;
  }

  .mx-xl-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }

  .mx-xl-5 {
    margin-right: 0.3125rem !important;
    margin-left: 0.3125rem !important;
  }

  .mx-xl-10 {
    margin-right: 0.625rem !important;
    margin-left: 0.625rem !important;
  }

  .mx-xl-15 {
    margin-right: 0.9375rem !important;
    margin-left: 0.9375rem !important;
  }

  .mx-xl-20 {
    margin-right: 1.25rem !important;
    margin-left: 1.25rem !important;
  }

  .mx-xl-25 {
    margin-right: 1.5625rem !important;
    margin-left: 1.5625rem !important;
  }

  .mx-xl-30 {
    margin-right: 1.875rem !important;
    margin-left: 1.875rem !important;
  }

  .mx-xl-40 {
    margin-right: 2.5rem !important;
    margin-left: 2.5rem !important;
  }

  .mx-xl-50 {
    margin-right: 3.125rem !important;
    margin-left: 3.125rem !important;
  }

  .mx-xl-70 {
    margin-right: 4.375rem !important;
    margin-left: 4.375rem !important;
  }

  .mx-xl-80 {
    margin-right: 5rem !important;
    margin-left: 5rem !important;
  }

  .mx-xl-100 {
    margin-right: 6.25rem !important;
    margin-left: 6.25rem !important;
  }

  .mx-xl-135 {
    margin-right: 8.4375rem !important;
    margin-left: 8.4375rem !important;
  }

  .mx-xl-150 {
    margin-right: 9.375rem !important;
    margin-left: 9.375rem !important;
  }

  .mx-xl-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }

  .my-xl-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }

  .my-xl-5 {
    margin-top: 0.3125rem !important;
    margin-bottom: 0.3125rem !important;
  }

  .my-xl-10 {
    margin-top: 0.625rem !important;
    margin-bottom: 0.625rem !important;
  }

  .my-xl-15 {
    margin-top: 0.9375rem !important;
    margin-bottom: 0.9375rem !important;
  }

  .my-xl-20 {
    margin-top: 1.25rem !important;
    margin-bottom: 1.25rem !important;
  }

  .my-xl-25 {
    margin-top: 1.5625rem !important;
    margin-bottom: 1.5625rem !important;
  }

  .my-xl-30 {
    margin-top: 1.875rem !important;
    margin-bottom: 1.875rem !important;
  }

  .my-xl-40 {
    margin-top: 2.5rem !important;
    margin-bottom: 2.5rem !important;
  }

  .my-xl-50 {
    margin-top: 3.125rem !important;
    margin-bottom: 3.125rem !important;
  }

  .my-xl-70 {
    margin-top: 4.375rem !important;
    margin-bottom: 4.375rem !important;
  }

  .my-xl-80 {
    margin-top: 5rem !important;
    margin-bottom: 5rem !important;
  }

  .my-xl-100 {
    margin-top: 6.25rem !important;
    margin-bottom: 6.25rem !important;
  }

  .my-xl-135 {
    margin-top: 8.4375rem !important;
    margin-bottom: 8.4375rem !important;
  }

  .my-xl-150 {
    margin-top: 9.375rem !important;
    margin-bottom: 9.375rem !important;
  }

  .my-xl-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }

  .mt-xl-0 {
    margin-top: 0 !important;
  }

  .mt-xl-5 {
    margin-top: 0.3125rem !important;
  }

  .mt-xl-10 {
    margin-top: 0.625rem !important;
  }

  .mt-xl-15 {
    margin-top: 0.9375rem !important;
  }

  .mt-xl-20 {
    margin-top: 1.25rem !important;
  }

  .mt-xl-25 {
    margin-top: 1.5625rem !important;
  }

  .mt-xl-30 {
    margin-top: 1.875rem !important;
  }

  .mt-xl-40 {
    margin-top: 2.5rem !important;
  }

  .mt-xl-50 {
    margin-top: 3.125rem !important;
  }

  .mt-xl-70 {
    margin-top: 4.375rem !important;
  }

  .mt-xl-80 {
    margin-top: 5rem !important;
  }

  .mt-xl-100 {
    margin-top: 6.25rem !important;
  }

  .mt-xl-135 {
    margin-top: 8.4375rem !important;
  }

  .mt-xl-150 {
    margin-top: 9.375rem !important;
  }

  .mt-xl-auto {
    margin-top: auto !important;
  }

  .me-xl-0 {
    margin-right: 0 !important;
  }

  .me-xl-5 {
    margin-right: 0.3125rem !important;
  }

  .me-xl-10 {
    margin-right: 0.625rem !important;
  }

  .me-xl-15 {
    margin-right: 0.9375rem !important;
  }

  .me-xl-20 {
    margin-right: 1.25rem !important;
  }

  .me-xl-25 {
    margin-right: 1.5625rem !important;
  }

  .me-xl-30 {
    margin-right: 1.875rem !important;
  }

  .me-xl-40 {
    margin-right: 2.5rem !important;
  }

  .me-xl-50 {
    margin-right: 3.125rem !important;
  }

  .me-xl-70 {
    margin-right: 4.375rem !important;
  }

  .me-xl-80 {
    margin-right: 5rem !important;
  }

  .me-xl-100 {
    margin-right: 6.25rem !important;
  }

  .me-xl-135 {
    margin-right: 8.4375rem !important;
  }

  .me-xl-150 {
    margin-right: 9.375rem !important;
  }

  .me-xl-auto {
    margin-right: auto !important;
  }

  .mb-xl-0 {
    margin-bottom: 0 !important;
  }

  .mb-xl-5 {
    margin-bottom: 0.3125rem !important;
  }

  .mb-xl-10 {
    margin-bottom: 0.625rem !important;
  }

  .mb-xl-15 {
    margin-bottom: 0.9375rem !important;
  }

  .mb-xl-20 {
    margin-bottom: 1.25rem !important;
  }

  .mb-xl-25 {
    margin-bottom: 1.5625rem !important;
  }

  .mb-xl-30 {
    margin-bottom: 1.875rem !important;
  }

  .mb-xl-40 {
    margin-bottom: 2.5rem !important;
  }

  .mb-xl-50 {
    margin-bottom: 3.125rem !important;
  }

  .mb-xl-70 {
    margin-bottom: 4.375rem !important;
  }

  .mb-xl-80 {
    margin-bottom: 5rem !important;
  }

  .mb-xl-100 {
    margin-bottom: 6.25rem !important;
  }

  .mb-xl-135 {
    margin-bottom: 8.4375rem !important;
  }

  .mb-xl-150 {
    margin-bottom: 9.375rem !important;
  }

  .mb-xl-auto {
    margin-bottom: auto !important;
  }

  .ms-xl-0 {
    margin-left: 0 !important;
  }

  .ms-xl-5 {
    margin-left: 0.3125rem !important;
  }

  .ms-xl-10 {
    margin-left: 0.625rem !important;
  }

  .ms-xl-15 {
    margin-left: 0.9375rem !important;
  }

  .ms-xl-20 {
    margin-left: 1.25rem !important;
  }

  .ms-xl-25 {
    margin-left: 1.5625rem !important;
  }

  .ms-xl-30 {
    margin-left: 1.875rem !important;
  }

  .ms-xl-40 {
    margin-left: 2.5rem !important;
  }

  .ms-xl-50 {
    margin-left: 3.125rem !important;
  }

  .ms-xl-70 {
    margin-left: 4.375rem !important;
  }

  .ms-xl-80 {
    margin-left: 5rem !important;
  }

  .ms-xl-100 {
    margin-left: 6.25rem !important;
  }

  .ms-xl-135 {
    margin-left: 8.4375rem !important;
  }

  .ms-xl-150 {
    margin-left: 9.375rem !important;
  }

  .ms-xl-auto {
    margin-left: auto !important;
  }

  .m-xl-n5 {
    margin: -0.3125rem !important;
  }

  .m-xl-n10 {
    margin: -0.625rem !important;
  }

  .m-xl-n15 {
    margin: -0.9375rem !important;
  }

  .m-xl-n20 {
    margin: -1.25rem !important;
  }

  .m-xl-n25 {
    margin: -1.5625rem !important;
  }

  .m-xl-n30 {
    margin: -1.875rem !important;
  }

  .m-xl-n40 {
    margin: -2.5rem !important;
  }

  .m-xl-n50 {
    margin: -3.125rem !important;
  }

  .m-xl-n70 {
    margin: -4.375rem !important;
  }

  .m-xl-n80 {
    margin: -5rem !important;
  }

  .m-xl-n100 {
    margin: -6.25rem !important;
  }

  .m-xl-n135 {
    margin: -8.4375rem !important;
  }

  .m-xl-n150 {
    margin: -9.375rem !important;
  }

  .mx-xl-n5 {
    margin-right: -0.3125rem !important;
    margin-left: -0.3125rem !important;
  }

  .mx-xl-n10 {
    margin-right: -0.625rem !important;
    margin-left: -0.625rem !important;
  }

  .mx-xl-n15 {
    margin-right: -0.9375rem !important;
    margin-left: -0.9375rem !important;
  }

  .mx-xl-n20 {
    margin-right: -1.25rem !important;
    margin-left: -1.25rem !important;
  }

  .mx-xl-n25 {
    margin-right: -1.5625rem !important;
    margin-left: -1.5625rem !important;
  }

  .mx-xl-n30 {
    margin-right: -1.875rem !important;
    margin-left: -1.875rem !important;
  }

  .mx-xl-n40 {
    margin-right: -2.5rem !important;
    margin-left: -2.5rem !important;
  }

  .mx-xl-n50 {
    margin-right: -3.125rem !important;
    margin-left: -3.125rem !important;
  }

  .mx-xl-n70 {
    margin-right: -4.375rem !important;
    margin-left: -4.375rem !important;
  }

  .mx-xl-n80 {
    margin-right: -5rem !important;
    margin-left: -5rem !important;
  }

  .mx-xl-n100 {
    margin-right: -6.25rem !important;
    margin-left: -6.25rem !important;
  }

  .mx-xl-n135 {
    margin-right: -8.4375rem !important;
    margin-left: -8.4375rem !important;
  }

  .mx-xl-n150 {
    margin-right: -9.375rem !important;
    margin-left: -9.375rem !important;
  }

  .my-xl-n5 {
    margin-top: -0.3125rem !important;
    margin-bottom: -0.3125rem !important;
  }

  .my-xl-n10 {
    margin-top: -0.625rem !important;
    margin-bottom: -0.625rem !important;
  }

  .my-xl-n15 {
    margin-top: -0.9375rem !important;
    margin-bottom: -0.9375rem !important;
  }

  .my-xl-n20 {
    margin-top: -1.25rem !important;
    margin-bottom: -1.25rem !important;
  }

  .my-xl-n25 {
    margin-top: -1.5625rem !important;
    margin-bottom: -1.5625rem !important;
  }

  .my-xl-n30 {
    margin-top: -1.875rem !important;
    margin-bottom: -1.875rem !important;
  }

  .my-xl-n40 {
    margin-top: -2.5rem !important;
    margin-bottom: -2.5rem !important;
  }

  .my-xl-n50 {
    margin-top: -3.125rem !important;
    margin-bottom: -3.125rem !important;
  }

  .my-xl-n70 {
    margin-top: -4.375rem !important;
    margin-bottom: -4.375rem !important;
  }

  .my-xl-n80 {
    margin-top: -5rem !important;
    margin-bottom: -5rem !important;
  }

  .my-xl-n100 {
    margin-top: -6.25rem !important;
    margin-bottom: -6.25rem !important;
  }

  .my-xl-n135 {
    margin-top: -8.4375rem !important;
    margin-bottom: -8.4375rem !important;
  }

  .my-xl-n150 {
    margin-top: -9.375rem !important;
    margin-bottom: -9.375rem !important;
  }

  .mt-xl-n5 {
    margin-top: -0.3125rem !important;
  }

  .mt-xl-n10 {
    margin-top: -0.625rem !important;
  }

  .mt-xl-n15 {
    margin-top: -0.9375rem !important;
  }

  .mt-xl-n20 {
    margin-top: -1.25rem !important;
  }

  .mt-xl-n25 {
    margin-top: -1.5625rem !important;
  }

  .mt-xl-n30 {
    margin-top: -1.875rem !important;
  }

  .mt-xl-n40 {
    margin-top: -2.5rem !important;
  }

  .mt-xl-n50 {
    margin-top: -3.125rem !important;
  }

  .mt-xl-n70 {
    margin-top: -4.375rem !important;
  }

  .mt-xl-n80 {
    margin-top: -5rem !important;
  }

  .mt-xl-n100 {
    margin-top: -6.25rem !important;
  }

  .mt-xl-n135 {
    margin-top: -8.4375rem !important;
  }

  .mt-xl-n150 {
    margin-top: -9.375rem !important;
  }

  .me-xl-n5 {
    margin-right: -0.3125rem !important;
  }

  .me-xl-n10 {
    margin-right: -0.625rem !important;
  }

  .me-xl-n15 {
    margin-right: -0.9375rem !important;
  }

  .me-xl-n20 {
    margin-right: -1.25rem !important;
  }

  .me-xl-n25 {
    margin-right: -1.5625rem !important;
  }

  .me-xl-n30 {
    margin-right: -1.875rem !important;
  }

  .me-xl-n40 {
    margin-right: -2.5rem !important;
  }

  .me-xl-n50 {
    margin-right: -3.125rem !important;
  }

  .me-xl-n70 {
    margin-right: -4.375rem !important;
  }

  .me-xl-n80 {
    margin-right: -5rem !important;
  }

  .me-xl-n100 {
    margin-right: -6.25rem !important;
  }

  .me-xl-n135 {
    margin-right: -8.4375rem !important;
  }

  .me-xl-n150 {
    margin-right: -9.375rem !important;
  }

  .mb-xl-n5 {
    margin-bottom: -0.3125rem !important;
  }

  .mb-xl-n10 {
    margin-bottom: -0.625rem !important;
  }

  .mb-xl-n15 {
    margin-bottom: -0.9375rem !important;
  }

  .mb-xl-n20 {
    margin-bottom: -1.25rem !important;
  }

  .mb-xl-n25 {
    margin-bottom: -1.5625rem !important;
  }

  .mb-xl-n30 {
    margin-bottom: -1.875rem !important;
  }

  .mb-xl-n40 {
    margin-bottom: -2.5rem !important;
  }

  .mb-xl-n50 {
    margin-bottom: -3.125rem !important;
  }

  .mb-xl-n70 {
    margin-bottom: -4.375rem !important;
  }

  .mb-xl-n80 {
    margin-bottom: -5rem !important;
  }

  .mb-xl-n100 {
    margin-bottom: -6.25rem !important;
  }

  .mb-xl-n135 {
    margin-bottom: -8.4375rem !important;
  }

  .mb-xl-n150 {
    margin-bottom: -9.375rem !important;
  }

  .ms-xl-n5 {
    margin-left: -0.3125rem !important;
  }

  .ms-xl-n10 {
    margin-left: -0.625rem !important;
  }

  .ms-xl-n15 {
    margin-left: -0.9375rem !important;
  }

  .ms-xl-n20 {
    margin-left: -1.25rem !important;
  }

  .ms-xl-n25 {
    margin-left: -1.5625rem !important;
  }

  .ms-xl-n30 {
    margin-left: -1.875rem !important;
  }

  .ms-xl-n40 {
    margin-left: -2.5rem !important;
  }

  .ms-xl-n50 {
    margin-left: -3.125rem !important;
  }

  .ms-xl-n70 {
    margin-left: -4.375rem !important;
  }

  .ms-xl-n80 {
    margin-left: -5rem !important;
  }

  .ms-xl-n100 {
    margin-left: -6.25rem !important;
  }

  .ms-xl-n135 {
    margin-left: -8.4375rem !important;
  }

  .ms-xl-n150 {
    margin-left: -9.375rem !important;
  }

  .p-xl-0 {
    padding: 0 !important;
  }

  .p-xl-5 {
    padding: 0.3125rem !important;
  }

  .p-xl-10 {
    padding: 0.625rem !important;
  }

  .p-xl-15 {
    padding: 0.9375rem !important;
  }

  .p-xl-20 {
    padding: 1.25rem !important;
  }

  .p-xl-25 {
    padding: 1.5625rem !important;
  }

  .p-xl-30 {
    padding: 1.875rem !important;
  }

  .p-xl-40 {
    padding: 2.5rem !important;
  }

  .p-xl-50 {
    padding: 3.125rem !important;
  }

  .p-xl-70 {
    padding: 4.375rem !important;
  }

  .p-xl-80 {
    padding: 5rem !important;
  }

  .p-xl-100 {
    padding: 6.25rem !important;
  }

  .p-xl-135 {
    padding: 8.4375rem !important;
  }

  .p-xl-150 {
    padding: 9.375rem !important;
  }

  .px-xl-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }

  .px-xl-5 {
    padding-right: 0.3125rem !important;
    padding-left: 0.3125rem !important;
  }

  .px-xl-10 {
    padding-right: 0.625rem !important;
    padding-left: 0.625rem !important;
  }

  .px-xl-15 {
    padding-right: 0.9375rem !important;
    padding-left: 0.9375rem !important;
  }

  .px-xl-20 {
    padding-right: 1.25rem !important;
    padding-left: 1.25rem !important;
  }

  .px-xl-25 {
    padding-right: 1.5625rem !important;
    padding-left: 1.5625rem !important;
  }

  .px-xl-30 {
    padding-right: 1.875rem !important;
    padding-left: 1.875rem !important;
  }

  .px-xl-40 {
    padding-right: 2.5rem !important;
    padding-left: 2.5rem !important;
  }

  .px-xl-50 {
    padding-right: 3.125rem !important;
    padding-left: 3.125rem !important;
  }

  .px-xl-70 {
    padding-right: 4.375rem !important;
    padding-left: 4.375rem !important;
  }

  .px-xl-80 {
    padding-right: 5rem !important;
    padding-left: 5rem !important;
  }

  .px-xl-100 {
    padding-right: 6.25rem !important;
    padding-left: 6.25rem !important;
  }

  .px-xl-135 {
    padding-right: 8.4375rem !important;
    padding-left: 8.4375rem !important;
  }

  .px-xl-150 {
    padding-right: 9.375rem !important;
    padding-left: 9.375rem !important;
  }

  .py-xl-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  .py-xl-5 {
    padding-top: 0.3125rem !important;
    padding-bottom: 0.3125rem !important;
  }

  .py-xl-10 {
    padding-top: 0.625rem !important;
    padding-bottom: 0.625rem !important;
  }

  .py-xl-15 {
    padding-top: 0.9375rem !important;
    padding-bottom: 0.9375rem !important;
  }

  .py-xl-20 {
    padding-top: 1.25rem !important;
    padding-bottom: 1.25rem !important;
  }

  .py-xl-25 {
    padding-top: 1.5625rem !important;
    padding-bottom: 1.5625rem !important;
  }

  .py-xl-30 {
    padding-top: 1.875rem !important;
    padding-bottom: 1.875rem !important;
  }

  .py-xl-40 {
    padding-top: 2.5rem !important;
    padding-bottom: 2.5rem !important;
  }

  .py-xl-50 {
    padding-top: 3.125rem !important;
    padding-bottom: 3.125rem !important;
  }

  .py-xl-70 {
    padding-top: 4.375rem !important;
    padding-bottom: 4.375rem !important;
  }

  .py-xl-80 {
    padding-top: 5rem !important;
    padding-bottom: 5rem !important;
  }

  .py-xl-100 {
    padding-top: 6.25rem !important;
    padding-bottom: 6.25rem !important;
  }

  .py-xl-135 {
    padding-top: 8.4375rem !important;
    padding-bottom: 8.4375rem !important;
  }

  .py-xl-150 {
    padding-top: 9.375rem !important;
    padding-bottom: 9.375rem !important;
  }

  .pt-xl-0 {
    padding-top: 0 !important;
  }

  .pt-xl-5 {
    padding-top: 0.3125rem !important;
  }

  .pt-xl-10 {
    padding-top: 0.625rem !important;
  }

  .pt-xl-15 {
    padding-top: 0.9375rem !important;
  }

  .pt-xl-20 {
    padding-top: 1.25rem !important;
  }

  .pt-xl-25 {
    padding-top: 1.5625rem !important;
  }

  .pt-xl-30 {
    padding-top: 1.875rem !important;
  }

  .pt-xl-40 {
    padding-top: 2.5rem !important;
  }

  .pt-xl-50 {
    padding-top: 3.125rem !important;
  }

  .pt-xl-70 {
    padding-top: 4.375rem !important;
  }

  .pt-xl-80 {
    padding-top: 5rem !important;
  }

  .pt-xl-100 {
    padding-top: 6.25rem !important;
  }

  .pt-xl-135 {
    padding-top: 8.4375rem !important;
  }

  .pt-xl-150 {
    padding-top: 9.375rem !important;
  }

  .pe-xl-0 {
    padding-right: 0 !important;
  }

  .pe-xl-5 {
    padding-right: 0.3125rem !important;
  }

  .pe-xl-10 {
    padding-right: 0.625rem !important;
  }

  .pe-xl-15 {
    padding-right: 0.9375rem !important;
  }

  .pe-xl-20 {
    padding-right: 1.25rem !important;
  }

  .pe-xl-25 {
    padding-right: 1.5625rem !important;
  }

  .pe-xl-30 {
    padding-right: 1.875rem !important;
  }

  .pe-xl-40 {
    padding-right: 2.5rem !important;
  }

  .pe-xl-50 {
    padding-right: 3.125rem !important;
  }

  .pe-xl-70 {
    padding-right: 4.375rem !important;
  }

  .pe-xl-80 {
    padding-right: 5rem !important;
  }

  .pe-xl-100 {
    padding-right: 6.25rem !important;
  }

  .pe-xl-135 {
    padding-right: 8.4375rem !important;
  }

  .pe-xl-150 {
    padding-right: 9.375rem !important;
  }

  .pb-xl-0 {
    padding-bottom: 0 !important;
  }

  .pb-xl-5 {
    padding-bottom: 0.3125rem !important;
  }

  .pb-xl-10 {
    padding-bottom: 0.625rem !important;
  }

  .pb-xl-15 {
    padding-bottom: 0.9375rem !important;
  }

  .pb-xl-20 {
    padding-bottom: 1.25rem !important;
  }

  .pb-xl-25 {
    padding-bottom: 1.5625rem !important;
  }

  .pb-xl-30 {
    padding-bottom: 1.875rem !important;
  }

  .pb-xl-40 {
    padding-bottom: 2.5rem !important;
  }

  .pb-xl-50 {
    padding-bottom: 3.125rem !important;
  }

  .pb-xl-70 {
    padding-bottom: 4.375rem !important;
  }

  .pb-xl-80 {
    padding-bottom: 5rem !important;
  }

  .pb-xl-100 {
    padding-bottom: 6.25rem !important;
  }

  .pb-xl-135 {
    padding-bottom: 8.4375rem !important;
  }

  .pb-xl-150 {
    padding-bottom: 9.375rem !important;
  }

  .ps-xl-0 {
    padding-left: 0 !important;
  }

  .ps-xl-5 {
    padding-left: 0.3125rem !important;
  }

  .ps-xl-10 {
    padding-left: 0.625rem !important;
  }

  .ps-xl-15 {
    padding-left: 0.9375rem !important;
  }

  .ps-xl-20 {
    padding-left: 1.25rem !important;
  }

  .ps-xl-25 {
    padding-left: 1.5625rem !important;
  }

  .ps-xl-30 {
    padding-left: 1.875rem !important;
  }

  .ps-xl-40 {
    padding-left: 2.5rem !important;
  }

  .ps-xl-50 {
    padding-left: 3.125rem !important;
  }

  .ps-xl-70 {
    padding-left: 4.375rem !important;
  }

  .ps-xl-80 {
    padding-left: 5rem !important;
  }

  .ps-xl-100 {
    padding-left: 6.25rem !important;
  }

  .ps-xl-135 {
    padding-left: 8.4375rem !important;
  }

  .ps-xl-150 {
    padding-left: 9.375rem !important;
  }

  .gap-xl-0 {
    gap: 0 !important;
  }

  .gap-xl-5 {
    gap: 0.3125rem !important;
  }

  .gap-xl-10 {
    gap: 0.625rem !important;
  }

  .gap-xl-15 {
    gap: 0.9375rem !important;
  }

  .gap-xl-20 {
    gap: 1.25rem !important;
  }

  .gap-xl-25 {
    gap: 1.5625rem !important;
  }

  .gap-xl-30 {
    gap: 1.875rem !important;
  }

  .gap-xl-40 {
    gap: 2.5rem !important;
  }

  .gap-xl-50 {
    gap: 3.125rem !important;
  }

  .gap-xl-70 {
    gap: 4.375rem !important;
  }

  .gap-xl-80 {
    gap: 5rem !important;
  }

  .gap-xl-100 {
    gap: 6.25rem !important;
  }

  .gap-xl-135 {
    gap: 8.4375rem !important;
  }

  .gap-xl-150 {
    gap: 9.375rem !important;
  }

  .row-gap-xl-0 {
    row-gap: 0 !important;
  }

  .row-gap-xl-5 {
    row-gap: 0.3125rem !important;
  }

  .row-gap-xl-10 {
    row-gap: 0.625rem !important;
  }

  .row-gap-xl-15 {
    row-gap: 0.9375rem !important;
  }

  .row-gap-xl-20 {
    row-gap: 1.25rem !important;
  }

  .row-gap-xl-25 {
    row-gap: 1.5625rem !important;
  }

  .row-gap-xl-30 {
    row-gap: 1.875rem !important;
  }

  .row-gap-xl-40 {
    row-gap: 2.5rem !important;
  }

  .row-gap-xl-50 {
    row-gap: 3.125rem !important;
  }

  .row-gap-xl-70 {
    row-gap: 4.375rem !important;
  }

  .row-gap-xl-80 {
    row-gap: 5rem !important;
  }

  .row-gap-xl-100 {
    row-gap: 6.25rem !important;
  }

  .row-gap-xl-135 {
    row-gap: 8.4375rem !important;
  }

  .row-gap-xl-150 {
    row-gap: 9.375rem !important;
  }

  .column-gap-xl-0 {
    column-gap: 0 !important;
  }

  .column-gap-xl-5 {
    column-gap: 0.3125rem !important;
  }

  .column-gap-xl-10 {
    column-gap: 0.625rem !important;
  }

  .column-gap-xl-15 {
    column-gap: 0.9375rem !important;
  }

  .column-gap-xl-20 {
    column-gap: 1.25rem !important;
  }

  .column-gap-xl-25 {
    column-gap: 1.5625rem !important;
  }

  .column-gap-xl-30 {
    column-gap: 1.875rem !important;
  }

  .column-gap-xl-40 {
    column-gap: 2.5rem !important;
  }

  .column-gap-xl-50 {
    column-gap: 3.125rem !important;
  }

  .column-gap-xl-70 {
    column-gap: 4.375rem !important;
  }

  .column-gap-xl-80 {
    column-gap: 5rem !important;
  }

  .column-gap-xl-100 {
    column-gap: 6.25rem !important;
  }

  .column-gap-xl-135 {
    column-gap: 8.4375rem !important;
  }

  .column-gap-xl-150 {
    column-gap: 9.375rem !important;
  }

  .text-xl-start {
    text-align: left !important;
  }

  .text-xl-end {
    text-align: right !important;
  }

  .text-xl-center {
    text-align: center !important;
  }
}
@media (min-width: 1400px) {
  .float-xxl-start {
    float: left !important;
  }

  .float-xxl-end {
    float: right !important;
  }

  .float-xxl-none {
    float: none !important;
  }

  .object-fit-xxl-contain {
    object-fit: contain !important;
  }

  .object-fit-xxl-cover {
    object-fit: cover !important;
  }

  .object-fit-xxl-fill {
    object-fit: fill !important;
  }

  .object-fit-xxl-scale {
    object-fit: scale-down !important;
  }

  .object-fit-xxl-none {
    object-fit: none !important;
  }

  .d-xxl-inline {
    display: inline !important;
  }

  .d-xxl-inline-block {
    display: inline-block !important;
  }

  .d-xxl-block {
    display: block !important;
  }

  .d-xxl-grid {
    display: grid !important;
  }

  .d-xxl-inline-grid {
    display: inline-grid !important;
  }

  .d-xxl-table {
    display: table !important;
  }

  .d-xxl-table-row {
    display: table-row !important;
  }

  .d-xxl-table-cell {
    display: table-cell !important;
  }

  .d-xxl-flex {
    display: flex !important;
  }

  .d-xxl-inline-flex {
    display: inline-flex !important;
  }

  .d-xxl-none {
    display: none !important;
  }

  .flex-xxl-fill {
    flex: 1 1 auto !important;
  }

  .flex-xxl-row {
    flex-direction: row !important;
  }

  .flex-xxl-column {
    flex-direction: column !important;
  }

  .flex-xxl-row-reverse {
    flex-direction: row-reverse !important;
  }

  .flex-xxl-column-reverse {
    flex-direction: column-reverse !important;
  }

  .flex-xxl-grow-0 {
    flex-grow: 0 !important;
  }

  .flex-xxl-grow-1 {
    flex-grow: 1 !important;
  }

  .flex-xxl-shrink-0 {
    flex-shrink: 0 !important;
  }

  .flex-xxl-shrink-1 {
    flex-shrink: 1 !important;
  }

  .flex-xxl-wrap {
    flex-wrap: wrap !important;
  }

  .flex-xxl-nowrap {
    flex-wrap: nowrap !important;
  }

  .flex-xxl-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }

  .justify-content-xxl-start {
    justify-content: flex-start !important;
  }

  .justify-content-xxl-end {
    justify-content: flex-end !important;
  }

  .justify-content-xxl-center {
    justify-content: center !important;
  }

  .justify-content-xxl-between {
    justify-content: space-between !important;
  }

  .justify-content-xxl-around {
    justify-content: space-around !important;
  }

  .justify-content-xxl-evenly {
    justify-content: space-evenly !important;
  }

  .align-items-xxl-start {
    align-items: flex-start !important;
  }

  .align-items-xxl-end {
    align-items: flex-end !important;
  }

  .align-items-xxl-center {
    align-items: center !important;
  }

  .align-items-xxl-baseline {
    align-items: baseline !important;
  }

  .align-items-xxl-stretch {
    align-items: stretch !important;
  }

  .align-content-xxl-start {
    align-content: flex-start !important;
  }

  .align-content-xxl-end {
    align-content: flex-end !important;
  }

  .align-content-xxl-center {
    align-content: center !important;
  }

  .align-content-xxl-between {
    align-content: space-between !important;
  }

  .align-content-xxl-around {
    align-content: space-around !important;
  }

  .align-content-xxl-stretch {
    align-content: stretch !important;
  }

  .align-self-xxl-auto {
    align-self: auto !important;
  }

  .align-self-xxl-start {
    align-self: flex-start !important;
  }

  .align-self-xxl-end {
    align-self: flex-end !important;
  }

  .align-self-xxl-center {
    align-self: center !important;
  }

  .align-self-xxl-baseline {
    align-self: baseline !important;
  }

  .align-self-xxl-stretch {
    align-self: stretch !important;
  }

  .order-xxl-first {
    order: -1 !important;
  }

  .order-xxl-0 {
    order: 0 !important;
  }

  .order-xxl-1 {
    order: 1 !important;
  }

  .order-xxl-2 {
    order: 2 !important;
  }

  .order-xxl-3 {
    order: 3 !important;
  }

  .order-xxl-4 {
    order: 4 !important;
  }

  .order-xxl-5 {
    order: 5 !important;
  }

  .order-xxl-last {
    order: 6 !important;
  }

  .m-xxl-0 {
    margin: 0 !important;
  }

  .m-xxl-5 {
    margin: 0.3125rem !important;
  }

  .m-xxl-10 {
    margin: 0.625rem !important;
  }

  .m-xxl-15 {
    margin: 0.9375rem !important;
  }

  .m-xxl-20 {
    margin: 1.25rem !important;
  }

  .m-xxl-25 {
    margin: 1.5625rem !important;
  }

  .m-xxl-30 {
    margin: 1.875rem !important;
  }

  .m-xxl-40 {
    margin: 2.5rem !important;
  }

  .m-xxl-50 {
    margin: 3.125rem !important;
  }

  .m-xxl-70 {
    margin: 4.375rem !important;
  }

  .m-xxl-80 {
    margin: 5rem !important;
  }

  .m-xxl-100 {
    margin: 6.25rem !important;
  }

  .m-xxl-135 {
    margin: 8.4375rem !important;
  }

  .m-xxl-150 {
    margin: 9.375rem !important;
  }

  .m-xxl-auto {
    margin: auto !important;
  }

  .mx-xxl-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }

  .mx-xxl-5 {
    margin-right: 0.3125rem !important;
    margin-left: 0.3125rem !important;
  }

  .mx-xxl-10 {
    margin-right: 0.625rem !important;
    margin-left: 0.625rem !important;
  }

  .mx-xxl-15 {
    margin-right: 0.9375rem !important;
    margin-left: 0.9375rem !important;
  }

  .mx-xxl-20 {
    margin-right: 1.25rem !important;
    margin-left: 1.25rem !important;
  }

  .mx-xxl-25 {
    margin-right: 1.5625rem !important;
    margin-left: 1.5625rem !important;
  }

  .mx-xxl-30 {
    margin-right: 1.875rem !important;
    margin-left: 1.875rem !important;
  }

  .mx-xxl-40 {
    margin-right: 2.5rem !important;
    margin-left: 2.5rem !important;
  }

  .mx-xxl-50 {
    margin-right: 3.125rem !important;
    margin-left: 3.125rem !important;
  }

  .mx-xxl-70 {
    margin-right: 4.375rem !important;
    margin-left: 4.375rem !important;
  }

  .mx-xxl-80 {
    margin-right: 5rem !important;
    margin-left: 5rem !important;
  }

  .mx-xxl-100 {
    margin-right: 6.25rem !important;
    margin-left: 6.25rem !important;
  }

  .mx-xxl-135 {
    margin-right: 8.4375rem !important;
    margin-left: 8.4375rem !important;
  }

  .mx-xxl-150 {
    margin-right: 9.375rem !important;
    margin-left: 9.375rem !important;
  }

  .mx-xxl-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }

  .my-xxl-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }

  .my-xxl-5 {
    margin-top: 0.3125rem !important;
    margin-bottom: 0.3125rem !important;
  }

  .my-xxl-10 {
    margin-top: 0.625rem !important;
    margin-bottom: 0.625rem !important;
  }

  .my-xxl-15 {
    margin-top: 0.9375rem !important;
    margin-bottom: 0.9375rem !important;
  }

  .my-xxl-20 {
    margin-top: 1.25rem !important;
    margin-bottom: 1.25rem !important;
  }

  .my-xxl-25 {
    margin-top: 1.5625rem !important;
    margin-bottom: 1.5625rem !important;
  }

  .my-xxl-30 {
    margin-top: 1.875rem !important;
    margin-bottom: 1.875rem !important;
  }

  .my-xxl-40 {
    margin-top: 2.5rem !important;
    margin-bottom: 2.5rem !important;
  }

  .my-xxl-50 {
    margin-top: 3.125rem !important;
    margin-bottom: 3.125rem !important;
  }

  .my-xxl-70 {
    margin-top: 4.375rem !important;
    margin-bottom: 4.375rem !important;
  }

  .my-xxl-80 {
    margin-top: 5rem !important;
    margin-bottom: 5rem !important;
  }

  .my-xxl-100 {
    margin-top: 6.25rem !important;
    margin-bottom: 6.25rem !important;
  }

  .my-xxl-135 {
    margin-top: 8.4375rem !important;
    margin-bottom: 8.4375rem !important;
  }

  .my-xxl-150 {
    margin-top: 9.375rem !important;
    margin-bottom: 9.375rem !important;
  }

  .my-xxl-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }

  .mt-xxl-0 {
    margin-top: 0 !important;
  }

  .mt-xxl-5 {
    margin-top: 0.3125rem !important;
  }

  .mt-xxl-10 {
    margin-top: 0.625rem !important;
  }

  .mt-xxl-15 {
    margin-top: 0.9375rem !important;
  }

  .mt-xxl-20 {
    margin-top: 1.25rem !important;
  }

  .mt-xxl-25 {
    margin-top: 1.5625rem !important;
  }

  .mt-xxl-30 {
    margin-top: 1.875rem !important;
  }

  .mt-xxl-40 {
    margin-top: 2.5rem !important;
  }

  .mt-xxl-50 {
    margin-top: 3.125rem !important;
  }

  .mt-xxl-70 {
    margin-top: 4.375rem !important;
  }

  .mt-xxl-80 {
    margin-top: 5rem !important;
  }

  .mt-xxl-100 {
    margin-top: 6.25rem !important;
  }

  .mt-xxl-135 {
    margin-top: 8.4375rem !important;
  }

  .mt-xxl-150 {
    margin-top: 9.375rem !important;
  }

  .mt-xxl-auto {
    margin-top: auto !important;
  }

  .me-xxl-0 {
    margin-right: 0 !important;
  }

  .me-xxl-5 {
    margin-right: 0.3125rem !important;
  }

  .me-xxl-10 {
    margin-right: 0.625rem !important;
  }

  .me-xxl-15 {
    margin-right: 0.9375rem !important;
  }

  .me-xxl-20 {
    margin-right: 1.25rem !important;
  }

  .me-xxl-25 {
    margin-right: 1.5625rem !important;
  }

  .me-xxl-30 {
    margin-right: 1.875rem !important;
  }

  .me-xxl-40 {
    margin-right: 2.5rem !important;
  }

  .me-xxl-50 {
    margin-right: 3.125rem !important;
  }

  .me-xxl-70 {
    margin-right: 4.375rem !important;
  }

  .me-xxl-80 {
    margin-right: 5rem !important;
  }

  .me-xxl-100 {
    margin-right: 6.25rem !important;
  }

  .me-xxl-135 {
    margin-right: 8.4375rem !important;
  }

  .me-xxl-150 {
    margin-right: 9.375rem !important;
  }

  .me-xxl-auto {
    margin-right: auto !important;
  }

  .mb-xxl-0 {
    margin-bottom: 0 !important;
  }

  .mb-xxl-5 {
    margin-bottom: 0.3125rem !important;
  }

  .mb-xxl-10 {
    margin-bottom: 0.625rem !important;
  }

  .mb-xxl-15 {
    margin-bottom: 0.9375rem !important;
  }

  .mb-xxl-20 {
    margin-bottom: 1.25rem !important;
  }

  .mb-xxl-25 {
    margin-bottom: 1.5625rem !important;
  }

  .mb-xxl-30 {
    margin-bottom: 1.875rem !important;
  }

  .mb-xxl-40 {
    margin-bottom: 2.5rem !important;
  }

  .mb-xxl-50 {
    margin-bottom: 3.125rem !important;
  }

  .mb-xxl-70 {
    margin-bottom: 4.375rem !important;
  }

  .mb-xxl-80 {
    margin-bottom: 5rem !important;
  }

  .mb-xxl-100 {
    margin-bottom: 6.25rem !important;
  }

  .mb-xxl-135 {
    margin-bottom: 8.4375rem !important;
  }

  .mb-xxl-150 {
    margin-bottom: 9.375rem !important;
  }

  .mb-xxl-auto {
    margin-bottom: auto !important;
  }

  .ms-xxl-0 {
    margin-left: 0 !important;
  }

  .ms-xxl-5 {
    margin-left: 0.3125rem !important;
  }

  .ms-xxl-10 {
    margin-left: 0.625rem !important;
  }

  .ms-xxl-15 {
    margin-left: 0.9375rem !important;
  }

  .ms-xxl-20 {
    margin-left: 1.25rem !important;
  }

  .ms-xxl-25 {
    margin-left: 1.5625rem !important;
  }

  .ms-xxl-30 {
    margin-left: 1.875rem !important;
  }

  .ms-xxl-40 {
    margin-left: 2.5rem !important;
  }

  .ms-xxl-50 {
    margin-left: 3.125rem !important;
  }

  .ms-xxl-70 {
    margin-left: 4.375rem !important;
  }

  .ms-xxl-80 {
    margin-left: 5rem !important;
  }

  .ms-xxl-100 {
    margin-left: 6.25rem !important;
  }

  .ms-xxl-135 {
    margin-left: 8.4375rem !important;
  }

  .ms-xxl-150 {
    margin-left: 9.375rem !important;
  }

  .ms-xxl-auto {
    margin-left: auto !important;
  }

  .m-xxl-n5 {
    margin: -0.3125rem !important;
  }

  .m-xxl-n10 {
    margin: -0.625rem !important;
  }

  .m-xxl-n15 {
    margin: -0.9375rem !important;
  }

  .m-xxl-n20 {
    margin: -1.25rem !important;
  }

  .m-xxl-n25 {
    margin: -1.5625rem !important;
  }

  .m-xxl-n30 {
    margin: -1.875rem !important;
  }

  .m-xxl-n40 {
    margin: -2.5rem !important;
  }

  .m-xxl-n50 {
    margin: -3.125rem !important;
  }

  .m-xxl-n70 {
    margin: -4.375rem !important;
  }

  .m-xxl-n80 {
    margin: -5rem !important;
  }

  .m-xxl-n100 {
    margin: -6.25rem !important;
  }

  .m-xxl-n135 {
    margin: -8.4375rem !important;
  }

  .m-xxl-n150 {
    margin: -9.375rem !important;
  }

  .mx-xxl-n5 {
    margin-right: -0.3125rem !important;
    margin-left: -0.3125rem !important;
  }

  .mx-xxl-n10 {
    margin-right: -0.625rem !important;
    margin-left: -0.625rem !important;
  }

  .mx-xxl-n15 {
    margin-right: -0.9375rem !important;
    margin-left: -0.9375rem !important;
  }

  .mx-xxl-n20 {
    margin-right: -1.25rem !important;
    margin-left: -1.25rem !important;
  }

  .mx-xxl-n25 {
    margin-right: -1.5625rem !important;
    margin-left: -1.5625rem !important;
  }

  .mx-xxl-n30 {
    margin-right: -1.875rem !important;
    margin-left: -1.875rem !important;
  }

  .mx-xxl-n40 {
    margin-right: -2.5rem !important;
    margin-left: -2.5rem !important;
  }

  .mx-xxl-n50 {
    margin-right: -3.125rem !important;
    margin-left: -3.125rem !important;
  }

  .mx-xxl-n70 {
    margin-right: -4.375rem !important;
    margin-left: -4.375rem !important;
  }

  .mx-xxl-n80 {
    margin-right: -5rem !important;
    margin-left: -5rem !important;
  }

  .mx-xxl-n100 {
    margin-right: -6.25rem !important;
    margin-left: -6.25rem !important;
  }

  .mx-xxl-n135 {
    margin-right: -8.4375rem !important;
    margin-left: -8.4375rem !important;
  }

  .mx-xxl-n150 {
    margin-right: -9.375rem !important;
    margin-left: -9.375rem !important;
  }

  .my-xxl-n5 {
    margin-top: -0.3125rem !important;
    margin-bottom: -0.3125rem !important;
  }

  .my-xxl-n10 {
    margin-top: -0.625rem !important;
    margin-bottom: -0.625rem !important;
  }

  .my-xxl-n15 {
    margin-top: -0.9375rem !important;
    margin-bottom: -0.9375rem !important;
  }

  .my-xxl-n20 {
    margin-top: -1.25rem !important;
    margin-bottom: -1.25rem !important;
  }

  .my-xxl-n25 {
    margin-top: -1.5625rem !important;
    margin-bottom: -1.5625rem !important;
  }

  .my-xxl-n30 {
    margin-top: -1.875rem !important;
    margin-bottom: -1.875rem !important;
  }

  .my-xxl-n40 {
    margin-top: -2.5rem !important;
    margin-bottom: -2.5rem !important;
  }

  .my-xxl-n50 {
    margin-top: -3.125rem !important;
    margin-bottom: -3.125rem !important;
  }

  .my-xxl-n70 {
    margin-top: -4.375rem !important;
    margin-bottom: -4.375rem !important;
  }

  .my-xxl-n80 {
    margin-top: -5rem !important;
    margin-bottom: -5rem !important;
  }

  .my-xxl-n100 {
    margin-top: -6.25rem !important;
    margin-bottom: -6.25rem !important;
  }

  .my-xxl-n135 {
    margin-top: -8.4375rem !important;
    margin-bottom: -8.4375rem !important;
  }

  .my-xxl-n150 {
    margin-top: -9.375rem !important;
    margin-bottom: -9.375rem !important;
  }

  .mt-xxl-n5 {
    margin-top: -0.3125rem !important;
  }

  .mt-xxl-n10 {
    margin-top: -0.625rem !important;
  }

  .mt-xxl-n15 {
    margin-top: -0.9375rem !important;
  }

  .mt-xxl-n20 {
    margin-top: -1.25rem !important;
  }

  .mt-xxl-n25 {
    margin-top: -1.5625rem !important;
  }

  .mt-xxl-n30 {
    margin-top: -1.875rem !important;
  }

  .mt-xxl-n40 {
    margin-top: -2.5rem !important;
  }

  .mt-xxl-n50 {
    margin-top: -3.125rem !important;
  }

  .mt-xxl-n70 {
    margin-top: -4.375rem !important;
  }

  .mt-xxl-n80 {
    margin-top: -5rem !important;
  }

  .mt-xxl-n100 {
    margin-top: -6.25rem !important;
  }

  .mt-xxl-n135 {
    margin-top: -8.4375rem !important;
  }

  .mt-xxl-n150 {
    margin-top: -9.375rem !important;
  }

  .me-xxl-n5 {
    margin-right: -0.3125rem !important;
  }

  .me-xxl-n10 {
    margin-right: -0.625rem !important;
  }

  .me-xxl-n15 {
    margin-right: -0.9375rem !important;
  }

  .me-xxl-n20 {
    margin-right: -1.25rem !important;
  }

  .me-xxl-n25 {
    margin-right: -1.5625rem !important;
  }

  .me-xxl-n30 {
    margin-right: -1.875rem !important;
  }

  .me-xxl-n40 {
    margin-right: -2.5rem !important;
  }

  .me-xxl-n50 {
    margin-right: -3.125rem !important;
  }

  .me-xxl-n70 {
    margin-right: -4.375rem !important;
  }

  .me-xxl-n80 {
    margin-right: -5rem !important;
  }

  .me-xxl-n100 {
    margin-right: -6.25rem !important;
  }

  .me-xxl-n135 {
    margin-right: -8.4375rem !important;
  }

  .me-xxl-n150 {
    margin-right: -9.375rem !important;
  }

  .mb-xxl-n5 {
    margin-bottom: -0.3125rem !important;
  }

  .mb-xxl-n10 {
    margin-bottom: -0.625rem !important;
  }

  .mb-xxl-n15 {
    margin-bottom: -0.9375rem !important;
  }

  .mb-xxl-n20 {
    margin-bottom: -1.25rem !important;
  }

  .mb-xxl-n25 {
    margin-bottom: -1.5625rem !important;
  }

  .mb-xxl-n30 {
    margin-bottom: -1.875rem !important;
  }

  .mb-xxl-n40 {
    margin-bottom: -2.5rem !important;
  }

  .mb-xxl-n50 {
    margin-bottom: -3.125rem !important;
  }

  .mb-xxl-n70 {
    margin-bottom: -4.375rem !important;
  }

  .mb-xxl-n80 {
    margin-bottom: -5rem !important;
  }

  .mb-xxl-n100 {
    margin-bottom: -6.25rem !important;
  }

  .mb-xxl-n135 {
    margin-bottom: -8.4375rem !important;
  }

  .mb-xxl-n150 {
    margin-bottom: -9.375rem !important;
  }

  .ms-xxl-n5 {
    margin-left: -0.3125rem !important;
  }

  .ms-xxl-n10 {
    margin-left: -0.625rem !important;
  }

  .ms-xxl-n15 {
    margin-left: -0.9375rem !important;
  }

  .ms-xxl-n20 {
    margin-left: -1.25rem !important;
  }

  .ms-xxl-n25 {
    margin-left: -1.5625rem !important;
  }

  .ms-xxl-n30 {
    margin-left: -1.875rem !important;
  }

  .ms-xxl-n40 {
    margin-left: -2.5rem !important;
  }

  .ms-xxl-n50 {
    margin-left: -3.125rem !important;
  }

  .ms-xxl-n70 {
    margin-left: -4.375rem !important;
  }

  .ms-xxl-n80 {
    margin-left: -5rem !important;
  }

  .ms-xxl-n100 {
    margin-left: -6.25rem !important;
  }

  .ms-xxl-n135 {
    margin-left: -8.4375rem !important;
  }

  .ms-xxl-n150 {
    margin-left: -9.375rem !important;
  }

  .p-xxl-0 {
    padding: 0 !important;
  }

  .p-xxl-5 {
    padding: 0.3125rem !important;
  }

  .p-xxl-10 {
    padding: 0.625rem !important;
  }

  .p-xxl-15 {
    padding: 0.9375rem !important;
  }

  .p-xxl-20 {
    padding: 1.25rem !important;
  }

  .p-xxl-25 {
    padding: 1.5625rem !important;
  }

  .p-xxl-30 {
    padding: 1.875rem !important;
  }

  .p-xxl-40 {
    padding: 2.5rem !important;
  }

  .p-xxl-50 {
    padding: 3.125rem !important;
  }

  .p-xxl-70 {
    padding: 4.375rem !important;
  }

  .p-xxl-80 {
    padding: 5rem !important;
  }

  .p-xxl-100 {
    padding: 6.25rem !important;
  }

  .p-xxl-135 {
    padding: 8.4375rem !important;
  }

  .p-xxl-150 {
    padding: 9.375rem !important;
  }

  .px-xxl-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }

  .px-xxl-5 {
    padding-right: 0.3125rem !important;
    padding-left: 0.3125rem !important;
  }

  .px-xxl-10 {
    padding-right: 0.625rem !important;
    padding-left: 0.625rem !important;
  }

  .px-xxl-15 {
    padding-right: 0.9375rem !important;
    padding-left: 0.9375rem !important;
  }

  .px-xxl-20 {
    padding-right: 1.25rem !important;
    padding-left: 1.25rem !important;
  }

  .px-xxl-25 {
    padding-right: 1.5625rem !important;
    padding-left: 1.5625rem !important;
  }

  .px-xxl-30 {
    padding-right: 1.875rem !important;
    padding-left: 1.875rem !important;
  }

  .px-xxl-40 {
    padding-right: 2.5rem !important;
    padding-left: 2.5rem !important;
  }

  .px-xxl-50 {
    padding-right: 3.125rem !important;
    padding-left: 3.125rem !important;
  }

  .px-xxl-70 {
    padding-right: 4.375rem !important;
    padding-left: 4.375rem !important;
  }

  .px-xxl-80 {
    padding-right: 5rem !important;
    padding-left: 5rem !important;
  }

  .px-xxl-100 {
    padding-right: 6.25rem !important;
    padding-left: 6.25rem !important;
  }

  .px-xxl-135 {
    padding-right: 8.4375rem !important;
    padding-left: 8.4375rem !important;
  }

  .px-xxl-150 {
    padding-right: 9.375rem !important;
    padding-left: 9.375rem !important;
  }

  .py-xxl-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  .py-xxl-5 {
    padding-top: 0.3125rem !important;
    padding-bottom: 0.3125rem !important;
  }

  .py-xxl-10 {
    padding-top: 0.625rem !important;
    padding-bottom: 0.625rem !important;
  }

  .py-xxl-15 {
    padding-top: 0.9375rem !important;
    padding-bottom: 0.9375rem !important;
  }

  .py-xxl-20 {
    padding-top: 1.25rem !important;
    padding-bottom: 1.25rem !important;
  }

  .py-xxl-25 {
    padding-top: 1.5625rem !important;
    padding-bottom: 1.5625rem !important;
  }

  .py-xxl-30 {
    padding-top: 1.875rem !important;
    padding-bottom: 1.875rem !important;
  }

  .py-xxl-40 {
    padding-top: 2.5rem !important;
    padding-bottom: 2.5rem !important;
  }

  .py-xxl-50 {
    padding-top: 3.125rem !important;
    padding-bottom: 3.125rem !important;
  }

  .py-xxl-70 {
    padding-top: 4.375rem !important;
    padding-bottom: 4.375rem !important;
  }

  .py-xxl-80 {
    padding-top: 5rem !important;
    padding-bottom: 5rem !important;
  }

  .py-xxl-100 {
    padding-top: 6.25rem !important;
    padding-bottom: 6.25rem !important;
  }

  .py-xxl-135 {
    padding-top: 8.4375rem !important;
    padding-bottom: 8.4375rem !important;
  }

  .py-xxl-150 {
    padding-top: 9.375rem !important;
    padding-bottom: 9.375rem !important;
  }

  .pt-xxl-0 {
    padding-top: 0 !important;
  }

  .pt-xxl-5 {
    padding-top: 0.3125rem !important;
  }

  .pt-xxl-10 {
    padding-top: 0.625rem !important;
  }

  .pt-xxl-15 {
    padding-top: 0.9375rem !important;
  }

  .pt-xxl-20 {
    padding-top: 1.25rem !important;
  }

  .pt-xxl-25 {
    padding-top: 1.5625rem !important;
  }

  .pt-xxl-30 {
    padding-top: 1.875rem !important;
  }

  .pt-xxl-40 {
    padding-top: 2.5rem !important;
  }

  .pt-xxl-50 {
    padding-top: 3.125rem !important;
  }

  .pt-xxl-70 {
    padding-top: 4.375rem !important;
  }

  .pt-xxl-80 {
    padding-top: 5rem !important;
  }

  .pt-xxl-100 {
    padding-top: 6.25rem !important;
  }

  .pt-xxl-135 {
    padding-top: 8.4375rem !important;
  }

  .pt-xxl-150 {
    padding-top: 9.375rem !important;
  }

  .pe-xxl-0 {
    padding-right: 0 !important;
  }

  .pe-xxl-5 {
    padding-right: 0.3125rem !important;
  }

  .pe-xxl-10 {
    padding-right: 0.625rem !important;
  }

  .pe-xxl-15 {
    padding-right: 0.9375rem !important;
  }

  .pe-xxl-20 {
    padding-right: 1.25rem !important;
  }

  .pe-xxl-25 {
    padding-right: 1.5625rem !important;
  }

  .pe-xxl-30 {
    padding-right: 1.875rem !important;
  }

  .pe-xxl-40 {
    padding-right: 2.5rem !important;
  }

  .pe-xxl-50 {
    padding-right: 3.125rem !important;
  }

  .pe-xxl-70 {
    padding-right: 4.375rem !important;
  }

  .pe-xxl-80 {
    padding-right: 5rem !important;
  }

  .pe-xxl-100 {
    padding-right: 6.25rem !important;
  }

  .pe-xxl-135 {
    padding-right: 8.4375rem !important;
  }

  .pe-xxl-150 {
    padding-right: 9.375rem !important;
  }

  .pb-xxl-0 {
    padding-bottom: 0 !important;
  }

  .pb-xxl-5 {
    padding-bottom: 0.3125rem !important;
  }

  .pb-xxl-10 {
    padding-bottom: 0.625rem !important;
  }

  .pb-xxl-15 {
    padding-bottom: 0.9375rem !important;
  }

  .pb-xxl-20 {
    padding-bottom: 1.25rem !important;
  }

  .pb-xxl-25 {
    padding-bottom: 1.5625rem !important;
  }

  .pb-xxl-30 {
    padding-bottom: 1.875rem !important;
  }

  .pb-xxl-40 {
    padding-bottom: 2.5rem !important;
  }

  .pb-xxl-50 {
    padding-bottom: 3.125rem !important;
  }

  .pb-xxl-70 {
    padding-bottom: 4.375rem !important;
  }

  .pb-xxl-80 {
    padding-bottom: 5rem !important;
  }

  .pb-xxl-100 {
    padding-bottom: 6.25rem !important;
  }

  .pb-xxl-135 {
    padding-bottom: 8.4375rem !important;
  }

  .pb-xxl-150 {
    padding-bottom: 9.375rem !important;
  }

  .ps-xxl-0 {
    padding-left: 0 !important;
  }

  .ps-xxl-5 {
    padding-left: 0.3125rem !important;
  }

  .ps-xxl-10 {
    padding-left: 0.625rem !important;
  }

  .ps-xxl-15 {
    padding-left: 0.9375rem !important;
  }

  .ps-xxl-20 {
    padding-left: 1.25rem !important;
  }

  .ps-xxl-25 {
    padding-left: 1.5625rem !important;
  }

  .ps-xxl-30 {
    padding-left: 1.875rem !important;
  }

  .ps-xxl-40 {
    padding-left: 2.5rem !important;
  }

  .ps-xxl-50 {
    padding-left: 3.125rem !important;
  }

  .ps-xxl-70 {
    padding-left: 4.375rem !important;
  }

  .ps-xxl-80 {
    padding-left: 5rem !important;
  }

  .ps-xxl-100 {
    padding-left: 6.25rem !important;
  }

  .ps-xxl-135 {
    padding-left: 8.4375rem !important;
  }

  .ps-xxl-150 {
    padding-left: 9.375rem !important;
  }

  .gap-xxl-0 {
    gap: 0 !important;
  }

  .gap-xxl-5 {
    gap: 0.3125rem !important;
  }

  .gap-xxl-10 {
    gap: 0.625rem !important;
  }

  .gap-xxl-15 {
    gap: 0.9375rem !important;
  }

  .gap-xxl-20 {
    gap: 1.25rem !important;
  }

  .gap-xxl-25 {
    gap: 1.5625rem !important;
  }

  .gap-xxl-30 {
    gap: 1.875rem !important;
  }

  .gap-xxl-40 {
    gap: 2.5rem !important;
  }

  .gap-xxl-50 {
    gap: 3.125rem !important;
  }

  .gap-xxl-70 {
    gap: 4.375rem !important;
  }

  .gap-xxl-80 {
    gap: 5rem !important;
  }

  .gap-xxl-100 {
    gap: 6.25rem !important;
  }

  .gap-xxl-135 {
    gap: 8.4375rem !important;
  }

  .gap-xxl-150 {
    gap: 9.375rem !important;
  }

  .row-gap-xxl-0 {
    row-gap: 0 !important;
  }

  .row-gap-xxl-5 {
    row-gap: 0.3125rem !important;
  }

  .row-gap-xxl-10 {
    row-gap: 0.625rem !important;
  }

  .row-gap-xxl-15 {
    row-gap: 0.9375rem !important;
  }

  .row-gap-xxl-20 {
    row-gap: 1.25rem !important;
  }

  .row-gap-xxl-25 {
    row-gap: 1.5625rem !important;
  }

  .row-gap-xxl-30 {
    row-gap: 1.875rem !important;
  }

  .row-gap-xxl-40 {
    row-gap: 2.5rem !important;
  }

  .row-gap-xxl-50 {
    row-gap: 3.125rem !important;
  }

  .row-gap-xxl-70 {
    row-gap: 4.375rem !important;
  }

  .row-gap-xxl-80 {
    row-gap: 5rem !important;
  }

  .row-gap-xxl-100 {
    row-gap: 6.25rem !important;
  }

  .row-gap-xxl-135 {
    row-gap: 8.4375rem !important;
  }

  .row-gap-xxl-150 {
    row-gap: 9.375rem !important;
  }

  .column-gap-xxl-0 {
    column-gap: 0 !important;
  }

  .column-gap-xxl-5 {
    column-gap: 0.3125rem !important;
  }

  .column-gap-xxl-10 {
    column-gap: 0.625rem !important;
  }

  .column-gap-xxl-15 {
    column-gap: 0.9375rem !important;
  }

  .column-gap-xxl-20 {
    column-gap: 1.25rem !important;
  }

  .column-gap-xxl-25 {
    column-gap: 1.5625rem !important;
  }

  .column-gap-xxl-30 {
    column-gap: 1.875rem !important;
  }

  .column-gap-xxl-40 {
    column-gap: 2.5rem !important;
  }

  .column-gap-xxl-50 {
    column-gap: 3.125rem !important;
  }

  .column-gap-xxl-70 {
    column-gap: 4.375rem !important;
  }

  .column-gap-xxl-80 {
    column-gap: 5rem !important;
  }

  .column-gap-xxl-100 {
    column-gap: 6.25rem !important;
  }

  .column-gap-xxl-135 {
    column-gap: 8.4375rem !important;
  }

  .column-gap-xxl-150 {
    column-gap: 9.375rem !important;
  }

  .text-xxl-start {
    text-align: left !important;
  }

  .text-xxl-end {
    text-align: right !important;
  }

  .text-xxl-center {
    text-align: center !important;
  }
}
@media (min-width: 1200px) {
  .fs-1 {
    font-size: 2.25rem !important;
  }

  .fs-2 {
    font-size: 1.3125rem !important;
  }
}
@media print {
  .d-print-inline {
    display: inline !important;
  }

  .d-print-inline-block {
    display: inline-block !important;
  }

  .d-print-block {
    display: block !important;
  }

  .d-print-grid {
    display: grid !important;
  }

  .d-print-inline-grid {
    display: inline-grid !important;
  }

  .d-print-table {
    display: table !important;
  }

  .d-print-table-row {
    display: table-row !important;
  }

  .d-print-table-cell {
    display: table-cell !important;
  }

  .d-print-flex {
    display: flex !important;
  }

  .d-print-inline-flex {
    display: inline-flex !important;
  }

  .d-print-none {
    display: none !important;
  }
}
* {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
}

dl {
  margin-top: 0;
  margin-bottom: 20px;
}

dt {
  font-weight: normal;
  font-style: italic;
  font-size: 0.85rem;
  margin-bottom: 10px;
  padding-left: 10px;
  line-height: 1.5;
}
dt:before {
  content: "- ";
  position: relative;
  margin-left: -10px;
}

h1,
.h1,
h2,
.h2,
h3,
.h3,
h4,
.h4,
h5,
.h5,
h6,
.h6 {
  margin-bottom: 1.25rem;
  margin-top: 3.125rem;
  font-family: var(--font-palonuevo, "Arial"), Arial, sans-serif;
  line-height: 1.25;
  text-align: center;
}
h1 strong,
.h1 strong,
h2 strong,
.h2 strong,
h3 strong,
.h3 strong,
h4 strong,
.h4 strong,
h5 strong,
.h5 strong,
h6 strong,
.h6 strong {
  color: #415143;
}

h1,
.h1 {
  font-family: var(--font-palonuevo, "Arial"), Arial, sans-serif;
  text-transform: uppercase;
  font-size: 19px;
}
h1 strong,
.h1 strong {
  font-size: inherit;
  font-family: var(--font-libre-franklin), Arial, sans-serif;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 1px;
}
@media (min-width: 576px) {
  h1,
.h1 {
    font-size: 21px;
  }
}
@media (min-width: 768px) {
  h1,
.h1 {
    font-size: 23px;
  }
}
@media (min-width: 992px) {
  h1,
.h1 {
    font-size: 25px;
  }
}
@media (min-width: 1200px) {
  h1,
.h1 {
    font-size: 27px;
  }
}

h2,
.h2 {
  font-family: var(--font-palonuevo, "Arial"), Arial, sans-serif;
  text-transform: uppercase;
  font-size: 16px;
}
h2 strong,
.h2 strong {
  font-size: inherit;
  font-family: var(--font-libre-franklin), Arial, sans-serif;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 1px;
}
@media (min-width: 576px) {
  h2,
.h2 {
    font-size: 18px;
  }
}
@media (min-width: 768px) {
  h2,
.h2 {
    font-size: 20px;
  }
}
@media (min-width: 992px) {
  h2,
.h2 {
    font-size: 22px;
  }
}
@media (min-width: 1200px) {
  h2,
.h2 {
    font-size: 24px;
  }
}

h3,
.h3 {
  font-size: 20px;
  font-family: var(--font-libre-franklin), Arial, sans-serif;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 1px;
}
h3 strong,
.h3 strong {
  font-size: 0.75em;
}
@media (min-width: 576px) {
  h3,
.h3 {
    font-size: 21px;
  }
}
@media (min-width: 768px) {
  h3,
.h3 {
    font-size: 25px;
  }
}
@media (min-width: 992px) {
  h3,
.h3 {
    font-size: 27px;
  }
}
@media (min-width: 1200px) {
  h3,
.h3 {
    font-size: 29px;
  }
}

h4,
.h4 {
  font-size: 18px;
  font-family: var(--font-libre-franklin), Arial, sans-serif;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 1px;
}
h4 strong,
.h4 strong {
  font-size: 0.75em;
}
@media (min-width: 576px) {
  h4,
.h4 {
    font-size: 20px;
  }
}
@media (min-width: 768px) {
  h4,
.h4 {
    font-size: 22px;
  }
}
@media (min-width: 992px) {
  h4,
.h4 {
    font-size: 24px;
  }
}
@media (min-width: 1200px) {
  h4,
.h4 {
    font-size: 26px;
  }
}

h5,
.h5 {
  font-family: var(--font-cormorant, "Arial"), Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-size: 20px;
  line-height: 1.2;
  text-transform: none;
  font-weight: 700;
}
h5 strong,
.h5 strong {
  font-size: 0.8em;
  font-weight: 600;
}
@media (min-width: 576px) {
  h5,
.h5 {
    font-size: 22px;
  }
}
@media (min-width: 768px) {
  h5,
.h5 {
    font-size: 24px;
  }
}
@media (min-width: 992px) {
  h5,
.h5 {
    font-size: 26px;
  }
}
@media (min-width: 1200px) {
  h5,
.h5 {
    font-size: 28px;
  }
}

h6,
.h6 {
  font-family: var(--font-cormorant, "Arial"), Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-size: 18px;
  line-height: 1.2;
  text-transform: none;
  font-weight: 700;
}
h6 strong,
.h6 strong {
  font-size: 0.8em;
  font-weight: 600;
}
@media (min-width: 576px) {
  h6,
.h6 {
    font-size: 20px;
  }
}
@media (min-width: 768px) {
  h6,
.h6 {
    font-size: 22px;
  }
}
@media (min-width: 992px) {
  h6,
.h6 {
    font-size: 24px;
  }
}
@media (min-width: 1200px) {
  h6,
.h6 {
    font-size: 26px;
  }
}

p {
  text-align: justify;
}

small,
.small {
  font-size: 0.75rem;
}

.text-justify {
  text-align: justify !important;
}

b,
strong,
.lead {
  font-weight: 700;
}

.lead {
  font-size: 1.0625rem;
}

mark,
.mark {
  padding: 0;
}

main {
  font-size: 17px;
}
main ul {
  text-align: justify;
}
main ol {
  list-style: decimal;
  text-align: justify;
}

hr,
.wp-block-separator {
  position: relative;
  opacity: 1;
  margin: 25px 0;
  border: none;
  border-top: 1px solid #415143;
  border-bottom: 1px solid #415143;
  padding: 0 !important;
}
hr::before,
.wp-block-separator::before {
  content: "";
  display: block;
  height: 1px;
  background-color: #415143;
  margin: 2px auto;
}
hr.show,
.wp-block-separator.show {
  width: 100%;
}
hr.white,
.wp-block-separator.white {
  border-color: #FFF9E6;
}
hr.white::before,
.wp-block-separator.white::before {
  background-color: #FFF9E6;
}
hr.wide,
hr.is-style-wide,
.wp-block-separator.wide,
.wp-block-separator.is-style-wide {
  width: 100vw;
  left: 50%;
  transform: translateX(-50%);
}
@media (max-width: 767.98px) {
  hr.mobile-wide,
.wp-block-separator.mobile-wide {
    width: 100vw;
    left: 50%;
    transform: translateX(-50%);
  }
}

hr + h1, hr + .h1,
hr + h2, hr + .h2,
hr + h3, hr + .h3,
hr + h4, hr + .h4,
hr + h5, hr + .h5,
hr + h6, hr + .h6,
.wp-block-separator + h1,
.wp-block-separator + .h1,
.wp-block-separator + h2,
.wp-block-separator + .h2,
.wp-block-separator + h3,
.wp-block-separator + .h3,
.wp-block-separator + h4,
.wp-block-separator + .h4,
.wp-block-separator + h5,
.wp-block-separator + .h5,
.wp-block-separator + h6,
.wp-block-separator + .h6 {
  margin-top: 0;
}

@keyframes draw-hr {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}
.blockquote {
  position: relative;
  font-size: 1.5625rem;
  line-height: 1.875rem;
  margin: 0;
}
.blockquote::before, .blockquote::after {
  display: block;
  font-size: 2.1875rem;
  line-height: 2.5rem;
}
.blockquote::before {
  content: "“";
}
.blockquote::after {
  content: "”";
  margin-top: 0.9375rem;
}
@media (min-width: 992px) {
  .blockquote::after {
    text-align: right;
  }
}
.blockquote > *:first-child {
  margin-top: 0 !important;
}
.blockquote > *:last-child {
  margin-bottom: 0 !important;
}

.blockquote-footer {
  margin: 0;
  font-size: 1rem;
  color: currentColor;
}
.blockquote-footer::before {
  display: none;
}

figure {
  margin: unset;
}

.entry-content a {
  text-decoration-style: solid;
  text-decoration-thickness: 1px;
  text-underline-offset: 2px;
}

.loader {
  border-left-color: #415143 !important;
  border-bottom-color: rgba(65, 81, 67, 0.2) !important;
  border-right-color: rgba(65, 81, 67, 0.2) !important;
  border-top-color: rgba(65, 81, 67, 0.2) !important;
}

[class^=icon-] {
  display: inline-block;
  width: 1.25rem;
  height: 1.25rem;
}

[aria-expanded=false] .icon-arrow-down {
  transform: rotate(0deg);
}

[aria-expanded=true] .icon-arrow-down {
  transform: rotate(-180deg);
}

.icon-arrow-down {
  transition: transform 0.3s ease-in-out;
}

.turn-cw-90 {
  transform: rotate(90deg);
}

.turn-ccw-90 {
  transform: rotate(-90deg);
}

.download-badge {
  height: 2.5rem;
  width: auto;
  display: inline-block;
}

.badge-app-store,
.badge-play-store {
  display: inline-block;
}
.badge-app-store svg,
.badge-play-store svg {
  width: auto;
  height: 38px;
}

.icon-wordmark-bb-logo {
  display: block;
  width: 292px;
  height: auto;
}
@media (min-width: 768px) {
  .icon-wordmark-bb-logo {
    width: 402px;
    height: auto;
  }
}

.st-socials {
  margin-bottom: 1rem;
}
.st-socials .st-socials-label {
  color: inherit;
  display: inline-block;
  margin: 0 1rem 0 0;
  line-height: 2rem;
}
.st-socials .st-socials-list {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.st-socials .st-socials-item {
  display: inline-block;
  margin: 0 0.1rem;
  text-align: center;
}
.st-socials .st-socials-link {
  color: inherit;
  display: block;
}
.st-socials .st-socials-link i {
  font-size: 1.0625rem;
  background: #FFF9E6;
  color: inherit;
  display: block;
  line-height: 1.9rem;
  width: 29px;
  height: 29px;
}
.st-socials .st-socials-link:hover, .st-socials .st-socials-link:focus {
  text-decoration: none;
}
.st-socials.layout-icons {
  margin: 0;
}
.st-socials.layout-icons .st-socials-list {
  line-height: 1;
}
.st-socials.layout-icons [class^=icon-] {
  width: 22px;
  height: 22px;
}
.st-socials.layout-icons .st-socials-link {
  font-size: 0;
}

.btn, .wp-block-button__link,
body .editor-styles-wrapper .wp-block-button__link {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
  flex-grow: 0;
  flex-shrink: 0;
  border-width: 2px;
  text-transform: uppercase;
  line-height: 1;
  letter-spacing: 1px;
  font-family: var(--font-palonuevo-bold, "Arial"), Arial, sans-serif;
  text-align: center;
}

.btn[disabled], [disabled].wp-block-button__link,
body .editor-styles-wrapper [disabled].wp-block-button__link {
  background-color: #FFF9E6 !important;
  border-color: #6EA394 !important;
  color: #6EA394 !important;
}

.btn-primary, body .editor-styles-wrapper .wp-block-button.is-style-fill > .wp-block-button__link,
body .wp-block-button.is-style-fill > .wp-block-button__link {
  color: #FFF9E6;
}
.btn-primary:hover, body .editor-styles-wrapper .wp-block-button.is-style-fill > .wp-block-button__link:hover,
body .wp-block-button.is-style-fill > .wp-block-button__link:hover {
  background-color: #FFF9E6;
  border-color: #415143;
  color: #415143;
}
.btn-primary:focus, body .editor-styles-wrapper .wp-block-button.is-style-fill > .wp-block-button__link:focus,
body .wp-block-button.is-style-fill > .wp-block-button__link:focus, .btn-primary.active, body .editor-styles-wrapper .wp-block-button.is-style-fill > .active.wp-block-button__link,
body .wp-block-button.is-style-fill > .active.wp-block-button__link {
  box-shadow: inset 0 0 0 1px #FFF9E6;
}

.btn-red {
  color: #FFF9E6;
}

.btn-outline-primary:hover, body .editor-styles-wrapper .wp-block-button.is-style-outline > .wp-block-button__link:hover,
body .wp-block-button.is-style-outline > .wp-block-button__link:hover, .btn-outline-primary:focus, body .editor-styles-wrapper .wp-block-button.is-style-outline > .wp-block-button__link:focus,
body .wp-block-button.is-style-outline > .wp-block-button__link:focus, .btn-outline-primary.active, body .editor-styles-wrapper .wp-block-button.is-style-outline > .active.wp-block-button__link,
body .wp-block-button.is-style-outline > .active.wp-block-button__link {
  background-color: #415143;
  color: #FFF9E6;
}
.btn-outline-primary:focus, body .editor-styles-wrapper .wp-block-button.is-style-outline > .wp-block-button__link:focus,
body .wp-block-button.is-style-outline > .wp-block-button__link:focus, .btn-outline-primary.active, body .editor-styles-wrapper .wp-block-button.is-style-outline > .active.wp-block-button__link,
body .wp-block-button.is-style-outline > .active.wp-block-button__link {
  box-shadow: inset 0 0 0 1px #FFF9E6;
}

.btn-secondary {
  color: #FFF9E6;
}
.btn-secondary:hover {
  background-color: #FFF9E6;
  color: #EC472E;
}
.btn-secondary:focus, .btn-secondary.active {
  box-shadow: inset 0 0 0 1px #FFF9E6;
}

.btn-outline-secondary:hover, body .editor-styles-wrapper .wp-block-button.is-style-outline-secondary > .wp-block-button__link:hover,
body .wp-block-button.is-style-outline-secondary > .wp-block-button__link:hover, .btn-outline-secondary:focus, body .editor-styles-wrapper .wp-block-button.is-style-outline-secondary > .wp-block-button__link:focus,
body .wp-block-button.is-style-outline-secondary > .wp-block-button__link:focus, .btn-outline-secondary.active, body .editor-styles-wrapper .wp-block-button.is-style-outline-secondary > .active.wp-block-button__link,
body .wp-block-button.is-style-outline-secondary > .active.wp-block-button__link {
  background-color: #EC472E;
  color: #FFF9E6;
}
.btn-outline-secondary:focus, body .editor-styles-wrapper .wp-block-button.is-style-outline-secondary > .wp-block-button__link:focus,
body .wp-block-button.is-style-outline-secondary > .wp-block-button__link:focus {
  box-shadow: inset 0 0 0 1px #FFF9E6;
}

.btn-outline-warm {
  border-color: #FFF9E6;
  color: #FFF9E6;
}
.btn-outline-warm:hover, .btn-outline-warm:focus, .btn-outline-warm.active {
  background-color: #EC472E;
  color: #FFF9E6;
}
.btn-outline-warm.active {
  text-decoration: underline;
}

.btn-narrow {
  max-width: none;
  width: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

.wp-block-button__link,
body .editor-styles-wrapper .wp-block-button__link {
  margin: 0.5rem 0;
}

body .editor-styles-wrapper .wp-block-button.is-style-outline > .wp-block-button__link,
body .wp-block-button.is-style-outline > .wp-block-button__link,
body .wp-block-button.is-style-outline:not(.has-text-color) > .wp-block-button__link {
  color: #415143;
}
body .editor-styles-wrapper .wp-block-button.is-style-outline > .wp-block-button__link:hover, body .editor-styles-wrapper .wp-block-button.is-style-outline > .wp-block-button__link:focus, body .editor-styles-wrapper .wp-block-button.is-style-outline > .wp-block-button__link.active,
body .wp-block-button.is-style-outline > .wp-block-button__link:hover,
body .wp-block-button.is-style-outline > .wp-block-button__link:focus,
body .wp-block-button.is-style-outline > .wp-block-button__link.active,
body .wp-block-button.is-style-outline:not(.has-text-color) > .wp-block-button__link:hover,
body .wp-block-button.is-style-outline:not(.has-text-color) > .wp-block-button__link:focus,
body .wp-block-button.is-style-outline:not(.has-text-color) > .wp-block-button__link.active {
  color: #FFF9E6;
  border-color: #415143;
  background-color: #415143;
}

body .editor-styles-wrapper [data-style=outline-primary] .wp-block-wp-bootstrap-blocks-button {
  border: 1px solid #415143;
}

body .editor-styles-wrapper [data-style=outline-secondary] .wp-block-wp-bootstrap-blocks-button {
  border: 1px solid #28170F;
}

.wp-bootstrap-blocks-button {
  flex-grow: 1;
}
.wp-bootstrap-blocks-button a {
  margin: 0 0 0.5rem;
}
@media (min-width: 992px) {
  .wp-bootstrap-blocks-button {
    margin-right: 0.46875rem !important;
  }
}

.block-featured {
  position: relative;
}
.block-featured .innerblocks p {
  font-weight: 600;
}
.block-featured .featured-content {
  position: relative;
}
.block-featured .featured-image-wrap {
  position: relative;
}
.block-featured .featured-image-wrap .image-cover {
  position: absolute;
  left: 0%;
  top: 0%;
  right: auto;
  bottom: 0%;
  width: 100%;
  height: 100%;
  background-color: var(--base-backround);
}
.block-featured .buttons {
  margin-bottom: 3.125rem;
}
.block-featured .buttons > *:last-child {
  margin-bottom: 0 !important;
}
@media (min-width: 992px) {
  .block-featured .buttons.columns-2 {
    flex-direction: row !important;
    flex-wrap: wrap;
  }
}
@media (min-width: 768px) {
  .block-featured .buttons {
    margin-bottom: 0;
  }
}

.skin-green {
  background: #415143;
  color: #FFF9E6;
}

.skin-red {
  background: #EC472E;
  color: #FFF9E6;
}
.skin-red hr {
  border-color: #FFF9E6;
}
.skin-red hr::before {
  background-color: #FFF9E6;
}

.skin-celadon {
  background: #6EA394;
  color: #415143;
}

.block-cta {
  position: relative;
  margin: 3.125rem 0;
}
.block-cta .innerblocks {
  text-align: center;
}
.block-cta .image {
  width: 180px;
}
@media (min-width: 992px) {
  .block-cta {
    min-height: 776px;
    display: flex;
    align-items: center;
  }
  .block-cta .image {
    width: 387px;
    position: absolute;
    right: 0;
    top: 0;
  }
}

.block-accordions ul,
.block-accordions ol {
  padding: 0;
}
.block-accordions ul {
  list-style: none;
}
.block-accordions ul::after {
  content: "❧";
  display: block;
  width: 100%;
}
.block-accordions ul li::before {
  content: "❧";
  display: block;
  width: 100%;
}
.block-accordions ol {
  counter-reset: cupcake;
  list-style: none;
}
.block-accordions ol li {
  counter-increment: increment;
  margin-bottom: 1rem;
}
.block-accordions ol li::before {
  content: counters(increment, "");
  display: block;
  font-weight: bold;
}

.accordion-item {
  border: none;
  border-bottom: 1px solid #EC472E;
}
.accordion-item:first-child, .accordion-item:last-child {
  border-radius: 0;
}

.accordion-body {
  text-align: justify;
}
.accordion-body .card-body > *:last-child {
  margin-bottom: 0;
}

.accordion-button {
  font-size: inherit;
  align-items: end;
  justify-content: space-between;
  position: relative;
  font-family: var(--font-libre-franklin), Arial, sans-serif;
  text-transform: uppercase;
}
@media (min-width: 992px) {
  .accordion-button {
    position: static;
    justify-content: space-between;
    padding-left: 0rem;
    padding-right: 0rem;
  }
}
.accordion-button::after {
  display: none;
}
.accordion-button .label-wrap {
  max-width: calc(100% - 20px - $spacer);
}
.accordion-button .label-wrap::after {
  content: "";
  display: block;
  position: absolute;
  border-bottom: 1px dotted;
  inset: 0 0 calc($spacer*2) 0;
  z-index: 0;
  max-width: calc(100% - 20px - $spacer);
}
.accordion-button .label-wrap span {
  background-color: #FFF9E6;
  display: inline;
  position: relative;
  z-index: 1;
}
.accordion-button svg {
  transition: transform 0.3s ease-in-out;
  background-color: #FFF9E6;
  width: 20px;
  box-sizing: content-box;
  height: auto;
  position: relative;
  z-index: 1;
  bottom: 3px;
}
.accordion-button[aria-expanded=true] svg {
  transform: rotate(-180deg);
}
.accordion-button:focus {
  box-shadow: none;
}
.accordion-button:not(.collapsed) {
  background-color: unset;
  outline: none;
}

.panel-hero + .container .entry-content > .block-features-carousel:first-child {
  margin-top: -150px;
}

.features-carousel {
  line-height: 1;
  --animationDistance: -300%;
  --animationDuration: 25s;
}
.features-carousel:hover * {
  animation-play-state: paused;
}
.features-carousel .splide,
.features-carousel .ticker-wrapper,
.features-carousel .slick-slider {
  line-height: 1;
}
@media (min-width: 768px) {
  .features-carousel .splide,
.features-carousel .ticker-wrapper,
.features-carousel .slick-slider {
    border-left: 1px solid;
    border-right: 1px solid;
  }
}
.features-carousel .splide__slide {
  padding: 2px 1.25rem;
  border-right: 1px solid;
  line-height: 1;
}
.features-carousel .splide__slide:first-child {
  margin-left: 100%;
}
.features-carousel .splide__slide * {
  vertical-align: middle;
}
.features-carousel .splide__slide > a {
  display: block;
  color: inherit;
  transition: all ease-in-out 0.2s;
}
.features-carousel .splide__slide > a:hover {
  color: #28170F;
}
.features-carousel .feature-icon {
  width: 3rem;
  height: 3rem;
}
.features-carousel .feature-label {
  font-weight: 400;
  font-size: 1.2em;
}
@media (max-width: 575.98px) {
  .features-carousel .carousel-wrapper {
    padding-left: 0;
    padding-right: 0;
  }
}
.features-carousel .ticker-wrapper {
  position: relative;
  white-space: nowrap;
  overflow: hidden;
}
.features-carousel .ticker-wrapper .inner {
  animation-duration: var(--animationDuration);
  animation-timing-function: linear;
}
.features-carousel .ticker-wrapper .inner.moving {
  animation-name: moveticker;
}
.features-carousel .ticker-wrapper .slide {
  display: inline-block;
  padding: 0 20px;
}
@keyframes moveticker {
  0% {
    transform: translateX(0px);
  }
  100% {
    transform: translateX(var(--animationDistance));
  }
}

.wp-block-image .badge {
  position: absolute;
  left: 0;
  top: 0;
  background: rgba(255, 249, 230, 0.8);
  padding: 0.5rem 0.5rem 0.5rem 1rem;
  font-size: 0.85rem;
}

.block-loyalty .heading strong,
.block-loyalty .heading span {
  font-weight: inherit;
  color: #EC472E;
}
.le-club-logo {
  width: 100%;
  max-width: 200px;
}
@media (min-width: 768px) {
  .le-club-logo {
    max-width: 300px;
  }
}

.block-about {
  --bg-image-height: 160px;
  padding-top: calc(var(--bg-image-height) / 2);
}
@media (min-width: 1200px) {
  .block-about {
    --bg-image-height: 190px;
  }
}
.block-about .badge {
  position: absolute;
  top: calc(var(--bg-image-height) / -2);
  left: 0;
  width: 100%;
  height: var(--bg-image-height);
  z-index: 2;
  background-repeat: no-repeat;
  background-position: center top;
  background-size: auto 100%;
}
.block-about .wrap {
  position: relative;
  border: 1px solid #415143;
  padding: 0.625rem;
  padding-bottom: 7.5rem;
  padding-top: calc(var(--bg-image-height) / 2 + 2 * $spacer);
  margin-bottom: 1.25rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
@media (min-width: 768px) {
  .block-about .wrap {
    min-height: 100%;
    padding: 1.875rem;
    padding-top: calc(var(--bg-image-height) / 2 + 2 * $spacer);
    padding-bottom: 7.5rem;
  }
}
.block-about .wrap::after {
  content: "EST 1996";
  position: absolute;
  bottom: 20px;
  left: 0;
  width: 100%;
  font-family: var(--font-libre-franklin), Arial, sans-serif;
  padding-top: 60px;
  text-align: center;
  line-height: 1;
  background: url("/images/monogram-red.svg") no-repeat;
  background-position: center top;
  background-size: auto 40px;
}

.opening-times {
  margin: 0 auto;
  text-align: center !important;
}
@media (min-width: 768px) {
  .opening-times {
    text-align: left !important;
  }
}
@media (min-width: 992px) {
  .opening-times {
    margin: 0;
  }
}
.opening-times h1, .opening-times .h1,
.opening-times h2, .opening-times .h2,
.opening-times h3, .opening-times .h3,
.opening-times h4, .opening-times .h4,
.opening-times h5, .opening-times .h5,
.opening-times h6, .opening-times .h6 {
  text-align: inherit;
}
.opening-times p {
  text-align: inherit;
}
.opening-times ul {
  font-family: var(--font-libre-franklin), Arial, sans-serif;
}
@media (min-width: 768px) {
  .opening-times ul {
    margin-left: 100px;
  }
}
.opening-times li {
  text-align: center !important;
}
@media (min-width: 768px) {
  .opening-times li {
    text-align: left !important;
  }
}
.opening-times .day,
.opening-times .time {
  display: inline-block;
}
.opening-times .day {
  font-weight: 600;
}
.opening-times-group .time {
  padding-left: 0;
}

.master-footer .opening-times {
  margin: 0 auto;
}
@media (min-width: 992px) {
  .master-footer .opening-times {
    margin: 0;
  }
}
.master-footer .opening-times .day,
.master-footer .opening-times .time {
  display: inline-block;
  width: 50%;
}
.master-footer .opening-times .day {
  text-align: right;
  padding-right: 0.3125rem;
}
@media (min-width: 992px) {
  .master-footer .opening-times .day {
    text-align: right;
  }
}
.master-footer .opening-times .time {
  text-align: left;
  padding-left: 0.3125rem;
}

.wp-block-video {
  position: relative;
}
.wp-block-video figcaption {
  font-weight: 600;
  font-size: 1rem;
}
@media (min-width: 992px) {
  .wp-block-video.has-caption figcaption {
    margin: 0;
    white-space: normal;
    text-align: right;
  }
}
@media (min-width: 992px) {
  .wp-block-video.edge-2-edge.has-caption figcaption {
    margin-right: 1.25rem;
  }
}

.wp-block-gallery .wp-block-image,
.wp-block-gallery .wp-block-video,
.wp-block-gallery .innerblocks .wp-block-image,
.wp-block-gallery .innerblocks .wp-block-video {
  margin-bottom: 1.25rem;
}
.wp-block-gallery .wp-block-image > *,
.wp-block-gallery .wp-block-video > *,
.wp-block-gallery .innerblocks .wp-block-image > *,
.wp-block-gallery .innerblocks .wp-block-video > * {
  height: 100% !important;
}
.wp-block-gallery .wp-block-image figcaption,
.wp-block-gallery .wp-block-video figcaption,
.wp-block-gallery .innerblocks .wp-block-image figcaption,
.wp-block-gallery .innerblocks .wp-block-video figcaption {
  display: none;
}
.wp-block-gallery .wp-block-image,
.wp-block-gallery .innerblocks .wp-block-image {
  cursor: pointer;
}
.wp-block-gallery img,
.wp-block-gallery video,
.wp-block-gallery .innerblocks img,
.wp-block-gallery .innerblocks video {
  height: 100%;
  width: 100%;
  object-fit: cover;
  object-position: center center;
}

.zonalevents-calendar-notes {
  margin-top: 1rem;
}
.zonalevents-calendar-notes p {
  text-align: center;
  margin: 0;
}

.block-booking .skin-green,
.block-booking .skin-red,
.block-booking .skin-celadon {
  position: relative;
  padding: 1.875rem 0;
}
.block-booking .skin-green::before,
.block-booking .skin-red::before,
.block-booking .skin-celadon::before {
  content: "";
  display: block;
  width: 100vw;
  position: absolute;
  top: 0;
  left: 50%;
  height: 100%;
  z-index: -1;
  transform: translateX(-50%);
}
.block-booking .skin-green::before {
  background: #415143;
}
.block-booking .skin-red::before {
  background: #EC472E;
}
.block-booking .skin-celadon::before {
  background: #6EA394;
}

h1, .h1, h2, .h2, h3, .h3, h4, .h4, h5, .h5, h6, .h6 {
  text-transform: none;
}
h1 strong, .h1 strong, h2 strong, .h2 strong, h3 strong, .h3 strong, h4 strong, .h4 strong, h5 strong, .h5 strong, h6 strong, .h6 strong {
  text-transform: none;
}

.editor-styles-wrapper .is-root-container,
.editor-styles-wrapper .editor-visual-editor__post-title-wrapper {
  max-width: 1200px;
  margin: 0 auto;
}
.editor-styles-wrapper .wp-block-columns {
  gap: 1em;
}

hr {
  width: auto;
}

/* Apply text justification to blocks */
.has-text-align-justify {
  text-align: justify !important;
}

/*# sourceMappingURL=cms.css.map */

/*
* This stylesheet is loaded into <PERSON><PERSON>nberg editor to mimic app styling for all blocks
* open this folder in terminal and use:
* sass --source-map cms.scss cms.css
*/

@use 'sass:math';

// 1. Include functions first (so you can manipulate colors, SVGs, calc, etc)
@import "../../node_modules/bootstrap/scss/functions";

// 2. Include any default variable overrides here
@import "../../styles/variables.scss";

// 3. Include remainder of required Bootstrap stylesheets (including any separate color mode stylesheets)
@import "../../node_modules/bootstrap/scss/variables";

// 4. Include any default map overrides here

// 5. Include remainder of required parts
@import "../../node_modules/bootstrap/scss/maps";
@import "../../node_modules/bootstrap/scss/mixins";
@import "../../node_modules/bootstrap/scss/root";

// 6. Optionally include any other parts as needed
@import "../../node_modules/bootstrap/scss/utilities";
@import "../../node_modules/bootstrap/scss/reboot";
@import "../../node_modules/bootstrap/scss/type";
@import "../../node_modules/bootstrap/scss/images";
@import "../../node_modules/bootstrap/scss/containers";
@import "../../node_modules/bootstrap/scss/grid";
@import "../../node_modules/bootstrap/scss/helpers";

@import "../../node_modules/bootstrap/scss/nav";
@import "../../node_modules/bootstrap/scss/navbar";
@import "../../node_modules/bootstrap/scss/dropdown";
@import "../../node_modules/bootstrap/scss/buttons";
@import "../../node_modules/bootstrap/scss/forms";
@import "../../node_modules/bootstrap/scss/modal";
@import "../../node_modules/bootstrap/scss/accordion";
@import "../../node_modules/bootstrap/scss/transitions";
@import "../../node_modules/bootstrap/scss/offcanvas";
@import "../../node_modules/bootstrap/scss/close";
@import "../../node_modules/bootstrap/scss/alert";

// 7. Optionally include utilities API last to generate classes based on the Sass map in `_utilities.scss`
@import "../../node_modules/bootstrap/scss/utilities/api";

// 8 Libraries
// 8.1 Wordpress Gutenber styles
// @import "../../styles/libraries/gutenberg.scss";

// 9. Custom Gutenberg Blocks (ACF)
@import "../../styles/common/typography.scss";
@import "../../styles/common/icons.scss";
@import "../../styles/common/socials.scss";
@import "../../styles/common/buttons.scss";

@import "../../styles/blocks/featured-content.scss";
@import "../../styles/blocks/cta.scss";
@import "../../styles/blocks/testimonials.scss";
@import "../../styles/blocks/case-studies.scss";
@import "../../styles/blocks/accordions.scss";
@import "../../styles/blocks/socials.scss";
@import "../../styles/blocks/features-carousel.scss";
@import "../../styles/blocks/features-location.scss";
@import "../../styles/blocks/image.scss";
@import "../../styles/blocks/menus-cta.scss";
@import "../../styles/blocks/loyalty.scss";
@import "../../styles/blocks/about.scss";
@import "../../styles/blocks/opening-times.scss";
@import "../../styles/blocks/video.scss";
@import "../../styles/blocks/gallery.scss";
@import "../../styles/blocks/booking-widget.scss";

// stylelint-disable
h1, .h1, h2, .h2, h3, .h3, h4, .h4, h5, .h5, h6, .h6 {
    text-transform: none;

    strong {
        text-transform: none;
    }
}

// 10. Gutenberg editor hacks
.editor-styles-wrapper {
    .is-root-container,
    .editor-visual-editor__post-title-wrapper {
        max-width: 1200px;
        margin: 0 auto;
    }

    .wp-block-columns {
        gap: 1em;
    }
}

hr {
  width: auto;
}

/* Apply text justification to blocks */
.has-text-align-justify {
    text-align: justify !important;
}

// stylelint-enable
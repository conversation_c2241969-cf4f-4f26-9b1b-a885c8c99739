import Head from 'next/head'
import { GetServerSideProps, GetStaticProps } from 'next'
import { getArchivePage, getCategoryFilters, getMenuItemsByLocation, wpSettings } from '../lib/api'
import parseHtml from "../lib/parser"
import { Col, Container, Row } from 'react-bootstrap'
import Layout from '../components/layout'
import PageHeader from '../components/page-header'
import PostPreview from '../components/post-preview'
import useSWR from 'swr'
import { useEffect, useState } from 'react'
import PostFilters from '../components/post-filters'
import { useRouter } from 'next/router'
import Link from 'next/link'
// import FloatingCTA from '../components/floating-cta'
import CustomPagination from '../components/pagination'

async function fetcher(query = '') {
  let headers = { 'Content-Type': 'application/json' }
  let response = await fetch('/api/wordpress/posts', {
    headers,
    method: 'POST',
    body: JSON.stringify({
      query
    }),
  })

  let json = await response.json()
  return json
}

export default function Index({pageIndex, pathName, postsPage, posts, categoryFilters, heroContent, settings,  preview }) {
  const seo = postsPage ? postsPage?.seo : false
  const bodyClass = postsPage?.pageOptions.cssClass ? ' '+postsPage.pageOptions.cssClass : ''
  const postsPerPage = 3 // settings?.reading.postsPerPage || 9
  const [total, setTotal] = useState(null)

  // console.log(router)
  seo.canonical = null

  function usePosts(pageIndex) {
    let query =
      `
      {
        posts: archivePosts(first: ${postsPerPage}, after: "${(pageIndex-1)*postsPerPage}") {
          pageInfo {
            offsetPagination {
              hasMore
              hasPrevious
              total
            }
          }
          edges {
            node {
              slug
              title
              date
              excerpt
              singlePost {
                expiryDate
              }
              categories {
                nodes {
                  slug
                }
              }
              featuredImage {
                node {
                  sourceUrl(size: MEDIUM_LARGE)
                  mediaDetails {
                    sizes(include: MEDIUM_LARGE) {
                      width
                      height
                    }
                    width
                    height
                  }
                }
              }
            }
          }
        }
      }
      `
    let { data, error, isLoading } = useSWR(query, fetcher)

    console.log(data?.posts.pageInfo.offsetPagination.total, total)
    if( data?.posts.pageInfo.offsetPagination.total !== undefined && total === null ) setTotal(data?.posts.pageInfo.offsetPagination.total)

    return {
      data: pageIndex ? data?.posts.edges : null,
      pageInfo: pageIndex ? data?.posts.pageInfo : null,
      isLoading,
      isError: error
    }
  }

  let { data, pageInfo, isLoading, isError } = usePosts(pageIndex)


  return (
    <Layout preview={preview} seo={seo} bodyClass={`archive blog${bodyClass}`} signup={true}>
      <Head>
        <title>{seo ? postsPage.seo.title : postsPage.title}</title>
      </Head>
      <Container>
        {!postsPage?.pageOptions.hideTitle && <PageHeader title={postsPage.title}></PageHeader>}
        {/* Blog page content */}
        <div className="entry-content">
          {postsPage.content && parseHtml(postsPage.content)}
        </div>
        {/* Category Filters */}
        <PostFilters categoryFilters={categoryFilters} />
        {/* No posts message */}
        {total === 0 && <p className='text-center h3'>There's nothing to see here yet. Please come back later.</p>}
        {/* posts grid */}
        { data && (
          <Row>
            {data.map(({node}) => (
              <Col key={node.slug} md="6" lg="4">
                <PostPreview node={node} />
              </Col>
            ))}
          </Row>
        ) }
        {/* pagination */}
        <CustomPagination
        pageIndex={pageIndex}
        pathname={pathName}
        total={posts?.pageInfo?.offsetPagination?.total}
        postsPerPage={postsPerPage}
        isArchive={true}
        />
      </Container>
      {/* <FloatingCTA postID={postsPage?.id} /> */}
    </Layout>
  )
}

export const getServerSideProps: GetServerSideProps = async (context) => {
  const { resolvedUrl, query } = context
  const pathName = resolvedUrl.split('?')[0] || ''
  const settings = await wpSettings()
  const data = await getArchivePage('archive', settings.reading.postsPerPage)
    const categoryFilters = await getCategoryFilters()
  const headerMenuItems = await getMenuItemsByLocation('PRIMARY_NAVIGATION')
  const footerMenuItems = await getMenuItemsByLocation('FOOTER_NAVIGATION')

  // --- Get ACF Field Groups (metaboxes)
  const heroContent = data.page?.HeroContent.hpEnable ? data.page.HeroContent : null

  return {
    props: {
      pageIndex: query?.page ? Number(query.page) : 1,
      pathName: pathName,
      postsPage: data.page,
      posts: data.posts,
      categoryFilters,
      heroContent,
      settings,
      menus: {
        primary: headerMenuItems,
        footer: footerMenuItems
      }
    }
  }
}

import { Container, Row } from "react-bootstrap"
import Head from "next/head"
import Link from "next/link"
import { wpSettings } from "../lib/api"
import Layout from "../components/layout"
import { GetServerSideProps } from "next"

export default function Custom410(props) {
    const status = '410',
    title = 'GONE!'

    return (
        <article>
            <Head>
            <title>{`${status} - ${title}`}</title>
            </Head>
            <div className="col-12 col-lg-10 offset-lg-1 mt-lg-150">
                <h1 className='page-header text-center mx-0 mb-30 mt-50 mt-lg-0'>{`${status} - ${title}`}</h1>
            </div>
            <div className="entry-content text-center pb-50">
                <p>Sorry, but the page you were looking for is gone.<br></br>Return to <Link href="/">homepage</Link>.</p>
            </div>
        </article>
    )
}

export const getServerSideProps: GetServerSideProps = async (context) => {
    const { res } = context
    const settings = await wpSettings()
    res.statusCode = 410
    return { props: {
        settings
    } }
  }
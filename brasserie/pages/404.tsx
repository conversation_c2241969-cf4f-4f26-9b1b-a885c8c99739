import Layout from "@components/layout"
import PageHeader from "@components/page-header"
import HeroContent from "@components/panels/hero"
import { ACF_HeroContent, getHomePage, getMenuItemsByLocation, getPageBySlug, wpSettings } from "@lib/api"
import parseHtml from "@lib/parser"
import { GetStaticProps } from "next"
import Head from "next/head"
import { Container } from "react-bootstrap"

export default function Custom404(props) {
    const { post, heroContent, settings, menus, preview } = props
    const status = '404'
    const seo = post?.seo || false

    // console.log(props)

    return (
      <>

      <Head>
        <title>{`${status} - ${post.title}`}</title>
      </Head>

      <Layout preview={preview} seo={seo} bodyClass={`page-404`}>
        <>
          {heroContent && <HeroContent hero={heroContent} ctx={post}></HeroContent>}
          <Container>
              <article>

              {!post?.pageOptions.hideTitle && <PageHeader title={`${post.title}`}></PageHeader>}

              <div className="entry-content pb-50">
                  {post.content && parseHtml(post.content)}
              </div>

              </article>
          </Container>
        </>
      </Layout>

      </>
    )
}

export const getStaticProps: GetStaticProps = async ({
  params,
  preview = false,
  previewData,
}) => {
  const settings = await wpSettings()
  const headerMenuItems = await getMenuItemsByLocation('PRIMARY_NAVIGATION')
  const footerMenuItems = await getMenuItemsByLocation('FOOTER_NAVIGATION')

  let slug =  settings.headlessConfig.error404Page?.slug
  let data = await getPageBySlug(slug, preview, previewData)
  if( !data.page ) {
    slug = '/'
    data = await getHomePage()
  }
  // --- Get ACF Field Groups (metaboxes)
  const heroContent = data?.page && data?.page?.HeroContent.hpEnable ? await ACF_HeroContent(slug, preview, previewData, 'page', 'URI') : null

  return {
    props: {
      post: data?.page || null,
      heroContent,
      settings,
      menus: {
        primary: headerMenuItems,
        footer: footerMenuItems
      },
      preview
    },
    revalidate: 60,
  }

}
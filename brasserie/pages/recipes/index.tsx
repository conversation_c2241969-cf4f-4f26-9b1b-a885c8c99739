import Head from 'next/head'
import { GetServerSideProps, GetStaticProps } from 'next'
import { getCategoryFilters, getCategoryPage, getCptSeo, getMenuItemsByLocation, getRecipes, wpSettings } from '../../lib/api'
import parseHtml from "../../lib/parser"
import { Col, Container, Row } from 'react-bootstrap'
import Layout from '../../components/layout'
import PageHeader from '../../components/page-header'
import PostPreview from '../../components/post-preview'
import useSWR from 'swr'
import { useEffect, useState } from 'react'
import PostFilters from '../../components/post-filters'
import { useRouter } from 'next/router'
import Link from 'next/link'
// import FloatingCTA from '../../components/floating-cta'
import CustomPagination from '../../components/pagination'

async function fetcher(query = '') {
  let headers = { 'Content-Type': 'application/json' }
  let response = await fetch('/api/wordpress/posts', {
    headers,
    method: 'POST',
    body: JSON.stringify({
      query
    }),
  })

  let json = await response.json()
  return json
}

export default function Index({category, pageIndex, pathName, cptSeo, posts, categoryFilters, heroContent, settings,  preview }) {
  const title = 'Recipes'
  const bodyClass = ''
  const postsPerPage = settings?.reading.postsPerPage || 9

  // --- SEO fix
  const seoRecipe = cptSeo.contentTypes.recipe
  const seo = {
    canonical: null,
    title: seoRecipe.archive.title,
    metaRobotsNoindex: seoRecipe.archive.metaRobotsIndex,
    metaRobotsNofollow: seoRecipe.archive.metaRobotsFollow,
    opengraphTitle: seoRecipe.archive.title,
    opengraphDescription: seoRecipe.archive.metaDesc,
    opengraphImage: null,
    opengraphUrl: seoRecipe.archive.archiveLink,
    opengraphSiteName: cptSeo.schema.siteName,
    opengraphModifiedTime: null,
    schema: {
      raw: seoRecipe.schema.raw
    }
  }

  function usePosts(pageIndex) {
    let query =
      `
      {
        posts: filteredRecipes(first: ${postsPerPage}, after: "${(pageIndex-1)*postsPerPage}") {
          pageInfo {
            offsetPagination {
              hasMore
              hasPrevious
              total
            }
          }
          edges {
            node {
              slug
              title
              date
              excerpt
              featuredImage {
                node {
                  sourceUrl(size: MEDIUM_LARGE)
                  mediaDetails {
                    sizes(include: MEDIUM_LARGE) {
                      width
                      height
                    }
                    width
                    height
                  }
                }
              }
            }
          }
        }
      }
      `
    let { data, error, isLoading } = useSWR(query, fetcher)
    // console.log(data)

    return {
      data: pageIndex ? data?.posts.edges : null,
      pageInfo: pageIndex ? data?.posts.pageInfo : null,
      isLoading,
      isError: error
    }
  }

  let { data, pageInfo, isLoading, isError } = usePosts(pageIndex)

  return (
    <Layout preview={preview} seo={seo} bodyClass={`archive blog${bodyClass}`} signup={true}>
      <Head>
        <title>{seo ? seo.title : title}</title>
      </Head>
      <Container>
        <PageHeader title={title}></PageHeader>
        {/* Blog page content */}
        <div className="entry-content">

        </div>
        {/* Category Filters */}
        <PostFilters categoryFilters={categoryFilters} />
        {/* posts grid */}
        { data && (
          <Row>
            {data.map(({node}) => (
              <Col key={node.slug} md="6" lg="4">
                <PostPreview node={node} type='recipes' />
              </Col>
            ))}
          </Row>
        ) }
        {/* pagination */}
        <CustomPagination
        pageIndex={pageIndex}
        pathname={pathName}
        total={posts?.pageInfo?.offsetPagination?.total}
        postsPerPage={postsPerPage}
        />
      </Container>
      {/* <FloatingCTA /> */}
    </Layout>
  )
}

export const getServerSideProps: GetServerSideProps = async (context) => {
  const { resolvedUrl, query } = context
  const pathName = resolvedUrl.split('?')[0] || ''
  const category = 'recipes'
  const settings = await wpSettings()
  const data = await getRecipes(settings.reading.postsPerPage)
  const cptSeo = await getCptSeo('recipe')
  const categoryFilters = await getCategoryFilters()
  const headerMenuItems = await getMenuItemsByLocation('PRIMARY_NAVIGATION')
  const footerMenuItems = await getMenuItemsByLocation('FOOTER_NAVIGATION')

  // --- Get ACF Field Groups (metaboxes)
  const heroContent = data.page?.HeroContent.hpEnable ? data.page.HeroContent : null

  return {
    props: {
      category,
      pageIndex: query?.page ? Number(query.page) : 1,
      pathName: pathName,
      cptSeo,
      posts: data.posts,
      categoryFilters,
      heroContent,
      settings,
      menus: {
        primary: headerMenuItems,
        footer: footerMenuItems
      }
    }
  }
}
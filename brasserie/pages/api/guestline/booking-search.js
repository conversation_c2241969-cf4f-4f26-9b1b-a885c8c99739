import {XMLValida<PERSON>, XMLParser} from "fast-xml-parser"

export default async function handler(req, res) {
    const API_URL = "https://pmsws.eu.guestline.net/RLXSoapRouter/rlxsoap.asmx"
    let myHeaders = new Headers()
    myHeaders.append("Content-Type", "text/xml")
    myHeaders.append("Accept-Encoding", "gzip, deflate, br")
    myHeaders.append('X-Requested-With', 'XMLHttpRequest')

    const SessionID = req.body.SessionID,
          CRSRef = req.body.CRSRef

    let raw = `<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">\n  <soap:Body>\n    <pmsbkg_BookingSearch xmlns=\"http://tempuri.org/RLXSOAP19/RLXSOAP19\">\n      <SessionID>${SessionID}</SessionID>\n      <Filters>\n        <CRSRef>${CRSRef}</CRSRef>\n      </Filters>\n        <SearchResults>\n        <Reservations>\n          <Reservation>\n          </Reservation>\n        </Reservations>\n      </SearchResults>\n    </pmsbkg_BookingSearch>\n  </soap:Body>\n</soap:Envelope>`

    let requestOptions = {
        method: 'POST',
        headers: myHeaders,
        body: raw,
        redirect: 'follow'
    }

    // res.status(200).send(req.body)

    fetch(API_URL, requestOptions)
    .then(response => response.text())
    .then(result => {
        // --- validate the response XML
        const valid = XMLValidator.validate(result)
        if( valid === true ) {
            const parser = new XMLParser()
            const json = parser.parse(result)
            const jsonBody = json["soap:Envelope"]["soap:Body"]
            if( jsonBody["pmsbkg_BookingSearchResponse"]["pmsbkg_BookingSearchResult"]["ExceptionCode"] == 0 &&
                jsonBody["pmsbkg_BookingSearchResponse"]["SearchResults"]["Reservations"] ) {
                res.status(200).send(jsonBody["pmsbkg_BookingSearchResponse"]["SearchResults"]["Reservations"])
            }else {
                res.status(404).send({ error: `BookingSearch failed: ${jsonBody["pmsbkg_BookingSearchResponse"]["pmsbkg_BookingSearchResult"]["ExceptionDescription"]}`, data: jsonBody })
            }
        }else {
            res.status(400).send({ error: `XML is invalid becuause of - ${valid.err.msg}` })
        }
    })
    .catch(error => res.status(500).send({ error: 'failed to fetch data' }))

}
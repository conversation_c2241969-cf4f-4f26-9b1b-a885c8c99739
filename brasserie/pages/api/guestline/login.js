import {XMLValidator, XMLParser} from "fast-xml-parser"

export default async function handler(req, res) {
    // const API_URL = "https://cors-anywhere.herokuapp.com/https://pmsws.eu.guestline.net/RLXSoapRouter/rlxsoap.asmx"
    // const API_URL = "https://pmsws.eu.guestline.net/RLXSoapRouter/rlxsoap.asmx", // var
    //       API_PASSWORD = '9muwYb8+UgnIj~lVhzM?ost~S', // var
    //       OperatorCode = 'HWIDORKINGSD', // var
    //       InterfaceID = 812 // constant
    //       SiteID = 'HWIDORKING', // var

    const API_URL = req.body.url, // var
          API_PASSWORD = req.body.pass, // var
          SiteID = req.body.site, // var
          OperatorCode = req.body.operator, // var
          InterfaceID = req.body.interface // constant

    let myHeaders = new Headers()
    myHeaders.append("Content-Type", "text/xml")
    myHeaders.append("Accept-Encoding", "gzip, deflate, br")
    myHeaders.append('X-Requested-With', 'XMLHttpRequest')

    let raw = `<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">\n    <soap:Body>\n        <LogIn xmlns=\"http://tempuri.org/RLXSOAP19/RLXSOAP19\">\n            <SiteID>${SiteID}</SiteID>\n            <InterfaceID>${InterfaceID}</InterfaceID>\n            <OperatorCode>${OperatorCode}</OperatorCode>\n            <Password>${API_PASSWORD}</Password>\n        </LogIn>\n    </soap:Body>\n</soap:Envelope>`

    let requestOptions = {
        method: 'POST',
        headers: myHeaders,
        body: raw,
        redirect: 'follow'
    }

    fetch(API_URL, requestOptions)
    .then(response => response.text())
    .then(result => {
        // --- validate the response XML
        const valid = XMLValidator.validate(result)
        if( valid === true ) {
            const parser = new XMLParser()
            const json = parser.parse(result)
            const jsonBody = json["soap:Envelope"]["soap:Body"]
            if( jsonBody["LogInResponse"]["LogInResult"]["ExceptionCode"] == 0 ){
                res.status(200).send(jsonBody["LogInResponse"])
            }else{
                res.status(404).send({
                    eror: `Login failed: ${jsonBody["LogInResponse"]["LogInResult"]["ExceptionDescription"]}`,
                    data: jsonBody,
                    request: {
                        apiUrl: API_URL,
                        apiPassword: API_PASSWORD,
                        siteId: SiteID,
                        operatorCode: OperatorCode
                    }
                })
            }
        }else {
            res.status(400).send({ error: `XML is invalid becuause of - ${valid.err.msg}` })
        }
    })
    .catch(error => res.status(500).send({ error: 'failed to fetch data' }))

}
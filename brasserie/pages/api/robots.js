import { trimTrailingSlash } from '/lib/utils';
import {wpSettings} from '/lib/api';

export default async function handler(req, res) {
  const data = await wpSettings()
  const robots = data?.acf?.seoRobots?.optRobots || false
  if (
    robots
  ) {
    res.write(`${robots}`);
    res.send();
  } else {
    res.write('User-agent: *');
    res.write('\n');
    res.write('Disallow: /');
    res.send();
  }
}
const handler = (req, res) => {
    if (req.method === "POST") {
        try {
        fetch("https://www.google.com/recaptcha/api/siteverify", {
            method: "POST",
            headers: {
            "Content-Type": "application/x-www-form-urlencoded",
            },
            body: `secret=${process.env.RECAPTCHA_SECRET}&response=${req.body.gRecaptchaToken}`,
        })
            .then((reCaptchaRes) => reCaptchaRes.json())
            .then((reCaptchaRes) => {
            // console.log(
            //     reCaptchaRes,
            //     "Response from Google reCatpcha verification API"
            // );
            if (reCaptchaRes?.score > 0.5) {
                // Save data to the database from here
                res.status(200).json({
                status: "success",
                message: "Google ReCaptcha successfully",
                });
            } else {
                res.status(200).json({
                status: "bot",
                message: "Google ReCaptcha Failure",
                });
            }
            });
        } catch (err) {
        res.status(405).json({
            status: "failure",
            message: "Error verifying ReCaptch<PERSON>",
        });
        }
    } else {
        res.status(405);
        res.end();
    }
};

export default handler;
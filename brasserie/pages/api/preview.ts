import type { NextApiRequest, NextApiResponse } from 'next'
import { getPreviewPost } from '../../lib/api'
import {isHierarchicalPostType, postTypes} from "../../lib/wordpress/_config"

export default async function preview(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const { secret, id, slug, post_type } = req.query

  // Check the secret and next parameters
  // This secret should only be known by this API route
  if (
    !process.env.WORDPRESS_PREVIEW_SECRET ||
    secret !== process.env.WORDPRESS_PREVIEW_SECRET ||
    (!id && !slug)
  ) {
    return res.status(401).json({
      message: 'Invalid token',
      id: id,
      slug: slug,
      secret: secret,
      token: process.env.WORDPRESS_PREVIEW_SECRET
    })
  }

  // Ensure first letter of post type is lowercase to match GraphQL.
  const postType = post_type
  ? post_type.toString().charAt(0).toLowerCase() + post_type.slice(1)
  : ''

  // Check if post type is hierarchical.
  const isHierarchical = isHierarchicalPostType(postType)

  // Fix default ID type for hierarchical posts.
  let idType = 'DATABASE_ID'
  idType = !isHierarchical || 'SLUG' !== idType ? idType : 'URI'
  const qlType = postTypes?.[postType]?.qlType

  let post = await getPreviewPost(
    postType,
    id,
    idType,
    qlType
  )

  // If the post doesn't exist prevent preview mode from being enabled
  if (!post[postType]) {
    return res.status(401).json({ message: 'Post not found' })
  }
  post = post[postType]

  // Enable Preview Mode by setting the cookies
  res.setPreviewData({
    maxAge: 60 * 60,
    post: {
      id: post.databaseId,
      slug: post.slug,
      status: post.status,
      uri: post.uri
    },
  })

  const baseRoute = postTypes?.[postType]?.route ?? ''
  // Redirect to post dynamic route.

  //  return res.status(401).json({
  //   id: id,
  //   slug: slug,
  //   secret: secret,
  //   token: process.env.WORDPRESS_PREVIEW_SECRET,
  //   postType: postType,
  //   isHierarchical: isHierarchical,
  //   idType: idType,
  //   post: post,
  //   baseRoute: baseRoute,
  //   redirect: post.slug && post.uri && post.uri.indexOf('?page_id=') === -1 ? post.uri : `${baseRoute ? `/${baseRoute}` : ''}/${post.databaseId}`
  // })

  const redirectionUrl = post.slug && post.uri && (post.uri.indexOf('?p=') === -1 && post.uri.indexOf('?page_id=') === -1)
                        ? post.uri
                        : `${baseRoute ? `/${baseRoute}` : ''}/${post.databaseId}`

  // Redirect to the path from the fetched post
  // We don't redirect to `req.query.slug` as that might lead to open redirect vulnerabilities
  res.writeHead(307, { Location: redirectionUrl })
  res.end()
}


import crypto from 'crypto'

const IMPACT_CampaignId = process.env.IMPACT_CampaignId || false,
    IMPACT_ActionTrackerId = process.env.IMPACT_ActionTrackerId || false,
    IMPACT_SID = process.env.IMPACT_SID || false,
    IMPACT_TOKEN = process.env.IMPACT_TOKEN || false
export default async function handler(req, res) {
    const API_URL = `https://api.impact.com/Advertisers/${IMPACT_SID}/Conversions`
    let myHeaders = new Headers()
    myHeaders.append("Content-Type", "application/json")
    myHeaders.append("Accept", "application/json")
    myHeaders.append("Accept-Encoding", "gzip, deflate, br")
    myHeaders.append("Authorization", `Basic ${btoa(`${IMPACT_SID}:${IMPACT_TOKEN}`)}`)
    const {bookingReference, emailAddress, im_ref, promocode} = req.body

    if( im_ref && IMPACT_SID && IMPACT_TOKEN && IMPACT_CampaignId && IMPACT_ActionTrackerId ) {
        const impactObj = {
            "CampaignId": IMPACT_CampaignId,
            "ActionTrackerId": IMPACT_ActionTrackerId,
            "EventDate": new Date().toISOString(),
            "ClickId": im_ref,
            "OrderId": bookingReference,
            // get SH1 hash of customer email for CustomerEmail
            "CustomerEmail": crypto.createHash('sha1').update(emailAddress).digest('hex'),
            "CustomerId": bookingReference,
            "OrderPromoCode": ""
        }

        fetch(API_URL, {
            method: 'POST',
            headers: myHeaders,
            body: JSON.stringify(impactObj)
        }).then(response => response.json())
        .then(data => {
            // else, send success response with impactObj and response data
            res.status(200).json({ message: 'Conversion successful', impactObj: impactObj, response: data })
        })
        .catch(error => res.status(500).send({ error: 'failed to fetch data' }))

    }else {
        res.status(200).json({ message: 'Impact credentials not available' })
    }
}
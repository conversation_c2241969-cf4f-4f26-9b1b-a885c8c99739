import { useRouter } from 'next/router'
// import ErrorPage from 'next/error'
import Head from 'next/head'
import { GetStaticPaths, GetStaticProps } from 'next'
import { getMenuItemsByLocation, wpSettings, ACF_HeroContent, ACF_PromoPopup, getLocationBySlug, getAllLocationsWithSlug, getMenusByLocation } from '@lib/api'
import parseHtml from "@lib/parser"
import { Container } from 'react-bootstrap'
import Layout from '@components/layout'
import PageHeader from '@components/page-header'
import HeroContent from '@components/panels/hero'
// import FloatingCTA from '@components/floating-cta'
import PasswordProtected from '@components/password-protected'
import Custom404 from '../404'
import { LocationProvider } from '@components/providers/locationProvider'
import TemplateMenu from '@components/templates/menu'

export default function Page({ post, allMenus, heroContent, promoPopup, settings, menus, preview }) {
  const router = useRouter()
  const seo = post?.seo || false
  const bodyClass = post?.pageOptions.cssClass ? ' '+post.pageOptions.cssClass : ''
  const showSignup = post?.pageOptions.showSignup || false
  const template = post?.template?.templateName || false
  const isMenuTemplate = template.toLowerCase().includes('location menu')
  const templateClass = isMenuTemplate ? 'single-menu' : 'single-location'

  let canonicalUrl = seo.canonical

  // console.log(template)

  // === If broadcasted then replace SEO canonical url
  if( post?.broadcastedUrl ) {
    if( post?.broadcastedUrl.indexOf('https://hwc-cms.wearetesting.co.uk/pubs') != -1 ){
      canonicalUrl = post?.broadcastedUrl.replace('https://hwc-cms.wearetesting.co.uk/pubs', 'https://heartwoodinns.com')
    }
    if( post?.broadcastedUrl.indexOf('https://cms.heartwoodcollection.com/pubs') != -1 ){
      canonicalUrl = post?.broadcastedUrl.replace('https://cms.heartwoodcollection.com/pubs', 'https://heartwoodinns.com')
    }
    if( post?.broadcastedUrl.indexOf('https://hwc-cms.wearetesting.co.uk/news') != -1 ) {
      canonicalUrl = post?.broadcastedUrl.replace('https://hwc-cms.wearetesting.co.uk/news', 'https://heartwoodcollection.com/news')
    }
    if( post?.broadcastedUrl.indexOf('https://cms.heartwoodcollection.com/news') != -1 ) {
      canonicalUrl = post?.broadcastedUrl.replace('https://cms.heartwoodcollection.com/news', 'https://heartwoodcollection.com/news')
    }
    seo.canonical = canonicalUrl
    console.log("Broadcasted url: " + post.broadcastedUrl)
  }

  if( !preview && post?.status === 'draft' ) {
    return <Custom404 />
  }

  if (!router.isFallback && !post?.slug) {
    return <Custom404 />
  }

  // console.log(post)

  return (
    <LocationProvider value={post}>
      <Layout preview={preview} seo={seo} bodyClass={`${templateClass}${bodyClass}`} promoPopup={promoPopup}>
          {router.isFallback ? (
            <h1>Loading page…</h1>
          ) : (
            <>
              <Head>
                <title>{post.seo ? post.seo.title : post.title}</title>
              </Head>
              <PasswordProtected ctx={post}>
                {heroContent && <HeroContent hero={heroContent} ctx={post}></HeroContent>}
                <Container>
                  {isMenuTemplate ?
                    <TemplateMenu post={post} />
                  :
                    <article>
                        {!post?.pageOptions.hideTitle && <PageHeader title={post.title}></PageHeader>}
                        <div className="entry-content pb-50">
                          {post.content && parseHtml(post.content)}
                        </div>
                    </article>
                  }

                </Container>
                {/* {showSignup && <FloatingCTA postID={post?.id} pageOptions={post?.pageOptions} />} */}
              </PasswordProtected>
            </>
          )}
        </Layout>
    </LocationProvider>
  )
}

export const getStaticProps: GetStaticProps = async ({
  params,
  preview = false,
  previewData,
}) => {
  console.log(params?.slug)
  const data = await getLocationBySlug('/brasseries/'+Array.from(params?.slug).join("/"), preview, previewData)
  const allMenus = await getMenusByLocation(data.location?.ancestors?.nodes[0].databaseId)
  const settings = await wpSettings()
  const headerMenuItems = await getMenuItemsByLocation('PRIMARY_NAVIGATION')
  const footerMenuItems = await getMenuItemsByLocation('FOOTER_NAVIGATION')

  // --- Get ACF Field Groups (metaboxes)
  const heroContent = data.location?.HeroContent.hpEnable ? await ACF_HeroContent('/brasseries/'+Array.from(params?.slug).join("/"), preview, previewData, 'location', 'URI') : null
  const promoPopup = data.location?.promoPopup?.promoEnable ? await ACF_PromoPopup('/brasseries/'+Array.from(params?.slug).join("/"), preview, previewData, 'location', 'URI') : null

  if( data.location ) {
    return {
      props: {
        post: data.location,
        allMenus,
        heroContent,
        promoPopup,
        settings,
        menus: {
          primary: headerMenuItems,
          footer: footerMenuItems
        },
        preview
      },
      revalidate: 10,
    }
  }else {
    return {
      notFound: true,
      revalidate: 10,
    }
  }


}

export const getStaticPaths: GetStaticPaths = async () => {
  const allPages = await getAllLocationsWithSlug()
  const newPaths = allPages.edges.map(({ node }) => ({
    params: { slug: node.uri.split("/").filter((str)=>str !== '').slice(1) }
  })) || []

  return {
    paths: newPaths,
    fallback: 'blocking',
  }
}

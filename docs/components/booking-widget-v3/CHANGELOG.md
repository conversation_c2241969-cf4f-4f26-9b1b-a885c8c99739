# Booking Widget v3 — Changelog

## 2026-02-06
- Added derived `restOccasionName/restAreaName/restMenuName` via `useMemo` and removed them from state.
- Simplified chained effects for restrictions/slots to depend on IDs + source arrays.
- Consolidated `goToStep` to a single functional `setState` update.
- Removed redundant form reset helper and folded behavior into `setRestaurantInfo`.
- Memoized react-select option lists to avoid per-render allocations.
- Converted async `setState`/`setBookingData` calls to functional updates.
- Added memoized `isStep1Valid` for step validation.

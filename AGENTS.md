# AGENTS.md

This repo is a Next.js app living in `brasserie/`. Use the notes below when coding or running commands.

## Quick orientation
- App source lives in `brasserie/`.
- Pages are in `brasserie/pages/` (Next.js pages router).
- Shared UI is in `brasserie/components/`.
- Data access and helpers live in `brasserie/lib/`.
- Global styles are SCSS in `brasserie/styles/` and loaded by `brasserie/pages/_app.tsx`.
- The app talks to WordPress via WPGraphQL + REST.

## Build, lint, test (run from `brasserie/`)
- Install dependencies: `npm install`
- Dev server: `npm run dev` (runs `next -p 8000`)
- Dev server with SSL proxy: `npm run devssl`
- Build: `npm run build`
- Start production server: `npm run start` (port 8000)
- Lint: `npm run lint` (Next.js ESLint)
- Test: `npm run test` (currently `next build && next start -p 8000`)

### Single-test guidance
- There is no Jest/Vitest/Cypress/Playwright setup in this repo.
- “Single test” is not supported because no test runner is configured.
- You can lint a single file with Next.js:
  `npm run lint -- --file pages/index.tsx`

## Environment variables
- `WORDPRESS_API_URL` is required (see `brasserie/next.config.js`).
- Other variables appear in codepaths:
  - `WORDPRESS_DOMAIN`
  - `NEXT_PUBLIC_WORDPRESS_REST_API`
  - `NEXT_PUBLIC_RECAPTCHA_KEY`
  - `WORDPRESS_AUTH_REFRESH_TOKEN`
  - `WORDPRESS_PREVIEW_SECRET`
- Do not commit `.env.local` (it exists locally in this repo).

## Code style and conventions
### General formatting
- There is no Prettier or custom ESLint config; preserve the existing style in each file.
- Indentation is typically 2 spaces.
- Semicolons are mixed across files; follow local file usage.
- Prefer single quotes for strings unless the local file uses double quotes.

### Imports
- Keep imports grouped: external packages first, then internal modules, then styles.
- Use existing path aliases from `brasserie/tsconfig.json`:
  - `@components/*` → `brasserie/components/*`
  - `@lib/*` → `brasserie/lib/*`
- When modifying a file, preserve its existing import order and quote style.

### TypeScript and types
- TypeScript is present but `strict` is disabled and `allowJs` is true.
- Many files are plain JS or use `// @ts-nocheck`; avoid adding types that fight existing patterns.
- When adding new TS/TSX, use explicit types for public APIs (props, return types) when helpful.

### Naming
- React components use PascalCase file names and default exports (`components/layout.tsx`).
- Helper utilities use lower camelCase function names (`lib/utils.ts`).
- Keep naming consistent with the area you edit.

### React/Next.js patterns
- Pages live in `brasserie/pages/` and default export a component.
- Global styles are imported once in `brasserie/pages/_app.tsx`.
- Prefer functional components and hooks (no classes).
- Use `next/font` for fonts (see `brasserie/pages/_app.tsx`).
- Keep `pages/api/*` handlers as default exports.

### Data access
- GraphQL calls are centralized in `brasserie/lib/api.ts`.
- Reuse existing helpers (e.g., `fetchAPI`, `sendCF7Form`, `decodeJsonStrings`).
- When adding new WordPress fields, update `lib/api.ts` and keep queries consistent.

### Error handling and logging
- Existing code logs with `console.error` then throws in data fetchers.
- In API routes, return `res.status(...).json(...)` with useful errors.
- Prefer early guards and clear error messages for missing env vars.

### State, effects, and side effects
- Effects in components often attach DOM listeners; always clean up in `useEffect` returns.
- When reading from `window` or `document`, guard for SSR if needed.
- Prefer `useCallback` for handlers passed to child components.

### Styling (SCSS)
- Styling is global SCSS (not CSS Modules).
- Keep new styles in the appropriate folder:
  - `styles/blocks/*` for block-like sections
  - `styles/components/*` for components
  - `styles/pages/*` for page-specific styles
  - `styles/common/*` for shared utilities
- Reuse existing class naming and selectors in the relevant file.

### Assets
- Fonts live in `brasserie/public/fonts/` and are wired through `next/font`.
- Images are constrained by `next.config.js` domains; update that list if adding new hosts.

### Performance and SEO
- Respect existing performance headers and redirects in `brasserie/next.config.js`.
- Avoid adding blocking scripts; prefer async/defer when possible.

## Documentation expectations
- Component changelogs live in `docs/components/<component>/CHANGELOG.md`.
- The overall changelog format is described in `docs/components/README.md`.

## Cursor/Copilot rules
- No `.cursor/rules/`, `.cursorrules`, or `.github/copilot-instructions.md` found in this repo.
